# GitLab CI Validation Script for PowerShell
# Usage: .\scripts\validate-ci.ps1

param(
    [switch]$Verbose = $false
)

# Colors for output
$Colors = @{
    Red = "Red"
    Green = "Green" 
    Yellow = "Yellow"
    Cyan = "Cyan"
    White = "White"
}

function Write-Status {
    param(
        [string]$Status,
        [string]$Message
    )
    
    $timestamp = Get-Date -Format "HH:mm:ss"
    
    switch ($Status) {
        "success" { 
            Write-Host "✅ [$timestamp] $Message" -ForegroundColor $Colors.Green
        }
        "error" { 
            Write-Host "❌ [$timestamp] $Message" -ForegroundColor $Colors.Red
        }
        "warning" { 
            Write-Host "⚠️  [$timestamp] $Message" -ForegroundColor $Colors.Yellow
        }
        "info" { 
            Write-Host "ℹ️  [$timestamp] $Message" -ForegroundColor $Colors.Cyan
        }
        default { 
            Write-Host "[$timestamp] $Message" -ForegroundColor $Colors.White
        }
    }
}

function Test-YamlSyntax {
    param([string]$FilePath)
    
    if (-not (Test-Path $FilePath)) {
        Write-Status "error" "File not found: $FilePath"
        return $false
    }
    
    try {
        # Test YAML syntax using PowerShell-Yaml module or python
        if (Get-Command python -ErrorAction SilentlyContinue) {
            $result = python -c "import yaml; yaml.safe_load(open('$FilePath', encoding='utf-8'))" 2>&1
            if ($LASTEXITCODE -eq 0) {
                Write-Status "success" "YAML syntax valid: $(Split-Path $FilePath -Leaf)"
                return $true
            } else {
                Write-Status "error" "YAML syntax error in $(Split-Path $FilePath -Leaf): $result"
                return $false
            }
        } else {
            Write-Status "warning" "Python not found, skipping YAML syntax validation"
            return $true
        }
    } catch {
        Write-Status "error" "Error validating YAML: $($_.Exception.Message)"
        return $false
    }
}

function Test-GitLabCILint {
    Write-Status "info" "Running GitLab CI lint..."
    
    if (Get-Command glab -ErrorAction SilentlyContinue) {
        try {
            $result = glab ci lint 2>&1
            if ($LASTEXITCODE -eq 0) {
                Write-Status "success" "GitLab CI configuration is valid"
                return $true
            } else {
                Write-Status "error" "GitLab CI lint failed: $result"
                return $false
            }
        } catch {
            Write-Status "error" "Error running glab ci lint: $($_.Exception.Message)"
            return $false
        }
    } else {
        Write-Status "warning" "glab CLI not found, skipping GitLab CI validation"
        Write-Status "info" "Install from: https://github.com/profclems/glab"
        return $true
    }
}

function Test-CommonIssues {
    param([string]$FilePath)
    
    if (-not (Test-Path $FilePath)) {
        return $false
    }
    
    $content = Get-Content $FilePath -Raw
    $hasIssues = $false
    
    # Check for tabs
    if ($content -match "`t") {
        Write-Status "warning" "Found tabs in $(Split-Path $FilePath -Leaf), use spaces instead"
        $hasIssues = $true
    }
    
    # Check for trailing whitespace
    $lines = Get-Content $FilePath
    $trailingWhitespaceLines = @()
    
    for ($i = 0; $i -lt $lines.Count; $i++) {
        if ($lines[$i] -match " +$") {
            $trailingWhitespaceLines += ($i + 1)
        }
    }
    
    if ($trailingWhitespaceLines.Count -gt 0) {
        Write-Status "warning" "Found trailing whitespace in $(Split-Path $FilePath -Leaf) on lines: $($trailingWhitespaceLines -join ', ')"
        $hasIssues = $true
    }
    
    return -not $hasIssues
}

# Main execution
Write-Host "🔍 GitLab CI Validation Script" -ForegroundColor $Colors.Cyan
Write-Host "================================" -ForegroundColor $Colors.Cyan
Write-Host ""

$allValid = $true

# Check main CI file
Write-Status "info" "Checking main GitLab CI file..."
if (-not (Test-Path ".gitlab-ci.yml")) {
    Write-Status "error" ".gitlab-ci.yml not found!"
    exit 1
}

Write-Status "success" "Found .gitlab-ci.yml"

# Validate YAML syntax
if (-not (Test-YamlSyntax ".gitlab-ci.yml")) {
    $allValid = $false
}

# Check included files
if (Test-Path ".gitlab/ci") {
    Write-Status "success" "Found .gitlab/ci directory"
    
    $ciFiles = Get-ChildItem ".gitlab/ci" -Filter "*.yml"
    foreach ($file in $ciFiles) {
        Write-Status "success" "Found $($file.Name)"
        
        if (-not (Test-YamlSyntax $file.FullName)) {
            $allValid = $false
        }
        
        if (-not (Test-CommonIssues $file.FullName)) {
            $allValid = $false
        }
    }
} else {
    Write-Status "warning" ".gitlab/ci directory not found"
}

# Check common issues in main file
Write-Status "info" "Checking for common issues..."
if (-not (Test-CommonIssues ".gitlab-ci.yml")) {
    $allValid = $false
}

# Check package.json
if (Test-Path "package.json") {
    try {
        $packageJson = Get-Content "package.json" | ConvertFrom-Json
        Write-Status "success" "Package: $($packageJson.name) v$($packageJson.version)"
    } catch {
        Write-Status "error" "Invalid package.json: $($_.Exception.Message)"
        $allValid = $false
    }
} else {
    Write-Status "warning" "package.json not found"
}

# Run GitLab CI lint
if (-not (Test-GitLabCILint)) {
    $allValid = $false
}

# Summary
Write-Host ""
Write-Host ("=" * 50)

if ($allValid) {
    Write-Status "success" "All validations passed! 🚀"
    Write-Status "info" "Your GitLab CI configuration looks good to go!"
    
    Write-Host ""
    Write-Host "💡 Next steps:" -ForegroundColor $Colors.Cyan
    Write-Host "   1. Test with: git push origin your-branch"
    Write-Host "   2. Check pipeline at: https://gitlab.com/maxxi-agro/atom/-/pipelines"
    Write-Host "   3. Use GitLab CI Lint: https://gitlab.com/maxxi-agro/atom/-/ci/lint"
} else {
    Write-Status "error" "Some validations failed. Please fix the issues above."
    exit 1
}
