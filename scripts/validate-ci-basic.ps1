# Basic GitLab CI Validation Script for WebStorm
# Usage: powershell -ExecutionPolicy Bypass -File scripts\validate-ci-basic.ps1

Write-Host "🔍 GitLab CI Validation Script" -ForegroundColor Cyan
Write-Host "================================" -ForegroundColor Cyan
Write-Host ""

$timestamp = Get-Date -Format "HH:mm:ss"
$allValid = $true

# Check main CI file
Write-Host "[$timestamp] Checking .gitlab-ci.yml..." -ForegroundColor White

if (Test-Path ".gitlab-ci.yml") {
    Write-Host "✅ Found .gitlab-ci.yml" -ForegroundColor Green
} else {
    Write-Host "❌ .gitlab-ci.yml not found!" -ForegroundColor Red
    exit 1
}

# Check CI directory
Write-Host "[$timestamp] Checking .gitlab/ci directory..." -ForegroundColor White

if (Test-Path ".gitlab/ci") {
    Write-Host "✅ Found .gitlab/ci directory" -ForegroundColor Green

    $ciFiles = Get-ChildItem ".gitlab/ci" -Filter "*.yml"
    foreach ($file in $ciFiles) {
        Write-Host "✅ Found $($file.Name)" -ForegroundColor Green
    }
} else {
    Write-Host "⚠️  .gitlab/ci directory not found" -ForegroundColor Yellow
}

# Basic file content check
Write-Host "[$timestamp] Checking file contents..." -ForegroundColor White

$filesToCheck = @(".gitlab-ci.yml")
if (Test-Path ".gitlab/ci") {
    $ciDirFiles = Get-ChildItem ".gitlab/ci" -Filter "*.yml"
    foreach ($file in $ciDirFiles) {
        $filesToCheck += $file.FullName
    }
}

foreach ($file in $filesToCheck) {
    $fileName = Split-Path $file -Leaf

    try {
        $content = Get-Content $file -Raw -ErrorAction Stop

        # Check for tabs
        if ($content.Contains("`t")) {
            Write-Host "⚠️  Found tabs in $fileName (use spaces)" -ForegroundColor Yellow
        }

        # Check basic YAML structure
        if ($content.Contains("stages:") -or $content.Contains("include:")) {
            Write-Host "✅ $fileName appears to be valid YAML" -ForegroundColor Green
        } else {
            Write-Host "⚠️  $fileName might have issues" -ForegroundColor Yellow
        }

    } catch {
        Write-Host "❌ Error reading $fileName" -ForegroundColor Red
        $allValid = $false
    }
}

# Check package.json
Write-Host "[$timestamp] Checking package.json..." -ForegroundColor White

if (Test-Path "package.json") {
    try {
        $packageContent = Get-Content "package.json" -Raw
        $packageJson = $packageContent | ConvertFrom-Json
        Write-Host "✅ Package: $($packageJson.name) v$($packageJson.version)" -ForegroundColor Green
    } catch {
        Write-Host "❌ Invalid package.json" -ForegroundColor Red
        $allValid = $false
    }
} else {
    Write-Host "⚠️  package.json not found" -ForegroundColor Yellow
}

# Check for GitLab CLI
Write-Host "[$timestamp] Checking GitLab CLI..." -ForegroundColor White

$glabExists = $false
try {
    $null = Get-Command glab -ErrorAction Stop
    $glabExists = $true
    Write-Host "✅ GitLab CLI (glab) found" -ForegroundColor Green
} catch {
    Write-Host "⚠️  GitLab CLI (glab) not found" -ForegroundColor Yellow
    Write-Host "   Install from: https://github.com/profclems/glab" -ForegroundColor Gray
}

if ($glabExists) {
    Write-Host "[$timestamp] Running GitLab CI lint..." -ForegroundColor White

    try {
        $lintResult = & glab ci lint 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ GitLab CI configuration is valid" -ForegroundColor Green
        } else {
            Write-Host "❌ GitLab CI lint failed" -ForegroundColor Red
            Write-Host "$lintResult" -ForegroundColor Red
            $allValid = $false
        }
    } catch {
        Write-Host "❌ Error running GitLab CI lint" -ForegroundColor Red
        $allValid = $false
    }
}

# Summary
Write-Host ""
Write-Host "=" * 50
$finalTime = Get-Date -Format "HH:mm:ss"

if ($allValid) {
    Write-Host "✅ [$finalTime] Validation completed successfully!" -ForegroundColor Green
    Write-Host ""
    Write-Host "Next steps:" -ForegroundColor Cyan
    Write-Host "1. Push to feature branch for testing" -ForegroundColor White
    Write-Host "2. Check pipeline: https://gitlab.com/maxxi-agro/atom/-/pipelines" -ForegroundColor White
    Write-Host "3. Use GitLab CI Lint: https://gitlab.com/maxxi-agro/atom/-/ci/lint" -ForegroundColor White
} else {
    Write-Host "❌ [$finalTime] Some validations failed" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "Validation completed." -ForegroundColor Gray
