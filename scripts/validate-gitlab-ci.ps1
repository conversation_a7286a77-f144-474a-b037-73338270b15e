Write-Host "GitLab CI Validation" -ForegroundColor Cyan
Write-Host "===================" -ForegroundColor Cyan

$valid = $true

# Check main file
if (Test-Path ".gitlab-ci.yml") {
    Write-Host "✅ .gitlab-ci.yml found" -ForegroundColor Green
} else {
    Write-Host "❌ .gitlab-ci.yml missing" -ForegroundColor Red
    $valid = $false
}

# Check CI directory
if (Test-Path ".gitlab/ci") {
    Write-Host "✅ .gitlab/ci directory found" -ForegroundColor Green
    $files = Get-ChildItem ".gitlab/ci" -Filter "*.yml"
    foreach ($file in $files) {
        Write-Host "  - $($file.Name)" -ForegroundColor White
    }
} else {
    Write-Host "⚠️  .gitlab/ci directory not found" -ForegroundColor Yellow
}

# Check package.json
if (Test-Path "package.json") {
    try {
        $pkg = Get-Content "package.json" | ConvertFrom-Json
        Write-Host "✅ package.json: $($pkg.name) v$($pkg.version)" -ForegroundColor Green
    } catch {
        Write-Host "❌ package.json invalid" -ForegroundColor Red
        $valid = $false
    }
}

# Check GitLab CLI
try {
    $null = Get-Command glab -ErrorAction Stop
    Write-Host "✅ GitLab CLI available" -ForegroundColor Green
    
    $result = glab ci lint 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ GitLab CI lint passed" -ForegroundColor Green
    } else {
        Write-Host "❌ GitLab CI lint failed" -ForegroundColor Red
        $valid = $false
    }
} catch {
    Write-Host "⚠️  GitLab CLI not found" -ForegroundColor Yellow
}

Write-Host ""
if ($valid) {
    Write-Host "✅ Validation passed!" -ForegroundColor Green
} else {
    Write-Host "❌ Validation failed!" -ForegroundColor Red
    exit 1
}
