#!/bin/bash

# GitLab CI Validation Script
# Usage: ./scripts/validate-ci.sh

set -e

echo "🔍 GitLab CI Validation Script"
echo "================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local status=$1
    local message=$2
    case $status in
        "success") echo -e "${GREEN}✅ $message${NC}" ;;
        "warning") echo -e "${YELLOW}⚠️  $message${NC}" ;;
        "error") echo -e "${RED}❌ $message${NC}" ;;
        "info") echo -e "ℹ️  $message" ;;
    esac
}

# Check if files exist
print_status "info" "Checking CI files..."

if [ ! -f ".gitlab-ci.yml" ]; then
    print_status "error" ".gitlab-ci.yml not found!"
    exit 1
fi

print_status "success" "Found .gitlab-ci.yml"

# Check included files
if [ -d ".gitlab/ci" ]; then
    print_status "success" "Found .gitlab/ci directory"
    for file in .gitlab/ci/*.yml; do
        if [ -f "$file" ]; then
            print_status "success" "Found $(basename $file)"
        fi
    done
else
    print_status "warning" ".gitlab/ci directory not found"
fi

# YAML syntax validation
print_status "info" "Validating YAML syntax..."

if command -v yamllint &> /dev/null; then
    if yamllint .gitlab-ci.yml; then
        print_status "success" "Main .gitlab-ci.yml syntax is valid"
    else
        print_status "error" "YAML syntax errors in .gitlab-ci.yml"
        exit 1
    fi
    
    if [ -d ".gitlab/ci" ]; then
        for file in .gitlab/ci/*.yml; do
            if [ -f "$file" ]; then
                if yamllint "$file"; then
                    print_status "success" "$(basename $file) syntax is valid"
                else
                    print_status "error" "YAML syntax errors in $file"
                    exit 1
                fi
            fi
        done
    fi
else
    print_status "warning" "yamllint not installed, skipping YAML syntax check"
    print_status "info" "Install with: pip install yamllint"
fi

# GitLab CI specific validation
print_status "info" "Validating GitLab CI configuration..."

if command -v glab &> /dev/null; then
    if glab ci lint; then
        print_status "success" "GitLab CI configuration is valid"
    else
        print_status "error" "GitLab CI configuration has errors"
        exit 1
    fi
else
    print_status "warning" "glab CLI not installed, skipping GitLab CI validation"
    print_status "info" "Install from: https://github.com/profclems/glab"
fi

# Check for common issues
print_status "info" "Checking for common issues..."

# Check for tabs (should use spaces)
if grep -P '\t' .gitlab-ci.yml .gitlab/ci/*.yml 2>/dev/null; then
    print_status "error" "Found tabs in YAML files, use spaces instead"
    exit 1
else
    print_status "success" "No tabs found in YAML files"
fi

# Check for trailing whitespace
if grep -E ' +$' .gitlab-ci.yml .gitlab/ci/*.yml 2>/dev/null; then
    print_status "warning" "Found trailing whitespace in YAML files"
else
    print_status "success" "No trailing whitespace found"
fi

# Check for required stages
if grep -q "stages:" .gitlab-ci.yml; then
    print_status "success" "Stages defined in .gitlab-ci.yml"
else
    print_status "warning" "No stages defined in .gitlab-ci.yml"
fi

# Check for include statements
if grep -q "include:" .gitlab-ci.yml; then
    print_status "success" "Include statements found"
    
    # Verify included files exist
    included_files=$(grep -A 10 "include:" .gitlab-ci.yml | grep "local:" | sed "s/.*local: *'//" | sed "s/'.*//")
    for file in $included_files; do
        if [ -f "$file" ]; then
            print_status "success" "Included file exists: $file"
        else
            print_status "error" "Included file missing: $file"
            exit 1
        fi
    done
fi

# Summary
print_status "success" "All validations passed!"
print_status "info" "Your GitLab CI configuration looks good to go! 🚀"

echo ""
echo "💡 Next steps:"
echo "   1. Test with: git push origin your-branch"
echo "   2. Check pipeline at: https://gitlab.com/maxxi-agro/atom/-/pipelines"
echo "   3. Use GitLab CI Lint: https://gitlab.com/maxxi-agro/atom/-/ci/lint"
