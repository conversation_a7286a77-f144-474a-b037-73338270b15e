@echo off
REM GitLab CI Validation Script Wrapper for Windows
REM Usage: scripts\validate-ci.bat

echo 🔍 GitLab CI Validation Script
echo ================================

REM Check if PowerShell is available
powershell -Command "Get-Command powershell" >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ PowerShell not found!
    exit /b 1
)

REM Run PowerShell script
powershell -ExecutionPolicy Bypass -File "%~dp0validate-ci.ps1"

REM Check exit code
if %errorlevel% neq 0 (
    echo.
    echo ❌ Validation failed with exit code %errorlevel%
    exit /b %errorlevel%
)

echo.
echo ✅ Validation completed successfully!
pause
