# Simple GitLab CI Validation Script for PowerShell
# Usage: .\scripts\validate-ci-simple.ps1

Write-Host "🔍 GitLab CI Validation Script" -ForegroundColor Cyan
Write-Host "================================" -ForegroundColor Cyan
Write-Host ""

$allValid = $true
$timestamp = Get-Date -Format "HH:mm:ss"

# Check main CI file
Write-Host "ℹ️  [$timestamp] Checking main GitLab CI file..." -ForegroundColor Cyan

if (-not (Test-Path ".gitlab-ci.yml")) {
    Write-Host "❌ [$timestamp] .gitlab-ci.yml not found!" -ForegroundColor Red
    exit 1
}

Write-Host "✅ [$timestamp] Found .gitlab-ci.yml" -ForegroundColor Green

# Check included files
if (Test-Path ".gitlab/ci") {
    Write-Host "✅ [$timestamp] Found .gitlab/ci directory" -ForegroundColor Green
    
    $ciFiles = Get-ChildItem ".gitlab/ci" -Filter "*.yml"
    foreach ($file in $ciFiles) {
        Write-Host "✅ [$timestamp] Found $($file.Name)" -ForegroundColor Green
    }
} else {
    Write-Host "⚠️  [$timestamp] .gitlab/ci directory not found" -ForegroundColor Yellow
}

# Basic YAML syntax check using PowerShell
Write-Host "ℹ️  [$timestamp] Checking YAML syntax..." -ForegroundColor Cyan

$yamlFiles = @(".gitlab-ci.yml")
if (Test-Path ".gitlab/ci") {
    $ciDirFiles = Get-ChildItem ".gitlab/ci" -Filter "*.yml" | ForEach-Object { $_.FullName }
    $yamlFiles += $ciDirFiles
}

foreach ($yamlFile in $yamlFiles) {
    try {
        # Basic check - try to read file and check for obvious issues
        $content = Get-Content $yamlFile -Raw
        
        # Check for tabs
        if ($content -match "`t") {
            Write-Host "⚠️  [$timestamp] Found tabs in $(Split-Path $yamlFile -Leaf), use spaces instead" -ForegroundColor Yellow
        }
        
        # Check for trailing whitespace
        $lines = Get-Content $yamlFile
        $trailingLines = @()
        for ($i = 0; $i -lt $lines.Count; $i++) {
            if ($lines[$i] -match " +$") {
                $trailingLines += ($i + 1)
            }
        }
        
        if ($trailingLines.Count -gt 0) {
            Write-Host "⚠️  [$timestamp] Trailing whitespace in $(Split-Path $yamlFile -Leaf) on lines: $($trailingLines -join ', ')" -ForegroundColor Yellow
        }
        
        Write-Host "✅ [$timestamp] Basic syntax check passed: $(Split-Path $yamlFile -Leaf)" -ForegroundColor Green
        
    } catch {
        Write-Host "❌ [$timestamp] Error reading $(Split-Path $yamlFile -Leaf): $($_.Exception.Message)" -ForegroundColor Red
        $allValid = $false
    }
}

# Check package.json
Write-Host "ℹ️  [$timestamp] Checking package.json..." -ForegroundColor Cyan

if (Test-Path "package.json") {
    try {
        $packageJson = Get-Content "package.json" | ConvertFrom-Json
        Write-Host "✅ [$timestamp] Package: $($packageJson.name) v$($packageJson.version)" -ForegroundColor Green
    } catch {
        Write-Host "❌ [$timestamp] Invalid package.json: $($_.Exception.Message)" -ForegroundColor Red
        $allValid = $false
    }
} else {
    Write-Host "⚠️  [$timestamp] package.json not found" -ForegroundColor Yellow
}

# Check for GitLab CLI
Write-Host "ℹ️  [$timestamp] Checking for GitLab CLI..." -ForegroundColor Cyan

if (Get-Command glab -ErrorAction SilentlyContinue) {
    Write-Host "✅ [$timestamp] GitLab CLI (glab) found" -ForegroundColor Green
    
    try {
        Write-Host "ℹ️  [$timestamp] Running GitLab CI lint..." -ForegroundColor Cyan
        $result = glab ci lint 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ [$timestamp] GitLab CI configuration is valid" -ForegroundColor Green
        } else {
            Write-Host "❌ [$timestamp] GitLab CI lint failed: $result" -ForegroundColor Red
            $allValid = $false
        }
    } catch {
        Write-Host "❌ [$timestamp] Error running glab ci lint: $($_.Exception.Message)" -ForegroundColor Red
        $allValid = $false
    }
} else {
    Write-Host "⚠️  [$timestamp] glab CLI not found, skipping GitLab CI validation" -ForegroundColor Yellow
    Write-Host "ℹ️  [$timestamp] Install from: https://github.com/profclems/glab" -ForegroundColor Cyan
}

# Summary
Write-Host ""
Write-Host ("=" * 50)

$finalTimestamp = Get-Date -Format "HH:mm:ss"

if ($allValid) {
    Write-Host "✅ [$finalTimestamp] All validations passed! 🚀" -ForegroundColor Green
    Write-Host "ℹ️  [$finalTimestamp] Your GitLab CI configuration looks good to go!" -ForegroundColor Cyan
    
    Write-Host ""
    Write-Host "💡 Next steps:" -ForegroundColor Cyan
    Write-Host "   1. Test with: git push origin your-branch"
    Write-Host "   2. Check pipeline at: https://gitlab.com/maxxi-agro/atom/-/pipelines"
    Write-Host "   3. Use GitLab CI Lint: https://gitlab.com/maxxi-agro/atom/-/ci/lint"
} else {
    Write-Host "❌ [$finalTimestamp] Some validations failed. Please fix the issues above." -ForegroundColor Red
    exit 1
}
