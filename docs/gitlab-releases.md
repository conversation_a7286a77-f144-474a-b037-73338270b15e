# GitLab Releases Integration

This document explains how GitLab Releases are integrated with our existing branch sync workflow.

## Overview

GitLab Releases are automatically created when tags are pushed to the `main` branch, providing a centralized location for tracking all releases with detailed information and environment links.

## How It Works

### 1. Existing Workflow (Unchanged)
```bash
# 1. Create release branch
git checkout -b release/v1.2.0

# 2. Run npm release (creates version, tag, and changelog)
npm run release

# 3. Push branch with tags
git push origin release/v1.2.0 --follow-tags

# 4. Merge to development
git checkout development
git merge release/v1.2.0
git push origin development

# 5. Automated sync: development → development-second → staging → main
# 6. Delete release branch
git branch -d release/v1.2.0
git push origin --delete release/v1.2.0
```

### 2. GitLab Release Creation (New)
When the sync reaches `main` branch with tags, the pipeline automatically:
- Extracts version from git tag
- Reads changelog from `CHANGELOG.md` (or generates from commits)
- Creates GitLab Release with:
  - Release notes
  - Environment links
  - Package information
  - Commit details

## Release Information

Each GitLab Release includes:

### 📋 Release Notes
- Changelog from `CHANGELOG.md` for the specific version
- Fallback to git commit messages if changelog not found

### 🌐 Environment Links
- **Production**: https://back-office.maxxiagri.com
- **Staging**: https://staging.back-office.maxxiagri.com
- **Development**: https://backoffice-atomdev.vercel.app
- **Development-2**: https://backoffice-atomdev2.vercel.app

### 📦 Package Information
- Package name and version from `package.json`
- Release tag and commit details
- Pipeline information

### 📚 Documentation Links
- Full changelog file
- Compare changes between versions
- Project repository

## Configuration

### GitLab CI Files
- **Main**: `.gitlab-ci.yml` (includes releases pipeline)
- **Releases**: `.gitlab/ci/releases.yml` (release creation logic)
- **Branch Sync**: `.gitlab/ci/branch-sync.yml` (updated to push tags)

### Required Variables
No additional GitLab variables are required. The pipeline uses existing `CI_PUSH_TOKEN`.

### Optional Variables (for notifications)
- `SLACK_WEBHOOK_URL`: For Slack notifications (optional)
- `DISCORD_WEBHOOK_URL`: For Discord notifications (optional)

## Pipeline Stages

The release pipeline runs in the `sync-to-production` stage:

1. **create-gitlab-release**: Creates the GitLab Release
2. **notify-release**: Sends optional notifications

## Rules and Triggers

GitLab Releases are created when:
- Branch is `main`
- Commit has a git tag
- Pipeline runs successfully

## Benefits

### 1. Centralized Release Tracking
- All releases in one place: https://gitlab.com/maxxi-agro/atom/-/releases
- Easy navigation between versions
- Clear release history

### 2. Enhanced Visibility
- Team can see what's deployed where
- Direct links to all environments
- Changelog for each release

### 3. No Workflow Changes
- Existing release process remains the same
- No additional manual steps required
- Automatic creation after sync completes

### 4. Rich Information
- Package details from `package.json`
- Commit information and author
- Pipeline links for traceability

## Troubleshooting

### Release Not Created
Check if:
- Tag exists on `main` branch
- Pipeline completed successfully
- `CHANGELOG.md` format is correct

### Missing Changelog
If changelog is empty:
- Check `CHANGELOG.md` format
- Ensure version section exists: `## [1.2.0] - 2024-01-15`
- Pipeline will fallback to git commits if needed

### Environment Links Not Working
Verify URLs in `.gitlab/ci/releases.yml`:
- Production: `https://back-office.maxxiagri.com`
- Staging: `https://staging.back-office.maxxiagri.com`
- Development: `https://backoffice-atomdev.vercel.app`
- Development-2: `https://backoffice-atomdev2.vercel.app`

## Future Enhancements

Potential improvements:
- Automated deployment status checks
- Integration with monitoring systems
- Release approval workflows
- Automated rollback capabilities

## Example Release

Visit: https://gitlab.com/maxxi-agro/atom/-/releases to see created releases.

Each release will show:
- Version and release date
- What's new in this version
- Links to all environments
- Technical details (commit, pipeline, etc.)
- Assets and documentation links
