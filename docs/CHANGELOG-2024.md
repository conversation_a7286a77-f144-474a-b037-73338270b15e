# Backoffice Changelog - 2024

## [1.9.0](///compare/v1.8.1...v1.9.0) (2024-11-11)


### Features

* **4592:** Detail program marketing - handle section input revision note edcc41b
* **4592:** Detail program marketing - handle section input revision note bf7c19a
* **MTMAI-4797:** card header purchase order component b9137dd
* **MTMAI-4801:** list product order component 1dcbc3a
* **MTMAI-4804, MTMAI-4805:** change response detail purchase order fe637c0
* **MTMAI-4806:** detail program marketing component 811eaee
* **MTMAI-4834:** create spm enhance page - new endpoint ab4ca1d
* **MTMAI-4834:** create spm enhance page - new endpoint 88ea069
* **MTMAI-4834:** create spm enhance page - new endpoint a8e5966
* **MTMAI-5109, MTMAI-5110:** slicing integration detail spm with new response f6bc125
* **MTMAI-5184:** pengajuan registrasi - per<PERSON>han data update rejection enum endpoint 389d535
* **MTMAI-5191, MTMAI-5192:** enhance form user e8a4cf9
* **MTMAI-5196:** enhance & refactor detail user ed9f8fb
* **MTMAI-5198, MTMAI-5224:** enhance user list & add log component aa753a5
* **MTMAI-5226, MTMAI-5259:** input select scope - switch input by role 0e33c70
* **MTMAI-5227,MTMAI-5230:** handle get load list to input select scope component 8473d05
* **MTMAI-5229:** validate regional director 9f8090d
* **MTMAI-5236:** Post create area - add RH/RD payload 3405c90
* **MTMAI-5240:** Subarea create payload post 980034c
* **MTMAI-5245:** enhance detail area section b7f8b8a
* **MTMAI-5248:** Area Team page ffa19c0
* **MTMAI-5249:** Area detail team page integrate filter list b58c9f9
* **MTMAI-5249:** Team page integrate get area team list d9f3424
* **MTMAI-5251:** add team input select dialog c1f8d2a
* **MTMAI-5255:** modal add team - switching input base on selected role 71fe6ab
* **MTMAI-5256:** slicing & integrate deactive user teregistrasi 4069e87
* **MTMAI-5256:** slicing & integrate deactive user teregistrasi 43f6aab
* **MTMAI-5257:** integration deactive user c97318d
* **MTMAI-5257:** integration deactive user 95606a3
* **MTMAI-5260:** integrate form multiple input replacement b5ad9c9
* **MTMAI-5260:** modal unassign - switching input replacement list 1599a39
* **MTMAI-5260:** payload post replacement user 1e0f16f
* **MTMAI-5262:** slicing & integrate change area user in detail user 0b98597
* **MTMAI-5263:** integration unassign and modal response 96a9387
* **MTMAI-5266, MTMAI-5197:** integration reactive & detail user fd5b87e
* **MTMAI-5269,MTMAI-5274:** filter list retailer area - subarea, detail section enhancement f02e218
* **MTMAI-5273:** Retailer detail page enhance section card add map view (aktif) 73185ac
* **MTMAI-5276:** approve/reject retailer registration - edit data; enhance modal 2baaa5d
* **MTMAI-5276:** detail retailer pengajuan perubahan data; add gmap component 3306b31
* post payload add team 3369cfb


### Bug Fixes

* area detail - add team, payload for FA role c0ea34d
* area unassign team - payload with no replacement 4ea7806
* breadcrumb anggota tim a3d556b
* change payload ubah cakupan user 16b854c
* clear value if change regency in user form c852544
* clear value if change regency in user form 1130adb
* colour label status user 8f6242d
* disable option input select material 56e8dd1
* disable option regional director 037f3e3
* edit cakupan user 51a9fd7
* fix status from subscribe 25a4cff
* handle show action deactivate user ade87fa
* input select scope 7831663
* loading detail user field assistant 374dc60
* **MTMAI-5231, MTMAI-5232:** slicing integration assign user f81e010
* **MTMAI-5231,MTMAI-5232:** add missing updates e732268
* **MTMAI-5260:** modal response unassign user pengganti text 74e4638
* **MTMAI-5260:** modal unassign invalid state button submit ed3d7c8
* **MTMAI-5260:** noteview assignment info modal response state 0a934cf
* **MTMAI-5260:** unassign payload key 0394f28
* **MTMAI-5265:** slicing reactive user b79be2b
* **MTMAI-5451:** Create spm product order init form map negative value to 0, calculate qty_less 4b29d91
* **MTMAI-5505:** create spm - disabled state input counter product order 864a43c
* **MTMAI-5505:** create spm check available stock - submit button state 58e0428
* **MTMAI-5509:** form update user 0ee9fe0
* **MTMAI-5509:** payload validate email ff66a3e
* **MTMAI-5528:** filter area user list 1d76b06
* **MTMAI-5532:** checkbox beberapa pengganti user 0940d08
* **MTMAI-5534:** note view confirmation non active with roles 5689522
* **MTMAI-5535:** note view confirmation b1cf262
* **MTMAI-5540:** note view assign e95b2a3
* **MTMAI-5541:** note view reset password in form user 72a56fc
* **MTMAI-5541:** noteview reset in form user only if status active a5f800b
* **MTMAI-5543:** view only area accountant 9ca61bc
* **MTMAI-5546, MTMAI-5548, MTMAI-5549:** Unassign team - handle data role admin_scales, admin_gudang c39a845
* **MTMAI-5548:** unassign admin timbangan - send replacement_list payload 48c66bc
* **MTMAI-5555, MTMAI-5554:** form user & form ubah cakupan 6b0cad0
* **MTMAI-5557:** fix sub area FA open toogle 339c97a
* **MTMAI-5561:** payload unassigned for ME abfd348
* **MTMAI-5570:** area  detail unassign modal - conditional view render 68c616f
* **MTMAI-5570:** render role string - modal response unassign 6a0b777
* **MTMAI-5589:** filter open if filter status in user list 1899a42
* **MTMAI-5608:** set disabled select area on create subarea from detail 7e0e985
* **MTMAI-5609:** unassign team accountant - set as optional d17bb53
* **MTMAI-5610:** add loading for get list replacement cb20ca1
* **MTMAI-5613:** add subarea form - send area_id payload edbd8d4
* **MTMAI-5622:** render new data retailer section informasi usaha 222ea99
* **MTMAI-5622:** retailer venture data check new value a8a6664
* **MTMAI-5624:** add area team - fetch options data userlist 00bf6a2
* no selection user FA 0699b19
* noteview info unassign - static text duplicated f804aad
* noteview ubah cakupan for role admin gudang 78a62d3
* noteview ubah cakupan for role admin gudang 2ca5fef
* payload form ubah cakupan for admin gudang e82d959
* payload form ubah cakupan for marketing executive 2709339
* **Program Marketing:** Submit promag diajukan c5caca5
* reset password user 0856606
* status filter 8d49bd0
* Subarea form update payload post b7c5089
* typo text ce90327
* unassign marketing executive c069433
* update pasal kul point 5,6 9f66528
* validate btn input select scope for admin gudang 0017bc3
* validate deactive ME fdd7ee5

## [1.8.1](///compare/v1.8.0...v1.8.1) (2024-10-07)


### Bug Fixes

* **Create SPM:** handle submit button disable state on so without promag 202b278
* **Create SPM:** payload post order qty, product bonus qty when available stock is less 09a68ac
* detail spm condition default scan info noteview a04026d
* Detail spm list product scan noteview info 9722290
* **MTMAI-5165:** unfinished product modal duplicated eebb779
* **MTMAI-5330, MTMAI-5311:** fix table detail spm & dropdown material search 4a5d0cf
* **MTMAI-5330:** form program marketing select product reward 1e1d3e4
* **MTMAI-5330:** select variant variant 51116be
* **MTMAI-5372:** fix save modal confirmation detail spm 081e3cf
* **MTMAI-5373:** redirect detail so 4577a2f
* **MTMAI-5373:** reject so tag f358e2a
* **MTMAI-5376:** create spm submit button disable state - multiple product order 8100bc6
* **MTMAI-5423:** handle product order load - submit button state d6a98dc
* **SPM LIST:** filter list; add missing status enum 79c719c

## [1.8.0](///compare/v1.7.3...v1.8.0) (2024-09-23)


### Features

* **4592:** Detail program marketing - handle section input revision note 5eae959
* change endpoint scan unscan for spm 555573c
* enhance mai tab group a276869
* input value price summary v1 cdf03f3
* loader price sumary 8c0386c
* **MTMAI-4183:** register new endpoint  po - create so a666ceb
* **MTMAI-4568:** modal confirmation form program marketing d42189d
* **MTMAI-4569:** form informasi program 7746a0d
* **MTMAI-4570:** program term form program marketing e504f51
* **MTMAI-4571:** ketentuan pembelian form 32f1e1a
* **MTMAI-4572:** change enum form program marketing d158680
* **MTMAI-4572:** payload form create 372da34
* **MTMAI-4573:** revision note view component 7da8713
* **MTMAI-4574:** brand group component 8130cae
* **MTMAI-4584:** add filter list program by status - period 49d5c0c
* **MTMAI-4584:** Program marketing list page; integrate table data endpoint 99aa096
* **MTMAI-4587:** slicing new menu sales and marketing 2ed990a
* **MTMAI-4588:** enable render menu by role-privilege e6d8668
* **MTMAI-4595:** enhance ringkasan order purchase order b102e21
* **MTMAI-4595:** slicing UI promag for purchase order detail 6b8c413
* **MTMAI-4596:** enhance response api program marketing 805cf5c
* **MTMAI-4596:** integration program marketing in detail purchase order cb84c9d
* **MTMAI-4721,MTMAI-4722:** Detail Program Marketing; view tab list PO 111f76f
* **MTMAI-4761:** disable form section bb9fb78
* **MTMAI-4761:** edit value program marketing e71a92c
* **MTMAI-4761:** payload update form program marketing 450f135
* **MTMAI-4797:** card header purchase order component b649330
* **MTMAI-4798, MTMAI-4799:** slicing component informasi kadaluarsa and order information b2acb69
* **MTMAI-4800:** program marketing list component 726b623
* **MTMAI-4801:** list product order component 5d7d3d2
* **MTMAI-4802:** product program marketing component ba5a59f
* **MTMAI-4803:** price summary card component 07bd5a9
* **MTMAI-4804, MTMAI-4805:** change response detail purchase order 9f3caff
* **MTMAI-4806:** detail program marketing component 83f8574
* **MTMAI-4814:** create so init component set dataValue product promag f06c5e3
* **MTMAI-4815, MTMAI-4816:** create sales order with integration e74bcc8
* **MTMAI-4825:** Detail so new endpoint integration dba9fed
* **MTMAI-4834:** create spm enhance page - new endpoint 1fe67ea
* **MTMAI-4867:** product order need full fill component 982b0a1
* **MTMAI-5044, MTMAI-5045:** slicing and integration create so tag fac913c
* **MTMAI-5109, MTMAI-5110:** slicing integration detail spm with new response ed0ff60
* payload post form program marketing 06fa0ea


### Bug Fixes

* add varcel setting 929a38f
* approval so tag 2caf18e
* card price summary add (-) mark on cbd valuu 6f49284
* card price summary; discount - cbd  add (-) mark on modal view 692d0cf
* card promag data  on detail so 62a27b0
* Cbd form - link route 325653e
* change api product unscan po 36b93c5
* change endpoint after reset spm c5e5564
* create SO message 1dea106
* create so page select gudang dropown overlay card z-index 7cdd2a1
* Detail Scan page - unscan show noteview info need unscan qty efb2374
* Detail SPM handle approval request cancel submission f746afe
* Detail spm has unfinished product check ac94a6c
* Detail SPM update endpoint url confirm save spm 434d288
* distributor for payload program marketing baa8c4c
* enhance noteview promag in detail po c243995
* form program marketing option scope 3b15026
* modal confirmation no section for promag 8c40b1f
* **MTMAI-4864, MTMAI-4863:** fix option area form program marketing a74792e
* **MTMAI-4866:** maximal discount support comma a7bd612
* **MTMAI-4869:** show qty product reward f1a8a46
* **MTMAI-4870, MTMAI-4869:** fix view max discount & qty program marketing form ea2bc0b
* **MTMAI-4871:** view area only one program marketing form e11d5bd
* **MTMAI-4874:** Program marketing - privilege checker sections verification 8053ba8
* **MTMAI-4875:** handle null maximal sales discount 2addfeb
* **MTMAI-5052:** Detail  promag - section verification status badge enum cf0f090
* **MTMAI-5052:** Detail promag - verification status badge text checker 2568c32
* **MTMAI-5056, MTMAI-5057:** show timestamp and name program a2c892f
* **MTMAI-5056, MTMAI-5057:** show timestamp and name program 5929580
* **MTMAI-5060:** message success verification promag c699180
* **MTMAI-5060:** message success verification promag 0e8ae68
* **MTMAI-5061:** Detail promag section order term; map render value 0756bbd
* **MTMAI-5063:** warning text program marketing 22f203a
* **MTMAI-5064:** disable role finance create program marketing 298e175
* **MTMAI-5065:** disable button submit form e99689d
* **MTMAI-5065:** disable button submit form program marketing 21bd017
* **MTMAI-5067:** maximal discount support discount 6143b72
* **MTMAI-5068:** show minimal pembelian in modal program marketing confirmation d5fad8a
* **MTMAI-5069:** fix thousand convert modal promag 62e8fe5
* **MTMAI-5071:** fix sales discount enum promag in modal detail PO ff5b1d5
* **MTMAI-5079, MTMAI-5080:** accumulation state promag detail modal acedcbc
* **MTMAI-5102:** async function for show section promag in detail SO 79b231a
* **MTMAI-5104:** Create SPM post payload dd3ff44
* **MTMAI-5106:** PO list status enum changes - css class color 8613b56
* **MTMAI-5112:** PO create so filter out product list with 0 qty c0d087d
* **MTMAI-5122:** display estimation for product create so f7cdf00
* **MTMAI-5129:** redirect detail so after approval finance ce35dd7
* **MTMAI-5130:** show information estimation 587c6b2
* **MTMAI-5133:** create spm - handle load form product order c694e3a
* **MTMAI-5133:** SO create spm handle load product order list tabledata e8a32a3
* **MTMAI-5138:** enable button back in detail scan spm ffeb2cd
* **MTMAI-5139:** fix filter spm list 3a74ef5
* **MTMAI-5163:** render promag data card on empty product order list a428580
* **MTMAI-5165:** Detail spm unfinished products noteview ecda5fc
* **MTMAI-5166:** note view produk tidak diproses 329dffa
* **MTMAI-5166:** note view produk tidak diproses ac8f9c7
* **MTMAI-5167, MTMAI-5168:** Price card summary discount price add (-) mark 18da14f
* **MTMAI-5292:** produk hadiah confirmation 6929ac3
* **MTMAI5069:** nominal pop up f77aee1
* PO list status badge text color 597f0bc
* Sales marketing route to cbd discount form fc968ba
* So outstanding - close  so endpoint url 37c7161
* status retailer enum 8721f06

## [1.7.3](https://gitlab.com/maxxi-agro/atom/compare/v1.7.2...v1.7.3) (2024-06-25)


### Bug Fixes

* note view modal update user FA ([cb0d5f4](https://gitlab.com/maxxi-agro/atom/commit/cb0d5f4f0a6949d18abc50812b440b9ee1a7c03c))
* response after update data user ([ea2bb18](https://gitlab.com/maxxi-agro/atom/commit/ea2bb18fe0884d8716af0ec1ab4d6a3ae4aaf658))

## [1.7.2](https://gitlab.com/maxxi-agro/atom/compare/v1.7.1...v1.7.2) (2024-06-25)


### Bug Fixes

* change typo note view in detail sales order ([d8741d2](https://gitlab.com/maxxi-agro/atom/commit/d8741d2a83045de54665d04f0e4469c819821517))
* **MTMAI-4476, MTMAI-4475:** update select material ([e36a44f](https://gitlab.com/maxxi-agro/atom/commit/e36a44f53dd11398933942454f378b1dcaf1d813))
* **MTMAI-4478:** Detail retailer marketing pic section ([9630500](https://gitlab.com/maxxi-agro/atom/commit/9630500c27a9dadc14d9c2bab05c38b557daee08))
* **MTMAI-4479:** User list render area value - use key region_list ([ed7bd2a](https://gitlab.com/maxxi-agro/atom/commit/ed7bd2a377514198ba895dbacfc25bc1ff450f10))
* **MTMAI-4480:** User detail - retailer list status enum ([3e98560](https://gitlab.com/maxxi-agro/atom/commit/3e9856013634a451221185b8152c0f4df44049fa))
* **MTMAI-4482:** User form - select role to enable others field visibility ([d0cbf74](https://gitlab.com/maxxi-agro/atom/commit/d0cbf74590b64813c5261ef848ce1a22a1adabc0))
* **MTMAI-4492:** value sub area in edit user ([4cac8fd](https://gitlab.com/maxxi-agro/atom/commit/4cac8fdaaa92e6e3edb49b7bf7f89f3864bfeb9f))
* **MTMAI-4502:** fix note view edit user for FA ([1dd37a6](https://gitlab.com/maxxi-agro/atom/commit/1dd37a6b7717a8d0568fd2c400fc6ad0d1929656))

## [1.7.1](https://gitlab.com/maxxi-agro/atom/compare/v1.7.0...v1.7.1) (2024-06-10)


### Features

* **MTMAI-3830 - MTMAI-3859:** delete SO stock ([1bc5f99](https://gitlab.com/maxxi-agro/atom/commit/1bc5f9913b2e65dc4ea7d4e748d0f201d6632ac7))
* **MTMAI-4208:** enhance modal response ([107fecf](https://gitlab.com/maxxi-agro/atom/commit/107fecfa1e90d9452696c8dc2bc52575882498ce))
* **MTMAI-4210:** enhance ui detail retailer ([098ea61](https://gitlab.com/maxxi-agro/atom/commit/098ea613ba9e3dcd5b5cbd12be0a7c452acfa26f))
* **MTMAI-4213, MTMAI-4214:** add section retailer FA in detail user ([ed95ee7](https://gitlab.com/maxxi-agro/atom/commit/ed95ee7e788983835812ee141d97bcc3d48594b9))
* user payload field assistant ([2da67e4](https://gitlab.com/maxxi-agro/atom/commit/2da67e48595571a4a115ad112eb0ce0b07d9adf7))


### Bug Fixes

* cancel create spm ([8e0bb4c](https://gitlab.com/maxxi-agro/atom/commit/8e0bb4c8f191349ced5f4fede73b68b9394204fa))
* change endpoint approval so TAG ([9dcdf59](https://gitlab.com/maxxi-agro/atom/commit/9dcdf59ffe11c47c0b5a9e00dd3f8399de018457))
* counting initial stock available create spm ([aad8b4c](https://gitlab.com/maxxi-agro/atom/commit/aad8b4c622e3eeba490d6203710aa4db4d82139a))
* create spm not triger stock ([9f43955](https://gitlab.com/maxxi-agro/atom/commit/9f43955784bd42c46533490738a4787c9a4b8d94))
* handle show hide tab produk menunggu ([670f4bd](https://gitlab.com/maxxi-agro/atom/commit/670f4bd65175a725c282f1d97dae7bad8bb6b88b))
* **MTMAI-3832:** fix error warning and change create so logic ([39a1540](https://gitlab.com/maxxi-agro/atom/commit/39a15407cde57b46c13978b4921751cd03c835c9))
* privilege cancel spm request ([0ba10df](https://gitlab.com/maxxi-agro/atom/commit/0ba10dfffd0b961eba4ebf5e7d257b4bbd913ff8))
* validate create so ([8e68e86](https://gitlab.com/maxxi-agro/atom/commit/8e68e86d3aa4bdfe153fe141ca83564461bc71fe))
* waring detail spm ([aef2f8c](https://gitlab.com/maxxi-agro/atom/commit/aef2f8cce4abf88c0fed5d101025554c9b4e5733))

## [1.7.0](https://gitlab.com/maxxi-agro/atom/compare/v1.6.3...v1.7.0) (2024-02-09)


### Features

* **MTMAI-3034, MTMAI-3035:** slicing ui list reward and preparation integrate ([32a85a1](https://gitlab.com/maxxi-agro/atom/commit/32a85a1a641f38094156ff984d17644971495e79))
* **MTMAI-3034, MTMAI-3035:** slicing ui list reward and preparation integrate ([2278d83](https://gitlab.com/maxxi-agro/atom/commit/2278d831943e4e4b061582e103acb4d2003a7dce))
* **MTMAI-3038:** component modal detail with document ([21e0fcf](https://gitlab.com/maxxi-agro/atom/commit/21e0fcf9d3c57ef7d49dee319258f91ecd5cdcab))
* **MTMAI-3040:** Integrate reward form periode - list brand ([fbe52fe](https://gitlab.com/maxxi-agro/atom/commit/fbe52fe5543b68d993d8be5b48de66837ec50a26))
* **MTMAI-3040:** Reward edit form post update data ([045dc38](https://gitlab.com/maxxi-agro/atom/commit/045dc381860ca2ef61b5c7f79824f75a9d486771))
* **MTMAI-3040:** Reward form - handle add/remove product item; handle modal confirm create/response ([83d8913](https://gitlab.com/maxxi-agro/atom/commit/83d89139a3126f04e13161498001a8a0f3228e55))
* **MTMAI-3047:** remove type retailer in all status retailer list ([b2ee57e](https://gitlab.com/maxxi-agro/atom/commit/b2ee57e95d2fe6ade411063f4afae2827043ac5d))
* **MTMAI-3062:** slicing ui list reward by brand in detail product ([397de86](https://gitlab.com/maxxi-agro/atom/commit/397de86acfacd9b71d689c63ac0de9672c9ebc5e))
* **MTMAI-3066:** slicing ui detail period ([ca22639](https://gitlab.com/maxxi-agro/atom/commit/ca22639c4cb2c2ef6b00a40fa6d5adaa634e6296))
* **MTMAI-3067:** integration list period in detail product ([8eca404](https://gitlab.com/maxxi-agro/atom/commit/8eca404df9dc8ea35db36036925a610ec032550d))
* **MTMAI-3070:** Publish product - Reward form UI ([65e57f3](https://gitlab.com/maxxi-agro/atom/commit/65e57f3e3a339e8473106e660b794dcb0e8f343b))
* **MTMAI-3074, MTMAI-2075:** slicing and integrate history reward ([1fc2be1](https://gitlab.com/maxxi-agro/atom/commit/1fc2be189a0001a73fbabdee6e4b2438e191c8cd))
* **MTMAI-3078:** add privilage for CTA reward setting and integration detail reward setting ([f5622f3](https://gitlab.com/maxxi-agro/atom/commit/f5622f3f6fb85217c470482b3a48de1716153119))
* **MTMAI-3116:** Initial setup reward form data form Product Catalogue ([c3d1e21](https://gitlab.com/maxxi-agro/atom/commit/c3d1e21262b1efe9edef07c6d094cd0d73ff021d))
* **MTMAI-3125:** Reward setting - document upload component ([28d8fe6](https://gitlab.com/maxxi-agro/atom/commit/28d8fe6d32a156f4f17b59367ca44db67b198b39))
* **MTMAI-3257:** add title in list product in detail reward ([32f1208](https://gitlab.com/maxxi-agro/atom/commit/32f120825f123fcea6e0ddc0223fa39d536d71d1))
* **MTMAI-3274:** add privilage add product ([083705b](https://gitlab.com/maxxi-agro/atom/commit/083705b17eb8813e345e2c5da9fd9bd34cf35316))


### Bug Fixes

* **alert:** alert noteview detail period ([28a0deb](https://gitlab.com/maxxi-agro/atom/commit/28a0debdf53ecdf0d642735dd83fc0d3d740e4a5))
* **APPROVAL FINANCE:** change interface billing information ([a95a286](https://gitlab.com/maxxi-agro/atom/commit/a95a286b8413035062deced85daa5bd751044b67))
* **history reward:** sorting date ([f2126d0](https://gitlab.com/maxxi-agro/atom/commit/f2126d0d40e898ea69e72ae8bab499b67357cba0))
* **information product:** remove CTA setting reward ([e2d0bb8](https://gitlab.com/maxxi-agro/atom/commit/e2d0bb80f27297afbb9fbbcc778f774b368017d0))
* **MTMAI-3048, MTMAI-3104, MTMAI-3105, MTMAI-3106:** enhance retailer ([d127343](https://gitlab.com/maxxi-agro/atom/commit/d127343c4e0dcd02efeb58ecbfc71b201d6a72a0))
* **MTMAI-3076:** add privilege for CTA approve finance purchase order ([5076270](https://gitlab.com/maxxi-agro/atom/commit/50762702b63f91ddbe30aea90a7c021ef72238c7))
* **MTMAI-3246, MTMAI-3243:** fix alert warning in detail period ([ec63bb7](https://gitlab.com/maxxi-agro/atom/commit/ec63bb7e35bc0f5b4f5c5520abab31f8fe47e830))
* **MTMAI-3247:** Reward Form - Handle redirect modal success create reward ([c0b45b4](https://gitlab.com/maxxi-agro/atom/commit/c0b45b45d1f9509d46f47a628b983e0bdd4df916))
* **MTMAI-3255:** Handle indeterminate checkbox value add product list component ([619bad8](https://gitlab.com/maxxi-agro/atom/commit/619bad8e2016b38a1cfc90849bb34c08cf098366))
* **MTMAI-3256:** Handle cancel remove selected item product from table list ([1f2ad68](https://gitlab.com/maxxi-agro/atom/commit/1f2ad68e25f8c59bd02bfa3e7fd022c1139fe2b5))
* **MTMAI-3257:** Add reward product form - handle change periode modal confirm ([71371b4](https://gitlab.com/maxxi-agro/atom/commit/71371b4d3a3934b3e6e524ceb2ae8a7f4288bf60))
* **MTMAI-3259:** Reward form note view - handle text value by selected periode ([0811eb6](https://gitlab.com/maxxi-agro/atom/commit/0811eb65c7b6a60fb8f7e68fa7339a621030fdc3))
* **MTMAI-3272:** Reward form document upload error state - max size 5mb ([f9c5e26](https://gitlab.com/maxxi-agro/atom/commit/f9c5e265e00b91cd18a7d4888e32e551cfb2c1dc))
* **MTMAI-3272:** Reward form document upload error state - uploader preview ([e30e23d](https://gitlab.com/maxxi-agro/atom/commit/e30e23dcd26d90ded6e492fca21bb6fb186a189c))
* **MTMAI-3273:** Reward form handle add/remove selected product list ([669a191](https://gitlab.com/maxxi-agro/atom/commit/669a191f9853362d0457c57d5728cc78594a65cf))
* **MTMAI-3276:** Reward form product input handle checked state ([29762be](https://gitlab.com/maxxi-agro/atom/commit/29762bea528d1d4c632e414054b7ff7013afb01d))
* **MTMAI-3280:** Publish product - reward setting confirm dialog missing product name ([a6f2656](https://gitlab.com/maxxi-agro/atom/commit/a6f2656ffa748d5373d74d4cee45e8257e400447))
* **MTMAI-3282:** Publish request - reward form state has reward-setup ([bf2fc2e](https://gitlab.com/maxxi-agro/atom/commit/bf2fc2eb1ef9d671357a07250e8b53b525d2a452))
* **MTMAI-3283:** Reward form add from product detail - disable cta add/remove product ([a4013ae](https://gitlab.com/maxxi-agro/atom/commit/a4013ae63a8d91a8b5bde7315101dc9791a458b5))
* **MTMAI-3285:** Reward form - handle active selected periode id ([64bd54b](https://gitlab.com/maxxi-agro/atom/commit/64bd54ba0ecc31870df121be5f2da391dc905452))
* **MTMAI-3286:** Reward form - add/remove product visibility state ([d784520](https://gitlab.com/maxxi-agro/atom/commit/d784520e1f5b775133f93fd182e53e74e26b1457))
* **Reward Form:** Change Input dropdown periode - add mandatory document upload MTMAI-3268 ([bb010cd](https://gitlab.com/maxxi-agro/atom/commit/bb010cddceac68d3c2462b938f94713cfe579cc8))
* **Reward Form:** Document upload handle error uploading state ([97a9a08](https://gitlab.com/maxxi-agro/atom/commit/97a9a08031c5753c67458bb1bcff503954042ad1))
* **Reward Product Form:** handle selected periode value on confirm cancel/continue change periode ([8d87f84](https://gitlab.com/maxxi-agro/atom/commit/8d87f841568d52a11a9bc4f151e2cb0209d9d16f))
* **tooltip:** reward information in detail product ([f594462](https://gitlab.com/maxxi-agro/atom/commit/f594462201c35292d681bc3bc28097230a8a1820))

## [1.6.3](https://gitlab.com/maxxi-agro/atom/compare/v1.6.2...v1.6.3) (2024-01-11)


### Bug Fixes

* **detail distributor:** change created date account distributor ([4b2801f](https://gitlab.com/maxxi-agro/atom/commit/4b2801f33067fabc7349dcca894b5f11db082795))
* **MTMAI-2941:** time stamp in detail PO ([72f4875](https://gitlab.com/maxxi-agro/atom/commit/72f48750af10778391e7d74e77e312fbe3911d9d))
* **MTMAI-2944:** fix priceing in form create so ([b7dc8a2](https://gitlab.com/maxxi-agro/atom/commit/b7dc8a2a9b516a6d9e250f09f8e2f0595de3bd9b))
* **MTMAI-3044:** price SO TAG calculation ([f172376](https://gitlab.com/maxxi-agro/atom/commit/f17237617bd41b12e3f70121f9e7e11d0dbd2054))
* **need full fill:** size icon danger need full fill ([b22d331](https://gitlab.com/maxxi-agro/atom/commit/b22d331b7b31a1d233e23417eebe2627276052b6))