# GitLab CI for Release Management
# Automated GitLab Release creation when tags are pushed to main branch

# TEST JOB: Dry run untuk test release creation (manual trigger)
test-release-creation:
  stage: sync-dev-branches
  image: registry.gitlab.com/gitlab-org/release-cli:latest
  before_script:
    - apk add --no-cache git nodejs npm curl jq
    - git config --global user.name "GitLab CI Test"
    - git config --global user.email "<EMAIL>"
  script:
    # Simulate release creation process
    - echo "🧪 DRY RUN: Testing GitLab Release creation process"

    # Test version extraction
    - |
      if [ -n "$CI_COMMIT_TAG" ]; then
        VERSION=${CI_COMMIT_TAG#v}
        echo "✅ Found tag: $CI_COMMIT_TAG → Version: $VERSION"
      else
        VERSION="1.0.0-test"
        echo "⚠️ No tag found, using test version: $VERSION"
      fi

    # Test package.json reading
    - |
      if [ -f "package.json" ]; then
        PACKAGE_VERSION=$(node -p "require('./package.json').version")
        PACKAGE_NAME=$(node -p "require('./package.json').name")
        echo "✅ Package info: $PACKAGE_NAME v$PACKAGE_VERSION"
      else
        echo "❌ package.json not found"
      fi

    # Test changelog extraction
    - |
      if [ -f "CHANGELOG.md" ]; then
        echo "✅ CHANGELOG.md found"
        CHANGELOG_CONTENT=$(awk "/^## \[$VERSION\]/{flag=1; next} /^## \[/{flag=0} flag" CHANGELOG.md | head -10)
        if [ -n "$CHANGELOG_CONTENT" ]; then
          echo "✅ Changelog found for version $VERSION:"
          echo "$CHANGELOG_CONTENT"
        else
          echo "⚠️ No changelog for version $VERSION, would use git commits"
        fi
      else
        echo "❌ CHANGELOG.md not found"
      fi

    # Test git operations
    - |
      PREV_TAG=$(git describe --tags --abbrev=0 HEAD^ 2>/dev/null || echo "")
      if [ -n "$PREV_TAG" ]; then
        echo "✅ Previous tag: $PREV_TAG"
        COMMIT_COUNT=$(git rev-list --count $PREV_TAG..HEAD)
        echo "✅ Commits since last tag: $COMMIT_COUNT"
      else
        echo "⚠️ No previous tag found"
      fi

    # Test environment URLs
    - |
      echo "🌐 Environment URLs that would be included:"
      echo "  - Development: https://backoffice-atomdev.vercel.app"
      echo "  - Development-2: https://backoffice-atomdev2.vercel.app"
      echo "  - Staging: https://staging.back-office.maxxiagri.com"
      echo "  - Production: https://back-office.maxxiagri.com"

    - echo "✅ DRY RUN completed successfully!"
    - echo "🚀 Real release would be created with this data when pushed to main with tag"

  rules:
    - if: '$CI_COMMIT_BRANCH == "development"'
      when: manual
      allow_failure: true
  allow_failure: true

# GitLab Release Creation
create-gitlab-release:
  stage: release
  image: registry.gitlab.com/gitlab-org/release-cli:latest
  before_script:
    - apk add --no-cache git nodejs npm curl jq
    - git config --global user.name "GitLab CI"
    - git config --global user.email "<EMAIL>"
  script:
    # Extract version dari tag
    - VERSION=${CI_COMMIT_TAG#v}
    - echo "🏷️ Creating GitLab Release for version: $VERSION"

    # Get package info
    - |
      if [ -f "package.json" ]; then
        PACKAGE_VERSION=$(node -p "require('./package.json').version")
        PACKAGE_NAME=$(node -p "require('./package.json').name")
        PACKAGE_DESC=$(node -p "require('./package.json').description || 'Atom Backoffice Data Application'")
        echo "📦 Package: $PACKAGE_NAME v$PACKAGE_VERSION"
      else
        PACKAGE_NAME="atom-backoffice"
        PACKAGE_VERSION=$VERSION
        PACKAGE_DESC="Atom Backoffice Data Application"
      fi

    # Extract changelog dari CHANGELOG.md
    - |
      CHANGELOG_CONTENT=""
      if [ -f "CHANGELOG.md" ]; then
        echo "📋 Extracting changelog from CHANGELOG.md"
        CHANGELOG_CONTENT=$(awk "/^## \[$VERSION\]/{flag=1; next} /^## \[/{flag=0} flag" CHANGELOG.md | sed '/^$/d' | head -50)

        if [ -n "$CHANGELOG_CONTENT" ]; then
          echo "✅ Found changelog for version $VERSION"
        else
          echo "⚠️ No changelog found for version $VERSION in CHANGELOG.md"
        fi
      fi

    # Fallback ke git commits jika tidak ada changelog
    - |
      if [ -z "$CHANGELOG_CONTENT" ]; then
        echo "📝 Generating changelog from git commits"
        PREV_TAG=$(git describe --tags --abbrev=0 HEAD^ 2>/dev/null || echo "")

        if [ -n "$PREV_TAG" ]; then
          echo "🔍 Comparing $PREV_TAG..HEAD"
          CHANGELOG_CONTENT=$(git log --pretty=format:"- %s (%h)" $PREV_TAG..HEAD)
        else
          echo "🆕 Initial release - using recent commits"
          CHANGELOG_CONTENT=$(git log --pretty=format:"- %s (%h)" --max-count=10)
        fi
      fi

    # Get commit info
    - COMMIT_SHORT=$(echo $CI_COMMIT_SHA | cut -c1-8)
    - COMMIT_DATE=$(git show -s --format=%ci $CI_COMMIT_SHA)
    - COMMIT_AUTHOR=$(git show -s --format='%an' $CI_COMMIT_SHA)

    # Environment URLs berdasarkan Vercel deployment
    - DEV_URL="https://backoffice-atomdev.vercel.app"
    - DEV2_URL="https://backoffice-atomdev2.vercel.app"
    - STAGING_URL="https://staging.back-office.maxxiagri.com"
    - PROD_URL="https://back-office.maxxiagri.com"

    # Create release description
    - |
      cat > release_description.md << EOF
      ## 🚀 What's New in v$VERSION

      $CHANGELOG_CONTENT

      ## 📦 Package Information
      - **Name**: $PACKAGE_NAME
      - **Version**: v$PACKAGE_VERSION
      - **Description**: $PACKAGE_DESC

      ## 🌐 Environment Links
      | Environment | URL | Status |
      |-------------|-----|--------|
      | Development | [$DEV_URL]($DEV_URL) | ✅ Auto-deployed |
      | Development-2 | [$DEV2_URL]($DEV2_URL) | ✅ Auto-deployed |
      | Staging | [$STAGING_URL]($STAGING_URL) | ✅ Auto-deployed |
      | **Production** | [**$PROD_URL**]($PROD_URL) | ✅ **Live** |

      ## 📋 Release Details
      - **Tag**: \`$CI_COMMIT_TAG\`
      - **Commit**: [\`$COMMIT_SHORT\`]($CI_PROJECT_URL/-/commit/$CI_COMMIT_SHA)
      - **Date**: $COMMIT_DATE
      - **Author**: $COMMIT_AUTHOR
      - **Pipeline**: [#$CI_PIPELINE_ID]($CI_PIPELINE_URL)

      ## 📚 Documentation & Links
      - [Full Changelog](https://gitlab.com/maxxi-agro/atom/-/blob/$CI_COMMIT_SHA/CHANGELOG.md)
      - [Compare Changes]($CI_PROJECT_URL/-/compare/$PREV_TAG...$CI_COMMIT_TAG)
      - [Project Repository]($CI_PROJECT_URL)

      ## 🔄 Branch Sync Flow
      This release followed the automated branch sync flow:
      \`\`\`
      development → development-second → staging → main
      \`\`\`

      ---
      🤖 *This release was automatically created by GitLab CI/CD*
      EOF

    - echo "📄 Release description created:"
    - cat release_description.md

  release:
    name: 'Release v$VERSION'
    description: './release_description.md'
    tag_name: '$CI_COMMIT_TAG'
    ref: '$CI_COMMIT_SHA'
    assets:
      links:
        - name: '🌍 Production Environment'
          url: 'https://back-office.maxxiagri.com'
          link_type: 'other'
        - name: '🧪 Staging Environment'
          url: 'https://staging.back-office.maxxiagri.com'
          link_type: 'other'
        - name: '🔧 Development Environment'
          url: 'https://backoffice-atomdev.vercel.app'
          link_type: 'other'
        - name: '🔧 Development-2 Environment'
          url: 'https://backoffice-atomdev2.vercel.app'
          link_type: 'other'
        - name: '📋 Full Changelog'
          url: '$CI_PROJECT_URL/-/blob/$CI_COMMIT_SHA/CHANGELOG.md'
          link_type: 'other'
        - name: '🔍 Compare Changes'
          url: '$CI_PROJECT_URL/-/compare/$PREV_TAG...$CI_COMMIT_TAG'
          link_type: 'other'
  rules:
    - if: '$CI_COMMIT_BRANCH == "main" && $CI_COMMIT_TAG'
    # Test rule: uncomment untuk test di development
    # - if: '$CI_COMMIT_BRANCH == "development" && $CI_COMMIT_TAG'
  needs:
    - job: "sync-to-main"
      optional: true
  allow_failure: true

# Optional: Release notification job
notify-release:
  stage: release
  image: alpine:latest
  before_script:
    - apk add --no-cache curl jq
  script:
    - VERSION=${CI_COMMIT_TAG#v}
    - |
      echo "🎉 Release v$VERSION has been created!"
      echo "🌍 Production: https://back-office.maxxiagri.com"
      echo "📋 Release Notes: $CI_PROJECT_URL/-/releases/$CI_COMMIT_TAG"
      echo "🔄 Branch sync completed: development → development-second → staging → main"

      # Optional: Send notification ke Slack/Discord/Teams
      # Uncomment dan configure webhook URL di GitLab Variables jika diperlukan
      # if [ -n "$SLACK_WEBHOOK_URL" ]; then
      #   curl -X POST -H 'Content-type: application/json' \
      #     --data "{\"text\":\"🚀 Atom Backoffice v$VERSION deployed to production! 🌍 $PROD_URL\"}" \
      #     $SLACK_WEBHOOK_URL
      # fi
  rules:
    - if: '$CI_COMMIT_BRANCH == "main" && $CI_COMMIT_TAG'
  needs: ["create-gitlab-release"]
  when: on_success
  allow_failure: true
