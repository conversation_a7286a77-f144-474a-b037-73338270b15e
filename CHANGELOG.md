# Backoffice Changelog

## [1.13.3](https://gitlab.com/maxxi-agro/atom/compare/v1.13.2...v1.13.3) (2025-06-05)


### Bug Fixes:

* component image update program marketing ([24d3781](https://gitlab.com/maxxi-agro/atom/commit/24d3781afe8f04ffb108273de472df295ab09e3c))
* conversion chips data selected state ([a475e47](https://gitlab.com/maxxi-agro/atom/commit/a475e4708e0ccbd82e61af6ff0cf5e6f709968fa))
* detail butuh perbaikan - cta privilege edit program ([8f95bbd](https://gitlab.com/maxxi-agro/atom/commit/8f95bbd878581b4291e8931d5675d919bc96ec1a))
* detail butuh perbaikan - cta privilege edit program ([f25ce1a](https://gitlab.com/maxxi-agro/atom/commit/f25ce1ac3e7ff79d145a0e072b528dd904d38dd1))
* edit scheduled program; product scan chips selected state ([9c7ab0c](https://gitlab.com/maxxi-agro/atom/commit/9c7ab0c8e72ffde27706d2b333a3322ad65ad62a))
* fix form auto complete pmdk ([7d40c2f](https://gitlab.com/maxxi-agro/atom/commit/7d40c2fdd0e766e0b0e6e4586fc5bbe018b7a779))
* image updload ([dfdbe78](https://gitlab.com/maxxi-agro/atom/commit/dfdbe78bcceba182e038956cf45bc5ebe1f88783))
* input product search data state ([e89836a](https://gitlab.com/maxxi-agro/atom/commit/e89836af1d31bb05a3544cd06a21250e30ae5b65))
* link clipboard changes ([c93450f](https://gitlab.com/maxxi-agro/atom/commit/c93450f4754ba677b0419ff6754972f8a07d37b6))
* product scan list selected checkbox data is replaced on add from search result ([b84e84c](https://gitlab.com/maxxi-agro/atom/commit/b84e84c1c131d21cfeeea205a5f9cbb1d8e27730))
* product scan modal - reset query on close ([c5f0163](https://gitlab.com/maxxi-agro/atom/commit/c5f016369da72daa65cb6d8dd26b22a7d80b9563))
* product scan program scheduled; disable state previous product selected ([e0b3a82](https://gitlab.com/maxxi-agro/atom/commit/e0b3a82a574325225cd3fb68c8cd5c524a82adf4))
* product scan; checkbox list disable state data ([47ab7fa](https://gitlab.com/maxxi-agro/atom/commit/47ab7fa9e7a82a3fa6ea6df681f4aa18dbe19134))
* string level retailer list mpr ([2da3cd9](https://gitlab.com/maxxi-agro/atom/commit/2da3cd92f7ccb4488787bf3f3c845fad561f1cc3))

## [1.13.2](https://gitlab.com/maxxi-agro/atom/compare/v1.13.1...v1.13.2) (2025-05-26)


### Bug Fixes:

* interface changes ([9f57667](https://gitlab.com/maxxi-agro/atom/commit/9f57667bd5389f61571ca51826c32be8bad4f8e9))
* shorting progress program ([169b2bf](https://gitlab.com/maxxi-agro/atom/commit/169b2bfa96c297c532091ba20fffb556233c09a6))
* wording detail scan ([7e8d43a](https://gitlab.com/maxxi-agro/atom/commit/7e8d43abcc9f13c88ada7ef585fe00ff27d9f578))

## [1.13.1](https://gitlab.com/maxxi-agro/atom/compare/v1.13.0...v1.13.1) (2025-05-22)


### Bug Fixes:

* conversion modal; input unit hardcoded change to QR ([96d3832](https://gitlab.com/maxxi-agro/atom/commit/96d383200e0066b4ba22fc9a36bff4c385a00b88))
* fallback success message - cancel program response dialog ([b8b840c](https://gitlab.com/maxxi-agro/atom/commit/b8b840cfaa4a8b27df97f362904a2b231a20a627))
* **MTMAI-8029:** filter list promag disable date ([08c7a23](https://gitlab.com/maxxi-agro/atom/commit/08c7a23b2c68fae5f01a7eda2029a7f9caf20395))
* program term; disable option subarea scope program/product origin ([296858f](https://gitlab.com/maxxi-agro/atom/commit/296858f999f96ce08a802e9fed96586e5d0562f7))

## [1.13.0](https://gitlab.com/maxxi-agro/atom/compare/v1.12.3...v1.13.0) (2025-05-21)


### Features:

* **7122:** retailer form - section informasi program ([f26ebcd](https://gitlab.com/maxxi-agro/atom/commit/f26ebcd9aeeb91efd4450bf1f3e3ccb336fe44b7))
* **7127:** form section reward add Foto Hadiah ([90a102f](https://gitlab.com/maxxi-agro/atom/commit/90a102f3d68a183e43b994d15cf8df6bb143a21a))
* **7127:** sections form data value program-information ([acd3f9f](https://gitlab.com/maxxi-agro/atom/commit/acd3f9f2e2834976baa7d9da3bf0a6b738cfb5ae))
* **7132:** integrasi detail monitoring mpr pov admin marketing ([48f4a02](https://gitlab.com/maxxi-agro/atom/commit/48f4a022237e3c7ec710845b348e931e8e4a3c2b))
* **7132:** integrasi detail monitoring mpr pov finance ([32f71d0](https://gitlab.com/maxxi-agro/atom/commit/32f71d0cbe2aa4c04c8e7014c4b9a664d2bbc11e))
* **7266:** slicing detail monitoring mpr pov finance ([0e2c96e](https://gitlab.com/maxxi-agro/atom/commit/0e2c96eeebb41420b6f080d669f60db72b119c6f))
* **7266:** slicing detail monitoring mpr pov finance ([386399f](https://gitlab.com/maxxi-agro/atom/commit/386399faf381b38b4a4127c5c79942db2e1b8017))
* **7331:** create list program in retailer detail ([e308aa6](https://gitlab.com/maxxi-agro/atom/commit/e308aa69d0ad01fe858bf34e69e1ef719c944721))
* **7331:** create list program in retailer detail ([cdb73cd](https://gitlab.com/maxxi-agro/atom/commit/cdb73cda1bfd000b9eceabe3bdee5b924d44216e))
* **7335:** integrasi list program in detail retailer ([9a130f5](https://gitlab.com/maxxi-agro/atom/commit/9a130f524f5392135091b56c687dd7004b26766c))
* **7335:** integrasi list program in detail retailer ([ecc63f9](https://gitlab.com/maxxi-agro/atom/commit/ecc63f905a75994857180da19d6cce4917d93fba))
* **7336:** download progress scan qr ([3fed9f3](https://gitlab.com/maxxi-agro/atom/commit/3fed9f33507cfda4ffa70760f31593514d51d812))
* **MTMAI-7112:** slicing list mpr ([ab752af](https://gitlab.com/maxxi-agro/atom/commit/ab752aff5099e068f738eb0f6c7efc6e3c7eb81c))
* **MTMAI-7113:** integration list mpr ([f278069](https://gitlab.com/maxxi-agro/atom/commit/f27806906e98128847fc78c7f767497234035c52))
* **MTMAI-7123:** mpr settings section program term ([c74ee1f](https://gitlab.com/maxxi-agro/atom/commit/c74ee1faaeda7ab2b9d07f4f870a98aef9a59c3c))
* **MTMAI-7124:** setting form target scan term section - map options data enum ([3b85ffb](https://gitlab.com/maxxi-agro/atom/commit/3b85ffbbec41c9dd11099a1c1a3f6d38fcba856d))
* **MTMAI-7125:** add field target program ([ea24389](https://gitlab.com/maxxi-agro/atom/commit/ea243890cbf8dc71086a77c6d4b48210507dd881))
* **MTMAI-7126:** single target reward - product MAI setup input ([6d87e95](https://gitlab.com/maxxi-agro/atom/commit/6d87e950002adc4bb42e37e9c1c14e3049218e2e))
* **MTMAI-7127:** product scan - map form data selected chips, add check weight/volume ([541f2c2](https://gitlab.com/maxxi-agro/atom/commit/541f2c20e5b79062c81f1650045323e6b2f927ee))
* **MTMAI-7127:** product scan form - multilevel target grouping products list ([6fd4356](https://gitlab.com/maxxi-agro/atom/commit/6fd4356b07079ee6e1e82ea97ba959f041711ae4))
* **MTMAI-7127:** product scan term - implement modal conversion ([edfb8b9](https://gitlab.com/maxxi-agro/atom/commit/edfb8b9585dad199712a0e9003ebd00e65008fbc))
* **MTMAI-7127:** section product scan term - map form payload ([2e809b2](https://gitlab.com/maxxi-agro/atom/commit/2e809b2289483656eda3bb90287981a7c3c2dfea))
* **MTMAI-7127:** section program information - map form to payload ([d36b44f](https://gitlab.com/maxxi-agro/atom/commit/d36b44f235459104d15c09a0705fbb5e8d7c4b96))
* **MTMAI-7127:** section program term - map form payload ([29cef0b](https://gitlab.com/maxxi-agro/atom/commit/29cef0b082b31ba5e94681dcaff293a7a96d19b1))
* **MTMAI-7127:** section target scan term - map form to payload ([78d3c3f](https://gitlab.com/maxxi-agro/atom/commit/78d3c3fc04b613ea7972d6472127f2ba902ddbea))
* **MTMAI-7130:** slicing detail monitoring pov admin marketing ([5d3e3a5](https://gitlab.com/maxxi-agro/atom/commit/5d3e3a5cc0968877b1c3f3ebbce86866f85f8e07))
* **MTMAI-7130:** slicing detail monitoring pov admin marketing ([50d2df4](https://gitlab.com/maxxi-agro/atom/commit/50d2df405f72881cbb1bb31e7ab511b69306695b))
* **MTMAI-7135, MTMAI-7265:** product scan term - form data conversion map ([ac24264](https://gitlab.com/maxxi-agro/atom/commit/ac24264515a5d0d5d3f0d9a8570d711e395de3a0))
* **MTMAI-7135, MTMAI-7326:** reward term - multilevel target form value map ([1f3d3a6](https://gitlab.com/maxxi-agro/atom/commit/1f3d3a698e16f79537c3fb33b582cacf7568d8af))
* **MTMAI-7139:** slicing detail approval mpr ([6b510ec](https://gitlab.com/maxxi-agro/atom/commit/6b510ec5cf0ea3b09d1ba6962406335d10f9aa4b))
* **MTMAI-7139:** slicing detail approval mpr ([8c71ecb](https://gitlab.com/maxxi-agro/atom/commit/8c71ecbd2a54fa6bdf3aae972f23af6c60fe017b))
* **MTMAI-7140:** integrasi detail approval mpr ([00dd00a](https://gitlab.com/maxxi-agro/atom/commit/00dd00aa2f8cbf51ba3fbce9ceb74d122c6629da))
* **MTMAI-7140:** integrasi detail approval mpr ([192b261](https://gitlab.com/maxxi-agro/atom/commit/192b261d0e23996b1dcf5c9b97c0bb6c7bef914c))
* **MTMAI-7156:** slicing summary modal for mpr setting ([9cd749c](https://gitlab.com/maxxi-agro/atom/commit/9cd749ce693953684e5dfa1cd3ea211dd9de4cde))
* **MTMAI-7156:** slicing summary modal for mpr setting ([24c4208](https://gitlab.com/maxxi-agro/atom/commit/24c42085aa137ed9c89d10c55f02e9f2fdc370d7))
* **MTMAI-7156:** slicing summary modal for mpr setting ([aae59cc](https://gitlab.com/maxxi-agro/atom/commit/aae59cceccd4b96273e336a1c800c4c308d11afa))
* **MTMAI-7156:** slicing summary modal for mpr setting ([0c311f7](https://gitlab.com/maxxi-agro/atom/commit/0c311f7eadd98b8fb9d062d99da3dbbaca0216bd))
* **MTMAI-7157:** setting form program term; modal subarea-retailer, options area checkbox ([d815b15](https://gitlab.com/maxxi-agro/atom/commit/d815b15c4632e37fdd447df9d2a62c3f3b135bb8))
* **MTMAI-7160:** modal list box tab brand-variant - add product ([cf4079d](https://gitlab.com/maxxi-agro/atom/commit/cf4079d3342bb65a0f995637b21d572bf94b11e9))
* **MTMAI-7161:** product scan conversion modal component ([7b254aa](https://gitlab.com/maxxi-agro/atom/commit/7b254aa3203e03ecbf7e6f3c0ec832ba5137053b))
* **MTMAI-7262:** view action menu edit - response confirm modal ([850f173](https://gitlab.com/maxxi-agro/atom/commit/850f173d29752eb40231b8cd6b3fd966b9bf8b6d))
* **MTMAI-7264:** section form disable input edit ([02e8fbf](https://gitlab.com/maxxi-agro/atom/commit/02e8fbfb074d602b67a6d9d27c96b0c00c4f16c4))
* **MTMAI-7264:** setting edit form - disable edit section product-scan, reward-term ([06278c5](https://gitlab.com/maxxi-agro/atom/commit/06278c55359a3a64374bf431f378e121c18c4786))
* **MTMAI-7265:** implement edit mpr in status aktif ([099e29a](https://gitlab.com/maxxi-agro/atom/commit/099e29a6b75cddefd7313697ca9172ff0e86d1fe))
* **MTMAI-7312:** fe pop up confirmation ([ffee448](https://gitlab.com/maxxi-agro/atom/commit/ffee448044be66ea9a5f812aad4fa55fe8beebb3))
* **MTMAI-7313:** pop up informasi konversi produk detail approval ([8ee7efc](https://gitlab.com/maxxi-agro/atom/commit/8ee7efc531b5fbd9cf8a3e786282900d25b9ab12))
* **MTMAI-7332:** slicing modal program in retailer detail ([ed0eacd](https://gitlab.com/maxxi-agro/atom/commit/ed0eacdc75af863d5f3963030f2650732813f5d3))
* **MTMAI-7373:** detail program scheduled - flow cancel program, add modal form cancel/respons ([f5fa1e9](https://gitlab.com/maxxi-agro/atom/commit/f5fa1e9d5480a22111a844afbbaeb66da64433ff))
* **MTMAI-7373:** post delete program service ([7f1b656](https://gitlab.com/maxxi-agro/atom/commit/7f1b6562e9c34fca57fe58f494749699f2a24fe7))


### Bug Fixes:

* add conversion component; renderInfoConversion check data length ([4396e57](https://gitlab.com/maxxi-agro/atom/commit/4396e57f43c7a435c829b864b2655a3c99adb72e))
* add loading in component input list distributor ([3f5ac95](https://gitlab.com/maxxi-agro/atom/commit/3f5ac95be9332ec393138e9ce2286d0e1e6efe45))
* add loading subject ([9b2f0e5](https://gitlab.com/maxxi-agro/atom/commit/9b2f0e52dea7c9f558cfac47a87029faa5dcbaa3))
* add product scan - selected state target/rule type, rule scan ([48d3537](https://gitlab.com/maxxi-agro/atom/commit/48d3537cac8688150ffc782695e9cc888db8a37e))
* add product scan selected state variant/brand ([dea3678](https://gitlab.com/maxxi-agro/atom/commit/dea3678a26b88e968d204e4115747a6f32d88700))
* approval verification ([fbdac80](https://gitlab.com/maxxi-agro/atom/commit/fbdac80a543ec001ba3654d6df8964494b2bc9f0))
* button privillege belum benar ([94c04ca](https://gitlab.com/maxxi-agro/atom/commit/94c04ca87ae0f323ac9867b5b034fd4f373bc924))
* change url endpoint ([2c7fd92](https://gitlab.com/maxxi-agro/atom/commit/2c7fd926532150e52fcd8f858d80343f2ff5e71a))
* chips keyword ([2de5583](https://gitlab.com/maxxi-agro/atom/commit/2de558301b2bfdf2ae543181da71b7b87583b9f8))
* chipslist component ([f2edeb0](https://gitlab.com/maxxi-agro/atom/commit/f2edeb06b9fa58f5d805dd5607a27ce79f2a2541))
* component list box checklist ([d22f04b](https://gitlab.com/maxxi-agro/atom/commit/d22f04bfd494aa7579df371a06aeec91e10a7ffd))
* confirm modal program; render reward ([7d55150](https://gitlab.com/maxxi-agro/atom/commit/7d55150bd14e3686465706c2a7ba69d25e45eac6))
* control name auto complete ([afe8903](https://gitlab.com/maxxi-agro/atom/commit/afe8903784ddecb79dba51875e61d43956cdb764))
* conversion chips; add no data tpl - if no variant available ([f41e390](https://gitlab.com/maxxi-agro/atom/commit/f41e390933fc13d6f6d571e19bf6358c97e9b568))
* conversion chips; disable previous selected chip ([43ef46a](https://gitlab.com/maxxi-agro/atom/commit/43ef46afb3a31c98bbe0675458c7087841bdc067))
* create program handle error nomor surat pengajuan ([fa8edbc](https://gitlab.com/maxxi-agro/atom/commit/fa8edbcb3347c548832e008c2b7c5997c914b3b9))
* detail program - modal period; input datepicker set disable ([9082951](https://gitlab.com/maxxi-agro/atom/commit/9082951fd718b3390dc444bfd8def9a155b8ff31))
* detail program active; copy link to external monitoring url (kembang) ([12dce42](https://gitlab.com/maxxi-agro/atom/commit/12dce42cfbfa840a67e069b1b4a85625d915e4b1))
* detail program aktif; disable action menu extend period ([1de1019](https://gitlab.com/maxxi-agro/atom/commit/1de10191a04e4524519f27f550074004584e93d8))
* disable action menu program active ([123a854](https://gitlab.com/maxxi-agro/atom/commit/123a854f7325c35fef122797d31583d2968f4ac3))
* document upload cta label if has input data ([0a5d081](https://gitlab.com/maxxi-agro/atom/commit/0a5d081f2a5c19e7e92d0e5659a4022c61d525ee))
* edit setting - product scan term; target program data state ([48ca612](https://gitlab.com/maxxi-agro/atom/commit/48ca612125c28e96dfa461c59422761d876b6f8c))
* edit setting form; periode program set min end date ([621e4bb](https://gitlab.com/maxxi-agro/atom/commit/621e4bbcbf51636849bf0b6ae6a472b5615998ef))
* edit setting form; product scan data ([3b8a7b1](https://gitlab.com/maxxi-agro/atom/commit/3b8a7b1de5f4e59a83a0b996a74ee72e1900f949))
* edit setting form; reward handle single reward data ([ed83494](https://gitlab.com/maxxi-agro/atom/commit/ed83494ee3072750deee979d723f11da949dc40e))
* edit setting program term scope - checkbox selected state area ([5548a6b](https://gitlab.com/maxxi-agro/atom/commit/5548a6bddb83cd160284dee5634cad56532c3a22))
* edit setting; product scan term  data ([61a3310](https://gitlab.com/maxxi-agro/atom/commit/61a331022165ebd07bebf0603e1dc4246c651557))
* edit settings; reward term multilevel data state ([45c8262](https://gitlab.com/maxxi-agro/atom/commit/45c8262da51749eb3a4c2312e4e7d3511885be0c))
* edit settings; single/multilevel  product scan target program validator ([0673316](https://gitlab.com/maxxi-agro/atom/commit/0673316cbe31edd094f0e01d39052f0b54590d38))
* enum ([f131b79](https://gitlab.com/maxxi-agro/atom/commit/f131b79f15050d4cfcc53fa4aec0ae82e0090b1e))
* fix loading state ([f5ea370](https://gitlab.com/maxxi-agro/atom/commit/f5ea3704c01401d7ed6f90038ed0ca7aa72090de))
* form setting add product scan conversion chips ([892a5e9](https://gitlab.com/maxxi-agro/atom/commit/892a5e99ec06fccc0b641b9aa86a2feb84ac1a06))
* generic interface ([576eb5a](https://gitlab.com/maxxi-agro/atom/commit/576eb5ae1d6392f29b1266ac69fdc10e549f8f78))
* input listbox toggle show selected on close modal ([5e774ba](https://gitlab.com/maxxi-agro/atom/commit/5e774bac71577c539426eee8b020730e7ba8ec3b))
* input-date-picker change props isDisabled ([1463da1](https://gitlab.com/maxxi-agro/atom/commit/1463da1b5c4d9096dd9614b2c012b880c37f72c1))
* interface missing index ([9bcbb21](https://gitlab.com/maxxi-agro/atom/commit/9bcbb21e000d83f25493088c22ab2497aabb04da))
* merge fix conflict ([6b36ce1](https://gitlab.com/maxxi-agro/atom/commit/6b36ce1ec67e3fab705637f7d10c20789303208b))
* missing reward photo product non mai ([ca6fb1f](https://gitlab.com/maxxi-agro/atom/commit/ca6fb1f478f8510e7c008f57c5c0a0cfc5e7b911))
* modal confirm label value text ([b75c6df](https://gitlab.com/maxxi-agro/atom/commit/b75c6df450345af519c41a56104d9277356cf691))
* modal perpanjang periode program; input datepicker format ([6484014](https://gitlab.com/maxxi-agro/atom/commit/648401462145a72289b50191be74f0b6aa00ce4f))
* mpr epoch date; format time utils ([a82eba6](https://gitlab.com/maxxi-agro/atom/commit/a82eba6b52f66c39cbf9b5e5f2f472e3902db3ee))
* **MTMAI-7312:** page link ([681adaa](https://gitlab.com/maxxi-agro/atom/commit/681adaa2d5db7bf1646bb8280c5ec492a4be5490))
* **MTMAI-7329:** download progress scan qr ([013f17d](https://gitlab.com/maxxi-agro/atom/commit/013f17d0032357e0f36db0d1a42cb6a93e985021))
* **MTMAI-7329:** download progress scan qr ([06e3616](https://gitlab.com/maxxi-agro/atom/commit/06e3616545f1d782cd066a04160edad92927c3e9))
* **MTMAI-7337:** program/product scope input select/unselect state item ([cee124b](https://gitlab.com/maxxi-agro/atom/commit/cee124b8bbba6a93c269ac66075ccdc9fa479251))
* **MTMAI-7372:** create program form success - redirect to detail page route ([0039731](https://gitlab.com/maxxi-agro/atom/commit/0039731417d1e877cbc56b06dbcfba139de8ada0))
* **MTMAI-7730:** modal confirmation create program; section scan target scan multilevel ([ec3b6f4](https://gitlab.com/maxxi-agro/atom/commit/ec3b6f4c5820aa35e7f09148a5ee6057de191ab3))
* **MTMAI-7730:** modal confirmation create program; section scan target scan single target ([dae5258](https://gitlab.com/maxxi-agro/atom/commit/dae52586e78e51f06680a5730157611eaabcaf91))
* **MTMAI-7733:** create program - modal success dismiss link back to list page ([2256060](https://gitlab.com/maxxi-agro/atom/commit/22560607ba64630d4660e49611e0bdb63504439d))
* **MTMAI-7745:** bo detail hanya tampil section informasi ([546cb71](https://gitlab.com/maxxi-agro/atom/commit/546cb71962e7211c6b6e31bcd7c4bd8ac82ebadd))
* **MTMAI-7750:** document upload - preview image show cta label Ganti File ([58f8bdf](https://gitlab.com/maxxi-agro/atom/commit/58f8bdf6c5db3441557cc51479d8331f186d57f6))
* **MTMAI-7751:** rewardterm; product mai input budget show/hide info maximum by price unit ([b85c901](https://gitlab.com/maxxi-agro/atom/commit/b85c901a3ff37e9fb4bfb9ce54d920125a88b6aa))
* **MTMAI-7752:** program term form;  scope changes reset chips selected subarea/retailer/distributor ([7cb97c3](https://gitlab.com/maxxi-agro/atom/commit/7cb97c3d9dd678394671ad3bd4c2dd5941954399))
* **MTMAI-7756:** add privillege ([582bba4](https://gitlab.com/maxxi-agro/atom/commit/582bba4fa9280d3778c159d155698072498b3aa7))
* **MTMAI-7756:** bo detail ketentuan hadiah tidak sesuai ([6b72e8e](https://gitlab.com/maxxi-agro/atom/commit/6b72e8eb4bf132fb21cdce274c25932d103727c0))
* **MTMAI-7756:** field target scan level string ([579b206](https://gitlab.com/maxxi-agro/atom/commit/579b2060d02134a80edcc122bdff9d60e4e8128f))
* **MTMAI-7766:** field target scan level string ([2b7d0e0](https://gitlab.com/maxxi-agro/atom/commit/2b7d0e05312ce2e0ad88104f5e7c64ce94473bd6))
* **MTMAI-7766:** perbaikan style ([b92a1d5](https://gitlab.com/maxxi-agro/atom/commit/b92a1d5619010ecabbe9a1f201380e154e09f08c))
* **MTMAI-7766:** perbaikan target scan qr menjadi target program ([88eb52d](https://gitlab.com/maxxi-agro/atom/commit/88eb52d0844e1167dd47ff268d9d13be9b06aae1))
* **MTMAI-7776:** reward term; multilevel reward doc upload index ([7f4f585](https://gitlab.com/maxxi-agro/atom/commit/7f4f585bcb37013b8b1eeb8533d2fde33ada400e))
* **MTMAI-7780:** foto reward term tidak sesuai ([266a487](https://gitlab.com/maxxi-agro/atom/commit/266a4878d8935a6319ecd3370424b115bcffcb07))
* **MTMAI-7807:** conversion form chips group state ([7f19661](https://gitlab.com/maxxi-agro/atom/commit/7f19661d45c9072b3c28aee50e298026a077c1f2))
* **MTMAI-7809:** ketentuan scan; rule_scan value from rule_scan_enum ([0b94088](https://gitlab.com/maxxi-agro/atom/commit/0b940880e95c08b64536dbc7b0aa8320e2b7644a))
* **MTMAI-7809:** program term form; program/product scope checkbox id ([36267d6](https://gitlab.com/maxxi-agro/atom/commit/36267d6076f80230113b44c57037a1a26db44620))
* **MTMAI-7818:** confirm create program; syarat program value ([4378baa](https://gitlab.com/maxxi-agro/atom/commit/4378baabc08192da97d044cf2104bd909c782bde))
* **MTMAI-7845:** validate state input multilevel target value ([6e199a6](https://gitlab.com/maxxi-agro/atom/commit/6e199a6122b1a27c0d79466c07edf3cbc4067d47))
* **MTMAI-7846:** button disappear ([538a3a3](https://gitlab.com/maxxi-agro/atom/commit/538a3a38e0d83c38a459ee8bcd5b780f2a4c8c17))
* **MTMAI-7846:** button disappear ([0281686](https://gitlab.com/maxxi-agro/atom/commit/028168651ec30a4d0864a29691242929b865fe50))
* **MTMAI-7846:** showing tab for need changes status ([a7a056a](https://gitlab.com/maxxi-agro/atom/commit/a7a056a3f7bf56ac93e692ac2785a16b6129e8de))
* **MTMAI-7846:** showing tab for need changes status ([58d0855](https://gitlab.com/maxxi-agro/atom/commit/58d0855591b43943b2f52c5f96e6c129d99ddad7))
* **MTMAI-7867:** create program handle conversion data to payload ([e47365e](https://gitlab.com/maxxi-agro/atom/commit/e47365ea4e0e2ecb9f8d0fcfec348702ecd8a67b))
* **MTMAI-7883:** conversion chips filter from option product scan ([84be6db](https://gitlab.com/maxxi-agro/atom/commit/84be6db0dd49298454bc10726bc726a500e1f22f))
* **MTMAI-7889:** reward term setting; validate input value state ([ec1511c](https://gitlab.com/maxxi-agro/atom/commit/ec1511c171d38d114cf0478daaa90b9f61b41ff3))
* **MTMAI-7892:** payload ([3310d11](https://gitlab.com/maxxi-agro/atom/commit/3310d11ca3baaf4100e99ae33568acdd3b171387))
* **MTMAI-7892:** style ([b6899c6](https://gitlab.com/maxxi-agro/atom/commit/b6899c6889eb970616f69422f55245cf5a5b3f47))
* **MTMAI-7892:** the detail tab is not visible ([51e0892](https://gitlab.com/maxxi-agro/atom/commit/51e0892914f3594151353db10107f78932704129))
* **MTMAI-7893:** icon disappear ([1df59c9](https://gitlab.com/maxxi-agro/atom/commit/1df59c9d618847da6cb2e1b4e987202e5a9e5219))
* **MTMAI-7893:** wording not the same and icon disappear ([605c066](https://gitlab.com/maxxi-agro/atom/commit/605c066a03d5dece01f89c7fcfdc7430ee22448d))
* **MTMAI-7896:** reward section; revision note ([add2e3a](https://gitlab.com/maxxi-agro/atom/commit/add2e3aa1955729e357b9ea0db1d56779ddaa9ad))
* **MTMAI-7900:** add logic finance ([617c575](https://gitlab.com/maxxi-agro/atom/commit/617c575df796b073c3086c3ae5810d6bae925e2a))
* **MTMAI-7900:** periode still epoch ([886fb3e](https://gitlab.com/maxxi-agro/atom/commit/886fb3efbeae6ed83fc6a6735861a7b4bb720dc8))
* **MTMAI-7901:** detail popup confirmation reward section is different ([ba0a381](https://gitlab.com/maxxi-agro/atom/commit/ba0a3819774babd2be12be8c647155c5f2076ad8))
* **MTMAI-7902:** card note change retailer; edit program  privilege check ([e37ef9f](https://gitlab.com/maxxi-agro/atom/commit/e37ef9f628b6aaf97cba1bd704c275a6ccc42f72))
* **MTMAI-7904, MTMAI-7906, MTMAI-7947:** confirm modal data; null value on program/product scope ([a6121ab](https://gitlab.com/maxxi-agro/atom/commit/a6121ab428781dc9a78f36c97496589e05e1be07))
* **MTMAI-7904, MTMAI-7906, MTMAI-7947:** confirm modal; mai product reward data item unit ([3bd4aa8](https://gitlab.com/maxxi-agro/atom/commit/3bd4aa8920293a377b2527cc6b6f27d256f75a5b))
* **MTMAI-7904, MTMAI-7906:** missing product scan data ([a9694c9](https://gitlab.com/maxxi-agro/atom/commit/a9694c9efe8ba73653dccf8b02397fdf739bffc1))
* **MTMAI-7904:** disable edit service get program status scheduled; map scope id payload update ([4c1b654](https://gitlab.com/maxxi-agro/atom/commit/4c1b6545ea0bd221a202b0b507c59b7076496cc2))
* **MTMAI-7904:** scheduled program; product scan set default disable ([ab65f32](https://gitlab.com/maxxi-agro/atom/commit/ab65f32c19e63df980d259c78f6bcf5e7768a538))
* **MTMAI-7906, MTMAI-7947, MTMAI-7973:** conversion product component; data handle selected variant/brand ([3002ddd](https://gitlab.com/maxxi-agro/atom/commit/3002ddd4e81dd8c01874c317cf9efb67905d1084))
* **MTMAI-7906, MTMAI-7947:** product scan; edit/update conversion data ([da7d25d](https://gitlab.com/maxxi-agro/atom/commit/da7d25d0abb1760cc14edf5eb9c638ff0ebdec89))
* **MTMAI-7906:** add-product-scan component; filter duplicated item chips ([176d0d4](https://gitlab.com/maxxi-agro/atom/commit/176d0d4b8eb05d1980a2270694fca8a8d0380ee9))
* **MTMAI-7906:** conversion product - selected chips map data ([88dad78](https://gitlab.com/maxxi-agro/atom/commit/88dad78bfdbc13c3b9bff3192d5b2a128145264d))
* **MTMAI-7906:** detail program butuh perbaikan; noteview section item order ([9ffe0db](https://gitlab.com/maxxi-agro/atom/commit/9ffe0db2a4899ea2c8d234bf560239aa9cbb1fa6))
* **MTMAI-7906:** payload set target_scan_qr to [] if multilevel target ([9fb0c20](https://gitlab.com/maxxi-agro/atom/commit/9fb0c2003c9db5acf3b60c9d34ef966de11373fd))
* **MTMAI-7906:** product conversion data state on removing item selected state ([34523ad](https://gitlab.com/maxxi-agro/atom/commit/34523ad9fb945d729732f4498659b6d07371a058))
* **MTMAI-7906:** product scan data; sort data index item with conversion value ([c7ee0e4](https://gitlab.com/maxxi-agro/atom/commit/c7ee0e4146d05c5c9af07cca375ffb6c0137839a))
* **MTMAI-7906:** program scope; missing selected sub-area ([eb2dcb6](https://gitlab.com/maxxi-agro/atom/commit/eb2dcb647c1acc0c5e5f44c5292fdedc9d6f9621))
* **MTMAI-7906:** program/product scope checkbox chips ([3748124](https://gitlab.com/maxxi-agro/atom/commit/374812416e9a6a56c95694c457e877db122baee9))
* **MTMAI-7908:** cancel program api service ([e10e7d3](https://gitlab.com/maxxi-agro/atom/commit/e10e7d3610afdf79d36cc53ad25a23facfe9333b))
* **MTMAI-7908:** scheduled program; action cancel program modal ([abe7e59](https://gitlab.com/maxxi-agro/atom/commit/abe7e59788c76d4a64b6d67148fa557ef407a04e))
* **MTMAI-7932:** button detail null condition ([97c131c](https://gitlab.com/maxxi-agro/atom/commit/97c131cd4c6613c746f3e43d9cc06b2e6f8856d0))
* **MTMAI-7943:** fix qr scan doesnt match ([16448f8](https://gitlab.com/maxxi-agro/atom/commit/16448f8408313af138dba083e8a44d4975d07e44))
* **MTMAI-7947:** scheduled program; disable dropdown options level ([8035677](https://gitlab.com/maxxi-agro/atom/commit/803567753c7182ecbdc3189dd5156aee08e1c79e))
* **MTMAI-7947:** scheduled program; product scan disabled state target program ([b056d1a](https://gitlab.com/maxxi-agro/atom/commit/b056d1a8fe4204931f2ccc700639ea9247ac6d26))
* **MTMAI-7947:** scheduled program; program/product scope selected chips disable state ([b069b07](https://gitlab.com/maxxi-agro/atom/commit/b069b0735416cd5b6dd7779edc124c604ec18407))
* **MTMAI-7948:** fix extend period ([1de62d9](https://gitlab.com/maxxi-agro/atom/commit/1de62d94ae61d4ecb9e6bd311175ba9475f8ea6b))
* **MTMAI-7967:** fix button action visible in detail cancel ([f3f7d54](https://gitlab.com/maxxi-agro/atom/commit/f3f7d5444a567ce1493457f9b3878289d00492ca))
* **MTMAI-7968:** program scope retailer/distributor; add spinner loading get data ([76e2a69](https://gitlab.com/maxxi-agro/atom/commit/76e2a69d2fad4e2ed1ca8afece5bf9f564588457))
* **MTMAI-7973, MTMAI-7947, MTMAI-7906:** confirmation modal ([5555032](https://gitlab.com/maxxi-agro/atom/commit/55550329ed38101a73bfbc6bb9c1b1e9a890ae51))
* **MTMAI-7973, MTMAI-7947, MTMAI-7906:** reward - uploader in multilevel target showing broken image ([5e7e8ad](https://gitlab.com/maxxi-agro/atom/commit/5e7e8ad3a8b1ebeadd094e99f08a86b52073e281))
* **MTMAI-7973, MTMAI-7947, MTMAI-7906:** reward - uploader in multilevel target showing broken image ([841bb15](https://gitlab.com/maxxi-agro/atom/commit/841bb15d682054840c048776373498a5f2d591a2))
* **MTMAI-7974:** detail budget finance ([b172548](https://gitlab.com/maxxi-agro/atom/commit/b172548368410d8e25ad9533f86f433f6a2f51cc))
* **MTMAI-7974:** fix modal progress scan error ([156169f](https://gitlab.com/maxxi-agro/atom/commit/156169fc834803698cc9d3e4876b65a6d49a3d26))
* **MTMAI-7974:** fix modal progress scan not showing up when detail button was clicked ([b2d6f38](https://gitlab.com/maxxi-agro/atom/commit/b2d6f38c8c18a8f092587323b78dbb5095ea70ca))
* **MTMAI-7975:** approved date program; convert epoch ([4b11bdf](https://gitlab.com/maxxi-agro/atom/commit/4b11bdf1f1adfb935c1a4018599408eec2aa7663))
* **MTMAI-7975:** program list - created timestamp need plus7 ([1e64790](https://gitlab.com/maxxi-agro/atom/commit/1e6479090170bbbb5c45fb830cf98c4c4430aa40))
* **MTMAI-7975:** utils format epoch time add param need plus7 ([d498ec4](https://gitlab.com/maxxi-agro/atom/commit/d498ec4171488e973d6ae08c85a06f9fb1c5e8c5))
* **MTMAI-7994:** target scan multilevel target data form; get level number ([6dbeb77](https://gitlab.com/maxxi-agro/atom/commit/6dbeb770c12f39cceb66cefbed8c4035b8645a7c))
* **MTMAI-7995:** detail budget finance (estimate preview) ([357a17b](https://gitlab.com/maxxi-agro/atom/commit/357a17b984348e07cda84d02d05456d97a33cb01))
* **MTMAI-7995:** enhance wording total retailer ([6b23953](https://gitlab.com/maxxi-agro/atom/commit/6b239533f661ebc17db3fff25ecb890446f0bd04))
* post create program; payload scope missing retailer id ([bea2246](https://gitlab.com/maxxi-agro/atom/commit/bea2246010a2b1a419b46c269f79fb69a18005e8))
* product bonus fetch filter undefined price unit item ([39fe81f](https://gitlab.com/maxxi-agro/atom/commit/39fe81f6a03403afa24495adb2f61f613d5250c3))
* product confirmation modal discount term ([e9c5036](https://gitlab.com/maxxi-agro/atom/commit/e9c5036d352951a99a80f55e9e58ddde8dc39371))
* product scan get selected product brand/variant ([60bf08a](https://gitlab.com/maxxi-agro/atom/commit/60bf08a75a32646787070f92bbcd12c1098a938b))
* product scan single target set/unset validator on rule target change ([035bd6d](https://gitlab.com/maxxi-agro/atom/commit/035bd6d8ecd35b91bfec37826d20a7d5953fc712))
* product scan term; multilevel input target disable/enable if previous input empty ([b3e90b8](https://gitlab.com/maxxi-agro/atom/commit/b3e90b8fa556d3c8f61c16114489187d12cff9df))
* product scan term; payload for target scan ([c192760](https://gitlab.com/maxxi-agro/atom/commit/c1927601e1fb1eef05cc382a16e753daa266ed9a))
* product scan term; selected target scan data ([25879bc](https://gitlab.com/maxxi-agro/atom/commit/25879bc221d85d628dac547028bf51b9a5200962))
* program enum status pengajuan perpanjangan ([e584363](https://gitlab.com/maxxi-agro/atom/commit/e584363b16002f9362f8f71cd388f85cb926cfbe))
* program information; document upload add document group field ([981009e](https://gitlab.com/maxxi-agro/atom/commit/981009ee47b844cf1de4681bcd27ea40093ae992))
* program period end; calc min date by start period change ([419f896](https://gitlab.com/maxxi-agro/atom/commit/419f8967a46f7483a41309c2ce8c8c50fbf7070e))
* Reward form - single target set/reset validator by selected reward type ([af2d774](https://gitlab.com/maxxi-agro/atom/commit/af2d774a30e529062df5c371f9be2ee72a97ea60))
* reward form validate state by selected single/multilevel target ([8727677](https://gitlab.com/maxxi-agro/atom/commit/8727677714cea1f38d0ffb0c4c1dc283823a5645))
* reward term section - validate maximum budget with  product bonus ([801b5ff](https://gitlab.com/maxxi-agro/atom/commit/801b5ff292f61b6fdd5482baf5731569c6be5908))
* reward term; get target reward should return default if no data ([9881e57](https://gitlab.com/maxxi-agro/atom/commit/9881e578abee0182baaeed2e4320d772e6d172a5))
* scope section input chips disable state ([af5b1d8](https://gitlab.com/maxxi-agro/atom/commit/af5b1d865a22d4940c633a36adf66544e23b2582))
* setting form - product scan checkbox selected state when changing tab variant/brand ([0ba1dcd](https://gitlab.com/maxxi-agro/atom/commit/0ba1dcd1be02e480da571b1e1047803d06cbaa01))
* setting form; product scan variant/brand select/unselect state ([413ed80](https://gitlab.com/maxxi-agro/atom/commit/413ed8050280dbc3734ca486db48df3110aa5b83))
* update endpoint ([9c0abe6](https://gitlab.com/maxxi-agro/atom/commit/9c0abe6b47454e424219a05c878a4221ddb58b9c))
* update endpoint path ([b74e4c0](https://gitlab.com/maxxi-agro/atom/commit/b74e4c09a07e6d7b5b7b1e1d3a8bca2cbc5d9446))
* update payload add status_enum ([765522f](https://gitlab.com/maxxi-agro/atom/commit/765522f0d323c58d1a64d55c58065a2560d933c0))

## [1.12.3](https://gitlab.com/maxxi-agro/atom/compare/v1.12.2...v1.12.3) (2025-05-19)


### Bug Fixes:

* add loading in component input list distributor ([f786698](https://gitlab.com/maxxi-agro/atom/commit/f786698fbeea27ed5c6cddfb7659670bc4c48253))
* chips keyword ([6ecad3b](https://gitlab.com/maxxi-agro/atom/commit/6ecad3b5c3a69a3db42c75b32f34712e066f8739))
* component list box checklist ([ced1a63](https://gitlab.com/maxxi-agro/atom/commit/ced1a63e4f6b6dce365b00d92b38700d67137498))
* list product unscan ([1c2370c](https://gitlab.com/maxxi-agro/atom/commit/1c2370c996220a348829e9e562acfd4485297f00))
* product confirmation modal discount term ([7b7e683](https://gitlab.com/maxxi-agro/atom/commit/7b7e683986045c7892e58a936ee9ac14bd79291d))

## [1.12.2](https://gitlab.com/maxxi-agro/atom/compare/v1.12.1...v1.12.2) (2025-04-16)


### Bug Fixes:

* discount term product form ([7276f69](https://gitlab.com/maxxi-agro/atom/commit/7276f69c6bdc5d1eeb492253fb486340ecf4d569))
* product need full fill reward ([02e1655](https://gitlab.com/maxxi-agro/atom/commit/02e16559d0eb8512b03b73a202e99c46c51194c4))

## [1.12.1](https://gitlab.com/maxxi-agro/atom/compare/v1.12.0...v1.12.1) (2025-04-11)


### Features:

* program marketing edit active ([b76fbc2](https://gitlab.com/maxxi-agro/atom/commit/b76fbc27cf7181c2113b08f1c6dec9531fd54a09))


### Bug Fixes:

* hide product need full fill if total price null ([7af169f](https://gitlab.com/maxxi-agro/atom/commit/7af169f41b42c4c7a697d07268c9039c116a459f))
* hide product need full fill if total price null ([f36a936](https://gitlab.com/maxxi-agro/atom/commit/f36a936aba39ac726a48480b750623255cc43d15))
* view document active status pmdk ([e03ba5d](https://gitlab.com/maxxi-agro/atom/commit/e03ba5db3d8a937fec01a23fb816428db91a67ca))
* view document active status pmdk ([553032c](https://gitlab.com/maxxi-agro/atom/commit/553032c06667f6507a35ecc4d92d0b139b89ba37))

## [1.12.0](https://gitlab.com/maxxi-agro/atom/compare/v1.11.0...v1.12.0) (2025-04-07)


### Features:

* **6594:** add promag form service ([933b598](https://gitlab.com/maxxi-agro/atom/commit/933b5988d4424f70e599a8f0c355b71f9e33027b))
* detail promag template ([136f1b4](https://gitlab.com/maxxi-agro/atom/commit/136f1b43c8cc38d644e78a94300c0dc03586641c))
* discount purchase form - discount term section get order product ([9cc7ef8](https://gitlab.com/maxxi-agro/atom/commit/9cc7ef858c01a80ab951a2ecca455cff1f2604a3))
* document upload component enhance handler file image - pdf ([defe4ab](https://gitlab.com/maxxi-agro/atom/commit/defe4ab9110a755431dca9c718db2d4fef2bf470))
* document upload component handle drop event - set image preview ([5b5c738](https://gitlab.com/maxxi-agro/atom/commit/5b5c738f1c6c3bcb3c03366ae7cdd71dc72d2671))
* Export Import ([0c383fc](https://gitlab.com/maxxi-agro/atom/commit/0c383fc4d5e4b1c240c23befbe068281bfd3cace))
* Export Import area ([1818cf1](https://gitlab.com/maxxi-agro/atom/commit/1818cf1318aab14edb936836a579546302900d5d))
* Export Import area ([6894d93](https://gitlab.com/maxxi-agro/atom/commit/6894d938384ed35561ab41afd68ce00c1d5e7451))
* Export Import area ([4ca5853](https://gitlab.com/maxxi-agro/atom/commit/4ca5853720d19acbc506bf5d787edcf039926154))
* fix data type of export import area ([c6af612](https://gitlab.com/maxxi-agro/atom/commit/c6af61244b82f8498aa2fc06716faccad8b7a0ce))
* get sales discount in promag service ([73e6069](https://gitlab.com/maxxi-agro/atom/commit/73e6069da2637d3ac9b3bd598330f15ab05895d9))
* Integration Page Detail Program Marketing Tab List PO ([31d20be](https://gitlab.com/maxxi-agro/atom/commit/31d20be030fb982cb0c3c3004ecf777ed6c6d8f7))
* Integration Page Detail Program Marketing Tab List PO ([513a2c6](https://gitlab.com/maxxi-agro/atom/commit/513a2c612c70384d5bcc811478dd39d123eb43d0))
* Integration Page Detail Program Marketing Tab List PO ([f78619d](https://gitlab.com/maxxi-agro/atom/commit/f78619d5021a40b49e43fa81ab8ade10c85e09a2))
* **Modal Selector:** component modal program type selector ([26549b2](https://gitlab.com/maxxi-agro/atom/commit/26549b2279f297b677729dfab5911a617b38ec12))
* **MTMAI-6494:** slicing modal confirmation one shoot ([31fb922](https://gitlab.com/maxxi-agro/atom/commit/31fb9220b6317413c433a16ea0a7fec2766b0393))
* **MTMAI-6496:** integration one shoot ([8238ab9](https://gitlab.com/maxxi-agro/atom/commit/8238ab99aba9cf857e781bd863384148d8e83f82))
* **MTMAI-6502:** integration edit one shoot ([f489f84](https://gitlab.com/maxxi-agro/atom/commit/f489f84d79245b78e3f7f08ce4b2f0a8375edcb3))
* **MTMAI-6513, MTMAI-6529:** approval promag section discount term ([c002802](https://gitlab.com/maxxi-agro/atom/commit/c0028022f5f52e4d485a8d6168ac8faef0357f4f))
* **MTMAI-6513:** add informasi kompensasi section card component ([af390d0](https://gitlab.com/maxxi-agro/atom/commit/af390d01845222aa58edfc14a6911d549d4c7dca))
* **MTMAI-6513:** add reward term card section data ([13f1ae7](https://gitlab.com/maxxi-agro/atom/commit/13f1ae7bf4b7fdaa02b74681727446278a701430))
* **MTMAI-6513:** discount term section card data ([b77d8c7](https://gitlab.com/maxxi-agro/atom/commit/b77d8c7312db2f43e61c856674aa865e4b71289b))
* **MTMAI-6523:** slicing detail program marketing template ([bdfbfd8](https://gitlab.com/maxxi-agro/atom/commit/bdfbfd8243919b7740428fcfee685a04d6d32d1b))
* **MTMAI-6529:** add multi type document preview component ([a06d15d](https://gitlab.com/maxxi-agro/atom/commit/a06d15d363e1695931b6b5265e270cba0439332b))
* **MTMAI-6529:** discount product table data - modal confirm ([1b11acc](https://gitlab.com/maxxi-agro/atom/commit/1b11acc96c5f0bab560bf055cc3d3a7c8186c88d))
* **MTMAI-6529:** discount term section promag type discount purchase - modal confirmation ([497f53d](https://gitlab.com/maxxi-agro/atom/commit/497f53d0354c84ce3214af377993fd48bccd6c48))
* **MTMAI-6529:** submit approval promag payload section enhancement ([6c64309](https://gitlab.com/maxxi-agro/atom/commit/6c643091cfc037e81ebfc8ba4fdac6ec0b4c6c83))
* **MTMAI-6529:** switching section content by program type - modal confirm ([302e727](https://gitlab.com/maxxi-agro/atom/commit/302e7277530ecea12e65b7399b4b1e92833ecda8))
* **MTMAI-6578:** filter program marketing list - add program type ([b4f5abb](https://gitlab.com/maxxi-agro/atom/commit/b4f5abbf4fda6eaf4efff1c688e5d5b27b1762cc))
* **MTMAI-6582:** enhance section informasi program - multiple document upload ([3a76394](https://gitlab.com/maxxi-agro/atom/commit/3a76394d580434d18a61024e68d3c3e8435403ca))
* **MTMAI-6583:** slicing section ketentuan program ([bc4aa8c](https://gitlab.com/maxxi-agro/atom/commit/bc4aa8ce728c7d283f1aa7b9aae790f2195d1ed4))
* **MTMAI-6583:** slicing section ketentuan program ([8437ab6](https://gitlab.com/maxxi-agro/atom/commit/8437ab6c54d427f5237a6cdbf51d16ec8bde7065))
* **MTMAI-6584, MTMAI-6587:** integration and enhance search get component input list box ([8478dd9](https://gitlab.com/maxxi-agro/atom/commit/8478dd9c353dd35a299c884fd39019128a45021d))
* **MTMAI-6586:** slicing form ketentuan pembelian ([1f1a35a](https://gitlab.com/maxxi-agro/atom/commit/1f1a35a2438e019d9acd99e4a12773cf1c2eeb71))
* **MTMAI-6589, MTMAI-6590:** enhance discount type per product ([a49bc69](https://gitlab.com/maxxi-agro/atom/commit/a49bc6942f6959909df59d7b933c49b3acf92568))
* **MTMAI-6589, MTMAI-6590:** section form ketentuan diskon - enhance input select discount type ([4708271](https://gitlab.com/maxxi-agro/atom/commit/47082716519bfe0d4a496fd6dc445f715e326569))
* **MTMAI-6591:** discount form add input select discount type component ([c67295d](https://gitlab.com/maxxi-agro/atom/commit/c67295df02130e0151584662d2b2747fb086f4cc))
* **MTMAI-6591:** discount term section form - input discount category component ([de47839](https://gitlab.com/maxxi-agro/atom/commit/de478391b64e8024a1ba824a60657e4ea0e1ba00))
* **MTMAI-6592:** slicing ketentuan hadiah ([623feaa](https://gitlab.com/maxxi-agro/atom/commit/623feaa8680a23178694fbdd910d4c274acd0a18))
* **MTMAI-6592:** slicing ketentuan hadiah & input list box distributor ([6ed73bf](https://gitlab.com/maxxi-agro/atom/commit/6ed73bf8a1a828e53c829eb6ff84cb60dd38363d))
* **MTMAI-6594:** ketentuan diskon - table data add/remove selected product list ([5901aa3](https://gitlab.com/maxxi-agro/atom/commit/5901aa325151cbe432edf389bd84d51ea3202404))
* **MTMAI-6594:** promag service get/set section program term form ([65c625e](https://gitlab.com/maxxi-agro/atom/commit/65c625e9d950d76494865d41a1b61b55d7cebdb3))
* **MTMAI-6595, MTMAI-6596, MTMAI-6593:** slicing section informasi kompensasi & integration ([6ac3e51](https://gitlab.com/maxxi-agro/atom/commit/6ac3e5176425ac557caa04eba69b442fec3cff1e))
* **MTMAI-6599, MTMAI-6613:** modal confirmation promag and form kompensasi product ([3741dad](https://gitlab.com/maxxi-agro/atom/commit/3741dad8f20fbde6fb31f054eed1c292453834f6))
* **MTMAI-6609:** slicing modal confirmation for type product discount ([2952924](https://gitlab.com/maxxi-agro/atom/commit/295292494e67204e955c5b0bdabe858f54091298))
* **MTMAI-6611:** set values discount term form - map products data ([3d5a012](https://gitlab.com/maxxi-agro/atom/commit/3d5a01253185f236663007d3de86801bb488a583))
* **MTMAI-6612:** enhance handle payload create promag - type discount purchase ([bc0acea](https://gitlab.com/maxxi-agro/atom/commit/bc0acea6d49c33627c11cacd1cef757fcf6da833))
* **MTMAI-6612:** enhance submit promag discount purchase - confirm modal section discount ([750dfa7](https://gitlab.com/maxxi-agro/atom/commit/750dfa7bf452736d4b71e66b26efdddb63656363))
* **MTMAI-6614:** integration kompensasi product & validate form one area ([a06880d](https://gitlab.com/maxxi-agro/atom/commit/a06880deac306260243e8f30acf61beffffb6cf0))
* **MTMAI-6615:** integration update product compensation ([30ae606](https://gitlab.com/maxxi-agro/atom/commit/30ae6061bffb447321a726627fc0df8c8de944d4))
* **MTMAI-6620, MTMAI-6621:** integration edit discount product ([d2f6cc7](https://gitlab.com/maxxi-agro/atom/commit/d2f6cc76ad583cc9d65b48cfae433943a076c9ba))
* **MTMAI-6620:** integration create discount product ([6e89cad](https://gitlab.com/maxxi-agro/atom/commit/6e89cadc590e0950052d674196352fef5c569585))
* **MTMAI-6626:** get list warehouse - modal warehouse selector ([df476b8](https://gitlab.com/maxxi-agro/atom/commit/df476b8be0f671c388cf6a499588b23ba014615d))
* **MTMAI-6628:** tab list spm ([709b6a5](https://gitlab.com/maxxi-agro/atom/commit/709b6a5e59456840d8b5d049430813cc44940a22))
* **MTMAI-6631:** slicing and integration form create spm from program marketing ([b8f8417](https://gitlab.com/maxxi-agro/atom/commit/b8f8417d1bfff744c8bcf08aade210562e3fe584))
* **MTMAI-6632:** slicing @ integration detail kompensasi ([e810840](https://gitlab.com/maxxi-agro/atom/commit/e810840a08a7a6051b16c13325db7ed9ec9887e0))
* **MTMAI-6632:** slicing integrasi detail compensation ([781d548](https://gitlab.com/maxxi-agro/atom/commit/781d548be975d385aaae26561c1780217ce65477))
* **MTMAI-7060:** fix title approval & loading submit response form promag all type ([89c1728](https://gitlab.com/maxxi-agro/atom/commit/89c1728d86214794dfc599ad90dd10d114121269))
* payload form ([7800628](https://gitlab.com/maxxi-agro/atom/commit/7800628ee083055f6d19108ee0d07c5ee79886c4))
* **Program Marketing Form:** add form sections component ([bd79a20](https://gitlab.com/maxxi-agro/atom/commit/bd79a20fc3c1fa1967a2d79fd6892b4c565964d7))
* program marketing information detail modal in purchase order & create so ([3da9570](https://gitlab.com/maxxi-agro/atom/commit/3da9570f9f874f30fe6788119993e507f5113857))
* **Promag Form:** section form information - add single input datepicker component ([2727e56](https://gitlab.com/maxxi-agro/atom/commit/2727e56f79477a9ae2ba8abad2ae50edd6745c21))
* **Promag List:** filter list add tipe program pills ([ccd1130](https://gitlab.com/maxxi-agro/atom/commit/ccd1130d14dbbd96931d60f05fc451692a7b9250))
* Remove Max Diskon Level 3 ([4e20e56](https://gitlab.com/maxxi-agro/atom/commit/4e20e56b137b2a0b2e98bdd9fa3beb3d89179c29))
* routing area form import ([b5f33b0](https://gitlab.com/maxxi-agro/atom/commit/b5f33b0dd348475d9331ca7202ea647ab6520b33))
* **Sales Discount List:** remove sd lvl.3 information ([1d73c8b](https://gitlab.com/maxxi-agro/atom/commit/1d73c8bfd70ceff0d3c105bcade30508059e84a2))
* slicing form create spm compensation ([5969ad9](https://gitlab.com/maxxi-agro/atom/commit/5969ad96c2280623acbfbc565b87b6e7447564ce))
* Slicing Page Detail Program Marketing Tab List PO ([f0e9730](https://gitlab.com/maxxi-agro/atom/commit/f0e9730ba3cf4408c6efc778651ab64348ba134d))
* slicing promag ([da65e7e](https://gitlab.com/maxxi-agro/atom/commit/da65e7ed9877f65148205251f01310241bd8a89b))


### Bug Fixes:

* admin marketing role, edit cakupan user - assign area ([1d74dae](https://gitlab.com/maxxi-agro/atom/commit/1d74daea48cac9d339b93d0b607f4e510c1b0b6d))
* btn loading complete SO ([dd3273b](https://gitlab.com/maxxi-agro/atom/commit/dd3273b695338ada0ad81dfc7a8f05e7bfed2082))
* change params edit one shoot ([927ecc0](https://gitlab.com/maxxi-agro/atom/commit/927ecc0828dc48eac15ba026a3a65ca5414b5810))
* change params edit one shoot ([c8620b4](https://gitlab.com/maxxi-agro/atom/commit/c8620b4255a5cac741c004687ec7a2306d672122))
* delete btn buat spm in page approval type compensation ([a8354dc](https://gitlab.com/maxxi-agro/atom/commit/a8354dc24ccbddbe0bbbd7ce80c7e587d2560d4d))
* delete validate stock in create sales order ([329a456](https://gitlab.com/maxxi-agro/atom/commit/329a4568c2942d5c5f83115e7dd3fb57437c8c35))
* disabled nominal discount term ([9621b40](https://gitlab.com/maxxi-agro/atom/commit/9621b4057df57986812df20fff117d8f910fa529))
* disabled nominal discount term ([9cb2c66](https://gitlab.com/maxxi-agro/atom/commit/9cb2c66ea2d5b475216a3d27c16b969b48a95709))
* discount product - add/remove table product list ([2d621d7](https://gitlab.com/maxxi-agro/atom/commit/2d621d7e055a6571e227a54674ac1a72178ab3d6))
* discount product list - check field  maximum discount input ([61a3ae1](https://gitlab.com/maxxi-agro/atom/commit/61a3ae19cefa3e00a42610393cb807a3536a8084))
* discount term form in type discount purchase program marketing ([d62d910](https://gitlab.com/maxxi-agro/atom/commit/d62d910366eacea876d4f6d9ae7b6da9d0045435))
* discount term section remove dummy products ([fa536c9](https://gitlab.com/maxxi-agro/atom/commit/fa536c98185a16f142dbe124777a3d3fd0bea784))
* double note view in list product SO ([e955fb3](https://gitlab.com/maxxi-agro/atom/commit/e955fb3e19e3af63585a47619e53a42ca8752307))
* form one shoot ([c8364b6](https://gitlab.com/maxxi-agro/atom/commit/c8364b64aef6ea6c0e773ee867de7451d0ae0d8f))
* formatInternationalNumber value in discount term ([9ed244d](https://gitlab.com/maxxi-agro/atom/commit/9ed244d48afc731c9348651a7f768b88bef3eb62))
* handle data discount term section per type ([e166ebf](https://gitlab.com/maxxi-agro/atom/commit/e166ebf5be68f1173f98b6fae19fbd620bdb4f2b))
* handle data product add form discount product term ([aeb35d5](https://gitlab.com/maxxi-agro/atom/commit/aeb35d5beea1fd9ac4d3fdd89f4e7b69a0d6ba37))
* handle delete product ([a674424](https://gitlab.com/maxxi-agro/atom/commit/a674424fa35f37f5fc86f88e7d4894a3e72c7aa3))
* hide periode in modal confirmation if product compensation ([af414cc](https://gitlab.com/maxxi-agro/atom/commit/af414cc1a106a956ee0ded2953b3d44ac5423642))
* label note view response success save SPM ([09c60a5](https://gitlab.com/maxxi-agro/atom/commit/09c60a508c9692e536abf120278040cce9539f21))
* label per product in edit form type one shoot ([8febd39](https://gitlab.com/maxxi-agro/atom/commit/8febd3922e0e95d569e45d137351f63fc3c629f9))
* **MTMAI-6529:** payload order term number format ([9f13ca1](https://gitlab.com/maxxi-agro/atom/commit/9f13ca1a09b37f60709072a5a0e272559a69fb9e))
* **MTMAI-6594:** discount term section products map to payload ([c431b62](https://gitlab.com/maxxi-agro/atom/commit/c431b620603e3c28d471634115a4ba9963ebf0bf))
* **MTMAI-6611:** detail promag - noteview card correction add type section ([53be6e9](https://gitlab.com/maxxi-agro/atom/commit/53be6e9e359ffbeb499cf1b7656eff6c37f4960e))
* **MTMAI-6611:** display discount purchase product name ([88f8d7e](https://gitlab.com/maxxi-agro/atom/commit/88f8d7ec051ae7149f2ac2a21a3f0b6fe24afd00))
* **MTMAI-6611:** handle add product from order term section ([2f9539e](https://gitlab.com/maxxi-agro/atom/commit/2f9539ec9f1d73c035010b2d25a9058b4b6391b3))
* **MTMAI-7062:** filter list component - reset filter program type ([df05e72](https://gitlab.com/maxxi-agro/atom/commit/df05e727d8a53ce8d8f0836a438bc420974a5979))
* **MTMAI-7062:** filter list query param program type ([045040a](https://gitlab.com/maxxi-agro/atom/commit/045040aebd316bcc938c552640d15f6549c6bcb4))
* **MTMAI-7064:** filter list promag date range - remove max date ([793a826](https://gitlab.com/maxxi-agro/atom/commit/793a826f2dde7ddc4001a531576f0c7be60b0e29))
* **MTMAI-7091, MTMAI-7107:** remake html checkbox for select distributor and sub area ([67e8025](https://gitlab.com/maxxi-agro/atom/commit/67e802586d1dc49d63d3e82bc782d15588b15797))
* **MTMAI-7092:** create program - modal confirm discount term render product list props ([638ede9](https://gitlab.com/maxxi-agro/atom/commit/638ede93151e798c65a33bae507c5b688cafa080))
* **MTMAI-7106:** display minimum order ([bf6492e](https://gitlab.com/maxxi-agro/atom/commit/bf6492e36aeb4a061854aac08b5ac9da660817b7))
* **MTMAI-7107, MTMAI-7158:** payload distributor & get area national form one shoot ([966f4c8](https://gitlab.com/maxxi-agro/atom/commit/966f4c836ad047e808c611c2ea55848b7dc04f57))
* **MTMAI-7143:** input list box group - handle remove chips ([eb90d8c](https://gitlab.com/maxxi-agro/atom/commit/eb90d8c47b0b9d938404279d2a73cde92c97594a))
* **MTMAI-7143:** pagination scroll product group brand & variant ([a3dee60](https://gitlab.com/maxxi-agro/atom/commit/a3dee607589d394b5291864ff444c2ced0ed625f))
* **MTMAI-7150:** input select product reward - pagination scroll ([74d02b5](https://gitlab.com/maxxi-agro/atom/commit/74d02b57b14b643a1e719342cc92dbdd02903759))
* **MTMAI-7150:** input select produk hadiah - component fix search pagination ([1fa8521](https://gitlab.com/maxxi-agro/atom/commit/1fa8521975da560a02970d6d23f0cffeecb437ed))
* **MTMAI-7159:** handle validate sales discount - program oneshoot; discount term form section ([96aab9d](https://gitlab.com/maxxi-agro/atom/commit/96aab9d5cabc31b132d62d0e0aba4e24a234d1d6))
* **MTMAI-7159:** noteview max discount template switch; promag oneshoot, promag discount purchase ([41a9507](https://gitlab.com/maxxi-agro/atom/commit/41a9507684bd7e2ba7fd7f228452ed20b888900a))
* **MTMAI-7159:** validate sales discount - program discount purchase; discount term form section ([b013944](https://gitlab.com/maxxi-agro/atom/commit/b0139448bc92f42352cc795c3b28216a0755a2bc))
* **MTMAI-7164, MTMAI-7162:** payload form & delivery unit ([db1ea3e](https://gitlab.com/maxxi-agro/atom/commit/db1ea3e3f5e68744d40d84f669448b4e420eb861))
* **MTMAI-7166:** discount term form - input add products merge selected table data value ([cacd038](https://gitlab.com/maxxi-agro/atom/commit/cacd038b1e7ffb0644d9d81f109a49e6aefa4d39))
* **MTMAI-7167:** remove options maximal sales discount lv.2 - discount type nominal ([6929607](https://gitlab.com/maxxi-agro/atom/commit/69296074b3bb70111a75c618d9cb50632a94b16b))
* **MTMAI-7239:** payload variant reward ([45ae6b8](https://gitlab.com/maxxi-agro/atom/commit/45ae6b89b820edd709f95f3b58825cb9619fe685))
* **MTMAI-7259, MTMAI-7251:** qouta sub area display & display product reward ([b38cd33](https://gitlab.com/maxxi-agro/atom/commit/b38cd332665d3b90eab4770d8f6325149c91a340))
* **MTMAI-7259:** generate scope list per scope ([538158c](https://gitlab.com/maxxi-agro/atom/commit/538158ce7f9fcb7b8ac5960d418260dee248c64e))
* **MTMAI-7261:** validation form purchase discount ([52443b2](https://gitlab.com/maxxi-agro/atom/commit/52443b21006721a1a42c8e761cefc3893d1f1810))
* **MTMAI-7275, MTMAI-7274, MTMAI-7273:** fix form update discount term ([8a1cf10](https://gitlab.com/maxxi-agro/atom/commit/8a1cf106015d09ec94ba8dd368592ed13e97f73e))
* **MTMAI-7276:** edit promag endpoint ([c8eef99](https://gitlab.com/maxxi-agro/atom/commit/c8eef99679f46d2eb58ba96f7edc3dc10b8d2f90))
* **MTMAI-7282, MTMAI-7270, MTMAI-7269, MTMAI-7153:** fix form one shoot update ([ff7c3fe](https://gitlab.com/maxxi-agro/atom/commit/ff7c3fedf1cb86d8c6808efb99f55cb2a7d49986))
* **MTMAI-7286, MTMAI-7279:** fix option distributor ([f6783bc](https://gitlab.com/maxxi-agro/atom/commit/f6783bc759575ba9550c4233e58e45f95049be2f))
* **MTMAI-7295:** handle option quota type for section program term ([2edc5b6](https://gitlab.com/maxxi-agro/atom/commit/2edc5b65817f300ca0748559cee05819ef44ea5a))
* **MTMAI-7353:** validate confirmation approval detail program marketing ([492a36a](https://gitlab.com/maxxi-agro/atom/commit/492a36a92e7e762b710a596c374cd2e79b6837d5))
* **MTMAI-7361, MTMAI-7372:** validation discount in discount term section ([a01ae79](https://gitlab.com/maxxi-agro/atom/commit/a01ae79626f79db6845b839d35730e9db9553786))
* **MTMAI-7382:** noteview available stock create spm compensation ([9475c1f](https://gitlab.com/maxxi-agro/atom/commit/9475c1f96bab0ee3c75fd41a11f0637cec6a1d18))
* **MTMAI-7397:** show hide btn for create spm compensation ([2130e79](https://gitlab.com/maxxi-agro/atom/commit/2130e791ebd879e793499649fb1ef44fb363d1a3))
* **MTMAI-7401:** flip flop endpoint for cancel spm ([1c040bf](https://gitlab.com/maxxi-agro/atom/commit/1c040bf544cd5baf18a2d3558d45e6e5d79e60f6))
* onDiscountScopeChange - add/remove validator ([84a8849](https://gitlab.com/maxxi-agro/atom/commit/84a88493f4cbfaf9458e91ee01be97699ddb3224))
* payload distributor & sub area ([4b871e9](https://gitlab.com/maxxi-agro/atom/commit/4b871e98b7d44a4887b4888a66bbf73289f72931))
* payload reward variant id ([0723180](https://gitlab.com/maxxi-agro/atom/commit/0723180f43f3606a27bac598fa83add1a35e5bec))
* product order percentage discount - input value number formatting validator ([974b1cd](https://gitlab.com/maxxi-agro/atom/commit/974b1cdb3dda5c7f530e03ab424c2e7df2d603c6))
* program marketing enum missing import ([b4ce662](https://gitlab.com/maxxi-agro/atom/commit/b4ce6621bfa849c2b9d9a6368802f4c9a405392d))
* program term scope - show cakupan semua(nasional) ([615e675](https://gitlab.com/maxxi-agro/atom/commit/615e67559d1c99f2a4d5153ea53d67a0c003471f))
* promag program term scope payload - map subarea id value ([3a66a1a](https://gitlab.com/maxxi-agro/atom/commit/3a66a1a5c40200c12861ff5d2799877a15f1cfdc))
* revision note reward & value label distributor compensation ([23e805b](https://gitlab.com/maxxi-agro/atom/commit/23e805ba8d35520326dac88a301539202716dd11))
* reward non mai program marketing ([d1e2d86](https://gitlab.com/maxxi-agro/atom/commit/d1e2d863ef5c805567cc38f0e925cf1344c970b4))
* reward noteview promag ([e5e7000](https://gitlab.com/maxxi-agro/atom/commit/e5e70002b71ff6f8ade1561f7f83d54a47b128ae))
* selection checkbox discount term add product ([c35493c](https://gitlab.com/maxxi-agro/atom/commit/c35493c46aefb7532ee5a3bd766d6a12f37e644c))
* show hide detail spm for compensation ([e8f9146](https://gitlab.com/maxxi-agro/atom/commit/e8f9146a647c43a9234400fc5a50dc41465f44b3))
* table action detail spm for status ready for delivery ([2e562ea](https://gitlab.com/maxxi-agro/atom/commit/2e562ea219fc58b661643dbcfb6c0753d9a969ab))
* table columns product - switch head title on discount type change ([9078382](https://gitlab.com/maxxi-agro/atom/commit/9078382bebba3c9d2c04cae1ba378ddb72d1f6c9))
* table product detail spm ([b73997d](https://gitlab.com/maxxi-agro/atom/commit/b73997dc289e4384a3cf207a9d73b0b050b1f81f))
* uploader preview template switch on removing doc file ([e62bf96](https://gitlab.com/maxxi-agro/atom/commit/e62bf9665c70f01941ca8813119b9556920458e6))
* validate form promag enable/disable submit button - remove unused ([0d8f5da](https://gitlab.com/maxxi-agro/atom/commit/0d8f5daf7accc1bec98121d868cf9c7da1b28073))
* WIP discount product - validator minimum percentage by sales discount ([841198b](https://gitlab.com/maxxi-agro/atom/commit/841198b1e82d62a038951ad391678af6a272a8f2))
* wip handle validate sales discount ([e8ede50](https://gitlab.com/maxxi-agro/atom/commit/e8ede5035a4d5b45ec2eb0695b86a7f24ab6a48e))
* wip order term form - input list box group behavior ([e82873f](https://gitlab.com/maxxi-agro/atom/commit/e82873f26ae77b1450b6391b568e4d1080f7ae97))
* WIP table data detail promag approval section discount term ([174f606](https://gitlab.com/maxxi-agro/atom/commit/174f6060aec5f3faab06ba134810bc07e5fd3ab0))

## [1.11.0](https://gitlab.com/maxxi-agro/atom/compare/v1.10.0...v1.11.0) (2025-01-20)


### Features:

* change menu reward ([2bc87fc](https://gitlab.com/maxxi-agro/atom/commit/2bc87fc9738e142523422a555a4b0ae5b87362ab))
* component image cropper ([079af82](https://gitlab.com/maxxi-agro/atom/commit/079af825462e2dc972a783e8eb1c0705572707d7))
* component image cropper ([3017647](https://gitlab.com/maxxi-agro/atom/commit/30176475a41a5eb5ca5524fbce4301280c68e439))
* embed video input - youtb iframe ([1bf0e18](https://gitlab.com/maxxi-agro/atom/commit/1bf0e1861fc1ffcfc0622a68ada2bdd18596b543))
* **MTMAI-6270, MTMAI-6277:** slicing level & retailer reward ([6759c3a](https://gitlab.com/maxxi-agro/atom/commit/6759c3aa5665506b576a22d2975f535ea4bac09e))
* **MTMAI-6282:** slicing detail product reward ([52e3675](https://gitlab.com/maxxi-agro/atom/commit/52e36754a921016157303347e64ce61631fff1d3))
* **MTMAI-6287:** form payload ([79fdbaf](https://gitlab.com/maxxi-agro/atom/commit/79fdbaf002febf053eb0b636609f011fab5033e5))
* **MTMAI-6287:** slicing form ([d9b1c3a](https://gitlab.com/maxxi-agro/atom/commit/d9b1c3ac4ef2260e95c1792cb500c7f70e9ab7f7))
* **MTMAI-6288, MTMAI-6283, MTMAI-6278:** integration form ([9990657](https://gitlab.com/maxxi-agro/atom/commit/99906574d1fd8d3cf8df867308ce713722cfd457))


### Bug Fixes:

* **Area Settings:** privilege cta area/team detail ([84b0cd8](https://gitlab.com/maxxi-agro/atom/commit/84b0cd83261d45327768c83bfe9e08455d02ba0f))
* card-tab-section handle delivery address card group ([f0451a7](https://gitlab.com/maxxi-agro/atom/commit/f0451a7cf7557e5a7a3f50bc03e2411b965de64c))
* **Detail Distributor:** PE admin marketing rbac ([fd42b85](https://gitlab.com/maxxi-agro/atom/commit/fd42b85c2b7a96794ecc85fe4abc4321aee75a0c))
* **Detail SO:** cta privilege create spm ([3270b9e](https://gitlab.com/maxxi-agro/atom/commit/3270b9e44db1e5683ed25f4bac87bc69e75de65d))
* **Detail User:** privilege cta activate user ([ad01a64](https://gitlab.com/maxxi-agro/atom/commit/ad01a646aed6ee42529afa3abd4f73cadb895119))
* **Detail User:** privilege cta user detail actions menu ([1c7c8a5](https://gitlab.com/maxxi-agro/atom/commit/1c7c8a546ca9a09efccc820aa4a01b4728774a7e))
* **image-cropper:** blob to file upload ([c0cdc67](https://gitlab.com/maxxi-agro/atom/commit/c0cdc678d9b69914354e44c35aa73f4d6f2e4272))
* **Product Catalog:** privilege cta add/download batch ([b9fed2e](https://gitlab.com/maxxi-agro/atom/commit/b9fed2e25f337eb6b948f133745af92819748983))
* **Profile Header:** render user status string ([8a99939](https://gitlab.com/maxxi-agro/atom/commit/8a9993957a13de5c47df96379451840c9d79f01d))
* **QR Product:** privilege cta add/download batch ([e2b7dae](https://gitlab.com/maxxi-agro/atom/commit/e2b7dae9daf9fefa8fd4473eacd91e1eeaf2ddf9))
* vercel script run env ([54b408f](https://gitlab.com/maxxi-agro/atom/commit/54b408ff62d0f7e0ca87600ee0096e2f66def6e9))
* wip tab-alamat-pengiriman ([7a72e76](https://gitlab.com/maxxi-agro/atom/commit/7a72e769569852de2e6fabf01c37ba27275bf2bd))

## [1.10.0](https://gitlab.com/maxxi-agro/atom/compare/v1.9.0...v1.10.0) (2024-12-31)


### Features:

* add custom action input qr code - retailer detail terverifikasi ([86bf840](https://gitlab.com/maxxi-agro/atom/commit/86bf8402fd3f852010793efda2a5880353046e9f))
* **Detail Distributor Active:** tab product exclusive; add noteview sub-area changes ([d75563a](https://gitlab.com/maxxi-agro/atom/commit/d75563a268812a38ca4729c44a71e2a2a948ebea))
* enhance component for tidak ada perubahan ([653480b](https://gitlab.com/maxxi-agro/atom/commit/653480bc75ef2ccd3db61c14128419a7994e2274))
* handle response submit registration distributor ([808125e](https://gitlab.com/maxxi-agro/atom/commit/808125ee60477f7d7d399f7d99e40defa40c87d1))
* integration pendaftaran diproses admin ([dda7611](https://gitlab.com/maxxi-agro/atom/commit/dda7611ed900eba06415113f39c621af45932008))
* integration plafon kredit ([fe11127](https://gitlab.com/maxxi-agro/atom/commit/fe111277c04ffb0c15818a8416f655091e48282a))
* integration post edit request by admin ([0ae44b7](https://gitlab.com/maxxi-agro/atom/commit/0ae44b7c5fc11a920ef705b8a7cf1e29e66ddd96))
* modal response succes edit request admin ([c549b83](https://gitlab.com/maxxi-agro/atom/commit/c549b833a2c7459d12da30b059be9843a81c5f2c))
* **MTMAI-5665:** integration data distributor ([76f62c3](https://gitlab.com/maxxi-agro/atom/commit/76f62c38d6ca95eaf4946c9f21226d2aa944a135))
* **MTMAI-5666:** Slicing data usaha distributor ([61520ea](https://gitlab.com/maxxi-agro/atom/commit/61520ea4f045ad07e301a083122007bbc438a752))
* **MTMAI-5667:** integration data usaha distributor ([e92087b](https://gitlab.com/maxxi-agro/atom/commit/e92087b4457524d37e437e1fa7e4aa76d30488a6))
* **MTMAI-5668:** slicing alamat pengiriman distributor ([4db30a3](https://gitlab.com/maxxi-agro/atom/commit/4db30a30ddbab0dc2cd98891630b789d877bcfeb))
* **MTMAI-5669:** integration alamat pengiriman ([d928b88](https://gitlab.com/maxxi-agro/atom/commit/d928b882ac062c8286291a0c5f2da04906074678))
* **MTMAI-5670:** slicing plafon kredit distributor ([bcf2366](https://gitlab.com/maxxi-agro/atom/commit/bcf2366367c0fbb604190a637413d054d80f5271))
* **MTMAI-5671:** integration plafon kredit & change name enum verification ([a67dc1c](https://gitlab.com/maxxi-agro/atom/commit/a67dc1c28bd1c488f44cb19828f0257b9b17736c))
* **MTMAI-5672:** slicing document distributor ([f1f0e41](https://gitlab.com/maxxi-agro/atom/commit/f1f0e41e02bc0062700de62332c0d08fcd986374))
* **MTMAI-5673:** integration document distributor ([ceddd4a](https://gitlab.com/maxxi-agro/atom/commit/ceddd4a9f38fd385171caef10417283957b8a3c5))
* **MTMAI-5685:** detail section card data - integrate get detail information distributor ([0cf6759](https://gitlab.com/maxxi-agro/atom/commit/0cf675991c0e592e47b2b5e7f6029ed8be03fb49))
* **MTMAI-5688, MTMAI-5702:** integrate detail piutang modal data ([f5b2677](https://gitlab.com/maxxi-agro/atom/commit/f5b26777aa62fd80f3bb8040bb12ff62c417f0fe))
* **MTMAI-5688:** distributor detail edit-request - integrate api data tab Informasi Distributor ([f24d50a](https://gitlab.com/maxxi-agro/atom/commit/f24d50a70d52b995bb502ba7d45b1e07922f65ab))
* **MTMAI-5688:** submission finance approval edit request ([3b90975](https://gitlab.com/maxxi-agro/atom/commit/3b9097593243e406db8566567388cc1887a98211))
* **MTMAI-5693:** detail active ([82bc681](https://gitlab.com/maxxi-agro/atom/commit/82bc681eccbe873dde0d2d07bb5c63b353745e17))
* **MTMAI-5693:** detail integration log distributor ([73269a5](https://gitlab.com/maxxi-agro/atom/commit/73269a5b41684bb7fbe4ac83f7d1cc0343fdd221))
* **MTMAI-5693:** detail perubahan data diproses distributor ([7f5159e](https://gitlab.com/maxxi-agro/atom/commit/7f5159e9b7b5f6fa373aa8964648241e6db99127))
* **MTMAI-5693:** detail perubahan data diproses distributor ([2c2e66e](https://gitlab.com/maxxi-agro/atom/commit/2c2e66e9fde1c9cdefe0d5dd22e66f7315d98b50))
* **MTMAI-5693:** integration detail active and KUL ([5792304](https://gitlab.com/maxxi-agro/atom/commit/57923044078e6d1148b0603b38d85110acdfdf82))
* **MTMAI-5694:** slicing detail log ([76f316d](https://gitlab.com/maxxi-agro/atom/commit/76f316df4b7914eab94a925f9a0bccff35535e51))
* **MTMAI-5700, MTMAI-5695:** integration detail distributor ([e869143](https://gitlab.com/maxxi-agro/atom/commit/e8691432dfc4709ca2973f9980b7a472772b6fc8))
* **MTMAI-5702:** Detail distrib verifikasi diproses - integrate tab informasi ([b311a5b](https://gitlab.com/maxxi-agro/atom/commit/b311a5bd23ffb5aaff07c053d2cf280eefbfe747))
* **MTMAI-5702:** integrate response modal data submit verification ([eb1173c](https://gitlab.com/maxxi-agro/atom/commit/eb1173ccd4c2b9f31fa4ed72cd5a107244eb0e01))
* **MTMAI-5707:** Distributor list - filter select area input ([3cb5ea8](https://gitlab.com/maxxi-agro/atom/commit/3cb5ea8ac1ce9eec1ded1cc5c70c03546dc09124))
* **MTMAI-5737, MTMAI-5664:** slicing detail distributor pending edit virification ([292a82a](https://gitlab.com/maxxi-agro/atom/commit/292a82a128f3bbea7037c422c112b00baeca6daa))
* **MTMAI-5799, MTMAI-5817, MTMAI-5820:** fix verification distributor ([72d4a68](https://gitlab.com/maxxi-agro/atom/commit/72d4a6853bc4d391c59f7b9ab94d212a20176286))
* revision document ([0623002](https://gitlab.com/maxxi-agro/atom/commit/062300250d95948e6a258243855f5240e90f1b21))


### Bug Fixes:

* approval finance - sections card npwp validate form data ([280e976](https://gitlab.com/maxxi-agro/atom/commit/280e976ed7bc059f5038a7f51b6eb271ff4f14a0))
* btn group verification ([69c5d08](https://gitlab.com/maxxi-agro/atom/commit/69c5d08da7495dfb0e0be4806e5e564e6d4f19aa))
* card-verification-form distributor-data detail log ([d13601e](https://gitlab.com/maxxi-agro/atom/commit/d13601eaaa40bf01b3b633ec3c9e762fd01057f4))
* change enum tab ([f146380](https://gitlab.com/maxxi-agro/atom/commit/f14638009b262ccba21a67e60100fd671e6e0818))
* color status in detail retailer ([b81ebec](https://gitlab.com/maxxi-agro/atom/commit/b81ebec9a956bf15b44c5d4d5fa943e7e52e2f77))
* **Create SPM:** so create spm payload not fulfilled ([fbd503b](https://gitlab.com/maxxi-agro/atom/commit/fbd503b3c097aa2dffdf06425d70883e78172108))
* description confirmation update data distributor ([86322b7](https://gitlab.com/maxxi-agro/atom/commit/86322b7c288b2e3abd2ffae7b55d2c0ecdb89c12))
* **Distributor Detail:** detail teregistrasi; tab plafon kredit handle data use new endpoint ([3673cad](https://gitlab.com/maxxi-agro/atom/commit/3673cade407a47de52f67fae0d6d40f3bc6ff0a3))
* **Distributor Detail:** path import update directory ([859c93f](https://gitlab.com/maxxi-agro/atom/commit/859c93f3054b294687eef786ec10f956678101c1))
* **Distributor List:** distributor pending list - handle init tab list data index ([2a8157c](https://gitlab.com/maxxi-agro/atom/commit/2a8157c060b91d52088608f07974b67b9f3f3110))
* edit-request finance confirm button privilege check ([2eb3d27](https://gitlab.com/maxxi-agro/atom/commit/2eb3d27d974600e8966deced9e9c5180fb07e3da))
* edit-request plafon kredit sections handle validate form ([5226109](https://gitlab.com/maxxi-agro/atom/commit/522610978f5b282207484d45439e241b43e6e6b3))
* edit-request plafon kredit sections handle validate form ([23e847e](https://gitlab.com/maxxi-agro/atom/commit/23e847e940488923b3312df0a962ceb945bb5ddf))
* icon steper if edited ([af3bdaf](https://gitlab.com/maxxi-agro/atom/commit/af3bdaf058d3550713f2a4180e0b1d9c305ed678))
* image akta pendirian not show ([2263ecf](https://gitlab.com/maxxi-agro/atom/commit/2263ecff2f615a4183b19fe99929dfb11e872f0b))
* **MTMAI-5795, MTMAI-5796:** privilege btn verification distributor & show alamat pengiriman ([bd9c58d](https://gitlab.com/maxxi-agro/atom/commit/bd9c58d7f182faa403b912366bf2eef31f88205b))
* **MTMAI-5798:** handling input note ([7f8e712](https://gitlab.com/maxxi-agro/atom/commit/7f8e712e24ce6a1a4a49f9ac57d171d7947c3a4c))
* **MTMAI-5801:** distributor tab log - link to detail log ([c88d5d5](https://gitlab.com/maxxi-agro/atom/commit/c88d5d5fc21d98a95fc6c993cb5e2eb93c67342e))
* **MTMAI-5802:** fix data branch ([dd4ce32](https://gitlab.com/maxxi-agro/atom/commit/dd4ce32a99eca957b23425ad68b28be296801f3f))
* **MTMAI-5806:** handle cta product exclusive setting form ([a9c847b](https://gitlab.com/maxxi-agro/atom/commit/a9c847bf4a98f61aeaa1b9bcf550696bdef67087))
* **MTMAI-5813:** verif kul. merge update - enhance enum revision session ([7619f89](https://gitlab.com/maxxi-agro/atom/commit/7619f8990a45e83569829dd47706346da6ca9c91))
* **MTMAI-5822:** fix payload ([bcffe15](https://gitlab.com/maxxi-agro/atom/commit/bcffe1592d224e0528cb0de66619f4915946e8e5))
* **MTMAI-5822:** fix payload and validation document update ([0563c67](https://gitlab.com/maxxi-agro/atom/commit/0563c67ea355fcde4faee28c4acc30a488a44c6f))
* **MTMAI-5825:** show btn verification with role ([b8f39bb](https://gitlab.com/maxxi-agro/atom/commit/b8f39bb7342a32e0071b925842cd79567c40b65b))
* **MTMAI-5827:** pinpoint alamat pengiriman ([f9669bb](https://gitlab.com/maxxi-agro/atom/commit/f9669bb111d2288b39bb24e1d2327fc552c07e1f))
* **MTMAI-5828:** approval finance - distributor registration; privilege confirm btn ([59881b9](https://gitlab.com/maxxi-agro/atom/commit/59881b9619f393d6619ce1204638e0d79615d15e))
* **MTMAI-5828:** authservice check role-privilege ([a63c5c1](https://gitlab.com/maxxi-agro/atom/commit/a63c5c19da59fba53ac913bf74c29fb3fab0e30f))
* **MTMAI-5830:** validation in shipping address ([fd2650b](https://gitlab.com/maxxi-agro/atom/commit/fd2650ba1275abe96ba1cc506121dc4af733b10b))
* **MTMAI-5831:** section card not showing - distrib regis approval finance ([6d0e9d0](https://gitlab.com/maxxi-agro/atom/commit/6d0e9d05408e7bc3137c4316b157bd37b94db853))
* **MTMAI-5832:** detail distrib menunggu kul - tab component data index ([13b2af4](https://gitlab.com/maxxi-agro/atom/commit/13b2af45a3ec4e3d360fc13b40d343234ee97440))
* **MTMAI-5836:** input revision document ([d53d69b](https://gitlab.com/maxxi-agro/atom/commit/d53d69b495e0aed9f96103b67845381231f70041))
* **MTMAI-5837:** section verified badge ([375294e](https://gitlab.com/maxxi-agro/atom/commit/375294e876aee5067b95c830e234b863361b8162))
* **MTMAI-5839:** payload perubahan data distributor ([fd1a778](https://gitlab.com/maxxi-agro/atom/commit/fd1a77811dbb207690928850ac966cc8105302c7))
* **MTMAI-5839:** payload perubahan data distributor ([6b84672](https://gitlab.com/maxxi-agro/atom/commit/6b8467271d335671d3881a939e85d3a1e467dbe1))
* **MTMAI-5963:** plafon kredit data mapping response key update ([2609700](https://gitlab.com/maxxi-agro/atom/commit/260970041553771812dd25bc0522ae0094ac585d))
* **MTMAI-5968:** redirect path detail reject ([1a0f679](https://gitlab.com/maxxi-agro/atom/commit/1a0f6798e610f860bdd05c868f58dd6f02aa23b4))
* **MTMAI-5968:** verifikasi kul modal confirm show revision note ([0cbf1c2](https://gitlab.com/maxxi-agro/atom/commit/0cbf1c2f6029bf5f68ad987fc358b1c325dad59b))
* **MTMAI-5971:** uncomment code ([a996f19](https://gitlab.com/maxxi-agro/atom/commit/a996f1986a5ec5353a21d2424032c933c0a97995))
* **MTMAI-5986:** card-verification component; revision data map duplicated ([e3f718b](https://gitlab.com/maxxi-agro/atom/commit/e3f718b42489c7402cae45169622edc7ac1f458e))
* **MTMAI-5989:** edit request admin - confirm modal ([9f52e37](https://gitlab.com/maxxi-agro/atom/commit/9f52e371d33a5c406ab952f0766541219f2059f6))
* **MTMAI-5989:** response reset password ([6ed6501](https://gitlab.com/maxxi-agro/atom/commit/6ed650174df90e21da9741cbeacf3333e22493a7))
* **MTMAI-5989:** response success for edit admin ([e9f9482](https://gitlab.com/maxxi-agro/atom/commit/e9f9482c481251b7a0e29270f81c566f718fb201))
* **MTMAI-5990:** detail distributor - tablist component index ([69fdbe7](https://gitlab.com/maxxi-agro/atom/commit/69fdbe7438069f526082f9615667b467e2cc093d))
* **MTMAI-5994, MTMAI-5993:** validation form shipping address ([15fa0e3](https://gitlab.com/maxxi-agro/atom/commit/15fa0e3f0b249648def3a59312211835343d0f3a))
* **MTMAI-6000:** view plafon credit ([e3e5404](https://gitlab.com/maxxi-agro/atom/commit/e3e5404fe1dfa3cd35c0b64ec9ef634680134e3d))
* **MTMAI-6001, MTMAI-6002, MTMAI-6005:** edit request ([30a9859](https://gitlab.com/maxxi-agro/atom/commit/30a98591e5edb5ac158f1c345f50194174c947fe))
* **MTMAI-6002:** fix disable btn form sub area ([1a48ccf](https://gitlab.com/maxxi-agro/atom/commit/1a48ccffb5048465dcdcfa5e6c3c190368b0e110))
* not have change section in detail log ([01bbde3](https://gitlab.com/maxxi-agro/atom/commit/01bbde33944d59079ef7d4961e2ef3b9d109d9dc))
* note revision ([d1f5503](https://gitlab.com/maxxi-agro/atom/commit/d1f5503a39075f237e532c9d84c68e491433938e))
* **Plafon Kredit:** tab plafon-kredit section data; format currency value ([7ea4f9e](https://gitlab.com/maxxi-agro/atom/commit/7ea4f9e92607a04f59640eee9fe9a76c87cca3ac))
* redirect edit request ([09e6456](https://gitlab.com/maxxi-agro/atom/commit/09e6456c5ce4aee252aeedf60b82aaaafccd81c7))
* redirect success edit request ([c6a90d6](https://gitlab.com/maxxi-agro/atom/commit/c6a90d663b03573dfea2ef8a09bbf9de6a99f58c))
* redirect success edit request ([315fda5](https://gitlab.com/maxxi-agro/atom/commit/315fda524c4575c0d924f2bf170639a0057e0e76))
* redirect success edit request ([0c2b516](https://gitlab.com/maxxi-agro/atom/commit/0c2b516e856a506222867a05b9912df2065f8343))
* registration approval finance tablist ([867bb31](https://gitlab.com/maxxi-agro/atom/commit/867bb3130856ef1a2ea0b9410b0ea5325f5cbdd8))
* revision value document ([c535d71](https://gitlab.com/maxxi-agro/atom/commit/c535d718675ecd8c834a4447f80391e49fa2c2fe))
* vercel build cmd ([f75ede4](https://gitlab.com/maxxi-agro/atom/commit/f75ede4f424d9358624d30c41d567359a3dfd099))
* vercel.sh staging run cmd ([2b575ac](https://gitlab.com/maxxi-agro/atom/commit/2b575ac324b6dd3e63677dd6ac4913b3e3691293))
* verification new data in delivery address ([ee844df](https://gitlab.com/maxxi-agro/atom/commit/ee844dff9549ce1ec5330e4c5c63f9b62518432b))