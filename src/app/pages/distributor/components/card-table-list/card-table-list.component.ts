import { AfterViewInit, Component, EventEmitter, Input, OnDestroy, OnInit, Output, ViewChild } from '@angular/core';
import { MatSort, Sort } from '@angular/material/sort';
import { BaseDatasource } from '@shared/base/base.datasource';
import { BehaviorSubject, Subscription } from 'rxjs';
import { STRING_CONSTANTS } from '@config/constants/string.constants';
import { TableColumn } from '@shared/interface/table.interface';
import { ActivatedRoute, Router } from '@angular/router';
import { UrlUtilsService } from '@utils/url-utils.service';
import { BaseTableService } from '@shared/base/base-table-service.service';
import { UtilitiesService } from '@services/utilities.service';
import { DistributorService } from '../../distributor.service';
import { FilterService } from '@services/filter.service';
import { IListDistributor } from '../../interfaces/distributor.interface';
import { DistributorStatusEnum, DistributorStatusEnumString } from '@config/enum/distributor.enum';

@Component({
  selector: 'app-card-table-list',
  templateUrl: './card-table-list.component.html',
  styleUrls: ['./card-table-list.component.scss'],
})
export class CardTableListComponent implements OnInit, AfterViewInit, OnDestroy {
  @ViewChild(MatSort, { static: false }) matSort: MatSort;

  @Input() useCustomActionLink = false;
  @Input() actionPathDetail!: string;
  @Input() url!: string;
  @Input() tableColumns!: TableColumn[];

  @Output() totalCount = new EventEmitter();
  @Output() actionLink = new EventEmitter();

  baseDatasource!: BaseDatasource<IListDistributor>;
  displayedColumns!: string[];

  STRING_CONSTANTS = STRING_CONSTANTS;
  iconNone = STRING_CONSTANTS.ICON.IC_RETAIL_NONE;
  isLoading = new BehaviorSubject(false);

  string_filter!: string;
  isOpenFilter = false;
  isActiveFilter = false;

  @Input() privilegeDetail!: BehaviorSubject<boolean>;

  private unsubscribe: Subscription[] = [];

  constructor(
    private router: Router,
    private activeRoute: ActivatedRoute,
    private paginationService: UrlUtilsService,
    private baseTable: BaseTableService<IListDistributor>,
    private distributorService: DistributorService,
    private filterService: FilterService,
    public utilities: UtilitiesService
  ) {}

  ngOnInit(): void {
    this.queryHandler();
    this.setTableData();
  }

  queryHandler() {
    const _queryHandler = this.activeRoute.queryParams.subscribe((data) => {
      const { string_filter, type, area_manager_id, area_id } = data;

      this.string_filter = string_filter;
      this.isActiveFilter = !!(type || area_manager_id || area_id);

      const param = this.paginationService.sliceQueryParams();
      this.baseTable.loadDataTable(this.url, param ? param : '');
    });

    this.unsubscribe.push(_queryHandler);
  }

  ngAfterViewInit(): void {
    this.emitDataCount();
  }

  setTableData() {
    this.displayedColumns = this.tableColumns.map((head) => head.key);
    const _dataSubs = this.baseTable.responseDatabase.subscribe((resp) => (this.baseDatasource = resp));
    this.unsubscribe.push(_dataSubs);
  }

  sortTable(param: Sort) {
    const sortby = this.tableColumns.find((column) => column.key === param.active);
    param.active = sortby?.key ?? '';
    this.distributorService.sortDataSource(param, this.activeRoute.snapshot);
  }

  changePageEvent($event: BaseDatasource<any>) {
    this.filterService.changePageEvent($event, this.string_filter ?? '');
  }

  handleActionDetail(data: IListDistributor) {
    return this.useCustomActionLink ? this.actionLink.emit(data) : this.router.navigate([this.actionPathDetail + data.distributor_id]);
  }

  emitDataCount() {
    const _loadingSubs = this.baseDatasource.totalItem$.subscribe((val) => this.totalCount.emit(val));
    this.unsubscribe.push(_loadingSubs);
  }

  renderDate(val: any) {
    if (!val) return '-';
    return `<span class="d-block">${this.utilities.timeStampToDate(val, 'dd-MM-yyyy')}</span>
            <span class="d-block">${this.utilities.timeStampToDate(val, 'HH:mm:ss')}</span>`;
  }

  getStatusString(status: DistributorStatusEnum) {
    return this.utilities.mapKeyToString(DistributorStatusEnumString, status);
  }

  ngOnDestroy(): void {
    this.unsubscribe.forEach((sb) => sb.unsubscribe());
  }
}
