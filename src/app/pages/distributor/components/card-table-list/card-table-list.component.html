<ng-container
  [ngTemplateOutlet]="
    !isActiveFilter && !string_filter && (baseDatasource.isFinishLoadingSubject | async) && (baseDatasource.totalItem$ | async) === 0 ? emptyStateTpl : tableDataTpl
  "
  [ngTemplateOutletContext]="{ msg: 'Belum terdapat data distributor.' }"
>
</ng-container>

<ng-template #tableDataTpl>
  <app-card [cardBodyClasses]="'pt-2'" [header]="true">
    <ng-container cardHeader>
      <app-filter-list-distributor [searchInputValue]="string_filter"></app-filter-list-distributor>
    </ng-container>

    <ng-container
      cardBody
      [ngTemplateOutlet]="(isActiveFilter || string_filter) && baseDatasource.isTableLoaded && baseDatasource.totalItem$.getValue() === 0 ? emptyStateTpl : contentTpl"
      [ngTemplateOutletContext]="{ msg: 'Data distributor tidak ditemukan.' }"
    ></ng-container>

    <ng-template #contentTpl>
      <div class="table-responsive">
        <table [dataSource]="baseDatasource" (matSortChange)="sortTable($event)" class="table w-100 gy-5 table-row-bordered align-middle" mat-table matSort>
          <ng-container *ngFor="let tableColumn of tableColumns" [matColumnDef]="tableColumn.key">
            <!-- COLUMN HEADER -->
            <ng-container *ngIf="tableColumn.isSortable; else notSortable">
              <th
                *matHeaderCellDef
                [arrowPosition]="'after'"
                [class.min-w-200px]="tableColumn.key !== 'actions' && tableColumn.key !== 'distributor_code'"
                [mat-sort-header]="tableColumn.key"
                class="min-w-125px px-3"
                mat-header-cell
              >
                <app-table-content [width]="70" [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject">
                  <div
                    *ngIf="tableColumn.key === 'submit_by' || tableColumn.key === 'createdDate' || tableColumn.key === 'distributor_type_title'; else otherHeadKey"
                    class="mw-100px text-start"
                  >
                    {{ tableColumn.title }}
                  </div>
                  <ng-template #otherHeadKey>{{ tableColumn.title }}</ng-template>
                </app-table-content>
              </th>
            </ng-container>
            <ng-template #notSortable>
              <th
                *matHeaderCellDef
                [class.min-w-200px]="tableColumn.key !== 'actions'"
                [ngClass]="{ 'min-w-70px text-center': tableColumn.key === 'actions' }"
                class="min-w-125px px-3"
                mat-header-cell
              >
                <app-table-content [width]="70" [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject">
                  <div
                    *ngIf="tableColumn.key === 'submit_by' || tableColumn.key === 'createdDate' || tableColumn.key === 'distributor_type_title'; else otherHeadKey"
                    class="mw-100px text-start"
                  >
                    {{ tableColumn.title }}
                  </div>
                  <ng-template #otherHeadKey>{{ tableColumn.title }}</ng-template>
                </app-table-content>
              </th>
            </ng-template>

            <!-- COLUMN DATA -->
            <td *matCellDef="let element" class="px-3" mat-cell>
              <ng-container [ngSwitch]="tableColumn.key">
                <div *ngSwitchCase="'createdDate'">
                  <app-table-content [count]="2" [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject" [type]="'text'">
                    <div [innerHTML]="renderDate(element[tableColumn.dataKey ?? ''])"></div>
                  </app-table-content>
                </div>

                <div *ngSwitchCase="'status_title'">
                  <app-table-content [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject">
                    <span class="badge badge__status badge__status--{{ element['status_enum'] }}">
                    <!-- {{ element[tableColumn.key] }}-->
                      {{ getStatusString(element['status_enum']) }}
                    </span>
                  </app-table-content>
                </div>

                <div *ngSwitchCase="'actions'" class="text-center">
                  <app-table-content [count]="1" [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject" [type]="'icon'">
                    <span (click)="handleActionDetail(element)" [inlineSVG]="'./assets/media/icons/ic_task.svg'" class="svg-icon svg-icon-2 cursor-pointer"></span>
                  </app-table-content>
                </div>

                <div *ngSwitchDefault>
                  <app-table-content [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject">
                    <span>{{ element[tableColumn.key] }} </span>
                  </app-table-content>
                </div>
              </ng-container>
            </td>
          </ng-container>
          <tr *matHeaderRowDef="displayedColumns" class="fw-bold text-start text-uppercase" mat-header-row></tr>
          <tr *matRowDef="let row; columns: displayedColumns" mat-row></tr>
        </table>
      </div>

      <div class="d-flex justify-content-between py-4">
        <app-mai-material-bottom-table
          (changePage)="changePageEvent($event)"
          [baseDataTableComponent]="baseDatasource"
          [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject"
          class="w-100"
        ></app-mai-material-bottom-table>
      </div>
    </ng-template>
  </app-card>
</ng-template>

<ng-template #emptyStateTpl let-msg="msg">
  <app-card-empty [icon]="iconNone" [text]="msg"></app-card-empty>
</ng-template>
