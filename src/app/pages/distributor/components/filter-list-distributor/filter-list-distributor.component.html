<app-input-search [isFinishLoadingSubject]="finishLoadingSubject" [placeholder]="searchInputPlaceholder" [value]="searchInputValue" (actionFilter)="handleSearchAction($event)" />

<div class="ms-auto position-relative" data-kt-customer-table-toolbar="base">
  <app-filter-table
    [isFinishLoadingSubject]="finishLoadingSubject"
    [isActiveFilter]="filterInputActivated"
    [isOpenFilter]="filterInputOpened"
    (actionClick)="toggleOpenFilter()"
    (actionReset)="handleResetFilter()"
  >
    <div class="menu menu-sub menu-sub-dropdown w-300px w-md-350px show filter-body" id="kt-toolbar-filter">
      <div class="px-7 py-5">
        <div class="fs-4 text-dark fw-bold">Filter</div>
      </div>
      <div class="separator border-gray-200"></div>

      <form (ngSubmit)="handleSubmitFilter()" [formGroup]="filterForm" [ngClass]="'w-100'" class="form">
        <div class="px-7 py-5">
          <div class="mb-8">
            <app-input-select-radio formControlName="type" [label]="'Tipe Distributor'" [options]="listDistributorType" ngDefaultControl />
          </div>

          <div class="mb-10">
            <label class="form-label mb-0">Area:</label>
            <app-input-select-autocomplete
              #inputSelectArea
              placeholder="Pilih salah satu"
              [useCustomLabel]="true"
              [usePagination]="false"
              formControlName="area_id"
              (selectedValue)="onSelectedArea($event)"
              ngDefaultControl
            />
          </div>

          <div class="d-flex justify-content-end">
            <button mat-flat-button (click)="handleResetFilter()" class="btn text-primary me-4" type="reset">Reset</button>
            <button mat-button [disabled]="filterService.validateSubmitFilter(filterForm)" color="primary" class="btn btn-primary btn-sm text-white" type="submit">Terapkan</button>
          </div>
        </div>
      </form>
    </div>
  </app-filter-table>
</div>
