import { ChangeDetectorRef, Component, Input, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { IGenericLabelValueEnum } from '@shared/interface/generic';
import { UtilitiesService } from '@services/utilities.service';
import { EnumTypeBodyCardVerificationForm } from '../../distributor-detail/detail-pending/detail-registration/distributor-data/distributor-data.data';
import { ICardTabSection } from './card-tab-section.interface';
import { InterfaceDataMapperService } from '@services/interface-data-mapper.service';
import { GoogleMapsConfigService } from '@services/google-maps-config.service';
import { BehaviorSubject, Observable } from 'rxjs';
import { CardTabUtilsService } from './card-tab-utils.service';
import { EnumDistributorSectionCard } from '../../enum/label-section.enum';
import { map } from 'rxjs/operators';
import { ModalConfig } from '@shared/components/modal/modal.interface';
import { STRING_CONSTANTS } from '@config/constants/string.constants';

@Component({
  selector: 'app-card-tab-section',
  templateUrl: './card-tab-section.component.html',
  styleUrls: ['./card-tab-section.component.scss'],
})
export class CardTabSectionComponent<T> implements OnInit {
  @Input() data!: Observable<ICardTabSection<T>>;
  @Input() isAddressCard = false;

  isLoading = new BehaviorSubject<boolean>(false);
  mapOptions: any = {};

  @ViewChild('defaultValueTpl') defaultValueTpl: TemplateRef<any>;
  @ViewChild('dateValueTpl') dateValueTpl: TemplateRef<any>;
  @ViewChild('currencyValueTpl') currencyValueTpl: TemplateRef<any>;
  @ViewChild('spaceMeasureValueTpl') spaceMeasureValueTpl: TemplateRef<any>;

  modalPinPointConfig = <ModalConfig>{
    modalTitle: 'Pinpoint Alamat Pengiriman',
    showHeader: false,
    showFooter: false,
  };

  constructor(
    public utils: UtilitiesService,
    private interfaceDataMapperService: InterfaceDataMapperService,
    private cardTabUtils: CardTabUtilsService,
    private cdr: ChangeDetectorRef,
    private googleMapsConfig: GoogleMapsConfigService
  ) {}

  async ngOnInit(): Promise<void> {
    await this.initializeGoogleMaps();
    this.mapCardBodyLabel();
  }

  private async initializeGoogleMaps(): Promise<void> {
    try {
      this.mapOptions = await this.googleMapsConfig.getMapMarkerOptions();
    } catch (error) {
      console.error('Failed to load Google Maps in CardTabSectionComponent:', error);
    }
  }

  mapCardBodyLabel() {
    const _data$ = this.data;
    this.isLoading.next(true);
    _data$.subscribe((res) => {
      const cardEnum = res.cardEnum as EnumDistributorSectionCard;
      if (res.cardBodyGroup?.body) {
        let { body } = res.cardBodyGroup;
        res.cardBodyGroup.body = body.map((item) => {
          const { value } = item;

          if (Array.isArray(value)) {
            item.value = value
              .map((val: IGenericLabelValueEnum) => this.interfaceDataMapperService.mapObjectLabelValue(val))
              .map((_v) => _v.map((_i) => this.mapDataSection(_i, res.cardEnumSection, cardEnum)));
          } else item = this.mapDataSection(item, res.cardEnumSection, cardEnum);

          return item;
        });

        this.isLoading.next(false);
      }

      this.cdr.detectChanges();
    });
  }

  mapDataSection<T>(data: IGenericLabelValueEnum, cardEnumSection?: T, key?: any) {
    const { label, value } = data;
    const enumLabelSection = this.cardTabUtils.getEnumSection(key as EnumDistributorSectionCard, this.isAddressCard);
    return {
      labelEnum: label,
      label: enumLabelSection[label],
      value: value ? value : '-',
      typeValue: this.getValueType(label),
    };
  }

  getValueType(key: string) {
    if (!key) return;
    return key.endsWith('_url')
      ? EnumTypeBodyCardVerificationForm.IMG_URL
      : key.endsWith('_date')
      ? EnumTypeBodyCardVerificationForm.DATE
      : key.endsWith('_year_target') || key.endsWith('_per_year') || key.endsWith('credit_limit')
      ? EnumTypeBodyCardVerificationForm.CURRENCY
      : key.endsWith('_large')
      ? EnumTypeBodyCardVerificationForm.SPACE_MEASURE
      : undefined;
  }

  getLabelGroup = (data: IGenericLabelValueEnum[]) => data.find((item) => item.labelEnum === 'name')?.value;

  bodyValueIsArrayGroup = (data: any) => Array.isArray(data);

  hasPinPointMap = (data: IGenericLabelValueEnum[]) => data.some((item) => item.labelEnum === 'latitude' || item.labelEnum === 'longitude');

  hasImageDocument = (data: IGenericLabelValueEnum[]) => data && data.some((item) => item.typeValue === EnumTypeBodyCardVerificationForm.IMG_URL);

  getAsideImageData = (data: IGenericLabelValueEnum[]) => data && data.find((item) => item.typeValue === EnumTypeBodyCardVerificationForm.IMG_URL);

  getAsideImagePath(data: IGenericLabelValueEnum[]) {
    const _result = this.getAsideImageData(data);
    const _val = (_result && _result.value) as string;
    return _val ? [_val] : undefined;
  }

  getPinPointMap(data: IGenericLabelValueEnum[]) {
    const _lat = data.find((item) => item.labelEnum === 'latitude');
    const _lng = data.find((item) => item.labelEnum === 'longitude');
    return { lat: _lat ? +_lat.value : 0, lng: _lng ? +_lng.value : 0 };
  }

  getMapLabel(data: IGenericLabelValueEnum[], key: string) {
    return data && (data.find((item) => item.labelEnum === key)?.value as string);
  }

  getViewValueTpl(bodyType: EnumTypeBodyCardVerificationForm) {
    return bodyType === EnumTypeBodyCardVerificationForm.CURRENCY
      ? this.currencyValueTpl
      : bodyType === EnumTypeBodyCardVerificationForm.DATE
      ? this.dateValueTpl
      : bodyType === EnumTypeBodyCardVerificationForm.SPACE_MEASURE
      ? this.spaceMeasureValueTpl
      : this.defaultValueTpl;
  }

  renderValueMeasure(item: IGenericLabelValueEnum) {
    // string already formatted from BE
    if (isNaN(+item.value)) return item.value;
    else {
      const _value = this.utils.toThousandConvert(+item.value);
      return `${_value}<span>m<sup>2</sup></span>`;
    }
  }

  protected readonly EnumTypeBodyCardVerificationForm = EnumTypeBodyCardVerificationForm;
  protected readonly STRING_CONSTANTS = STRING_CONSTANTS;
  protected readonly map = map;
}
