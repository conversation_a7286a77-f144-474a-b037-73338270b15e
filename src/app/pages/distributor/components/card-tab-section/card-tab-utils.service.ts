import { Injectable } from '@angular/core';
import { EnumDistributorSectionCard } from '../../enum/label-section.enum';
import { CardSectionEnums, ICardTabSectionProps_BodyGroup } from './card-tab-section.interface';
import { DistributorTabStatus, PaymentMethodEnum } from '@config/enum/distributor.enum';
import { InterfaceDataMapperService } from '@services/interface-data-mapper.service';
import { IGenericLabelValueEnum } from '@shared/interface/generic';

@Injectable({
  providedIn: 'root',
})
export class CardTabUtilsService {

  constructor(private interfaceMapperService: InterfaceDataMapperService) {
  }

  getEnumSection(key: EnumDistributorSectionCard, isAddressCard = false) {
    if (isAddressCard) {
      return CardSectionEnums.EnumCardSectionDeliveryAddress;
    }

    const enumSectionMap: Record<EnumDistributorSectionCard, any> = {
      [EnumDistributorSectionCard.scope]: CardSectionEnums.EnumCardSectionScope,
      [EnumDistributorSectionCard.distributor_detail]: CardSectionEnums.EnumCardSectionDistributorDetail,
      [EnumDistributorSectionCard.owner]: CardSectionEnums.EnumCardSectionOwner,
      [EnumDistributorSectionCard.manager]: CardSectionEnums.EnumCardSectionManager,
      [EnumDistributorSectionCard.business_information]: CardSectionEnums.EnumCardSectionBusinessInfo,
      [EnumDistributorSectionCard.npwp_information]: CardSectionEnums.EnumCardSectionNPWP,
      [EnumDistributorSectionCard.supplier_information]: CardSectionEnums.EnumCardSectionSupplier,
      [EnumDistributorSectionCard.business_branch]: CardSectionEnums.EnumCardSectionBranch,
      [EnumDistributorSectionCard.credit_ceiling_granted]: CardSectionEnums.EnumCardSectionCreditCeilingGranted,
      [EnumDistributorSectionCard.sale_target]: CardSectionEnums.EnumCardSectionSaleTarget,
      [EnumDistributorSectionCard.credit_billing]: CardSectionEnums.EnumCardSectionCreditBilling,
      [EnumDistributorSectionCard.information_receivable]: CardSectionEnums.EnumCardSectionReceivableInfo,
      [EnumDistributorSectionCard.document_non_active]: CardSectionEnums.EnumCardSectionDocumentNonActive,
    };

    return enumSectionMap[key];
  }


  deleteKey<T extends object, K extends keyof T>(obj: T, key: K): Omit<T, K> {
    const { [key]: _, ...rest } = obj;
    return rest;
  }

  generateCardBodyGroup<T>(key: keyof T, data: T, status: DistributorTabStatus) {
    let _body = this.interfaceMapperService.labelValueEnumMapper(data[key]);
    let _labelTitle = '';

    if ((key === EnumDistributorSectionCard.owner || key === EnumDistributorSectionCard.manager) && status === DistributorTabStatus.REGISTERED) {
      _body = _body?.filter((val) => val.label !== 'ktp_url');
    }

    if (key === EnumDistributorSectionCard.supplier_information && _body) {
      // if payment cash not have credit term
      _body.flatMap((value: any) => {
        const _bodyValue = value.value as IGenericLabelValueEnum[];
        value.value = _bodyValue.map((val: any) => {
          if (val.payment_method_enum === PaymentMethodEnum.CASH) {
            return this.deleteKey(val, 'credit_term');
          }
          return val;
        });
      });
    }

    if (key === EnumDistributorSectionCard.credit_billing) {
      _labelTitle = 'Profil Tertagih';
    }

    return <ICardTabSectionProps_BodyGroup>{
      label: _labelTitle,
      labelEnum: key,
      body: _body,
    };
  }

  handleDeliveryAddressBodyGroup<T>(data: T) {
    let _body = this.interfaceMapperService.labelValueEnumMapper(data);
    const _labelTitle = '';
    _body = _body?.filter((val) => val.label !== 'id');
    return <ICardTabSectionProps_BodyGroup>{
      label: _labelTitle,
      labelEnum: '',
      body: _body,
    };
  }
}
