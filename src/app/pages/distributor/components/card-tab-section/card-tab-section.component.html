<app-card [cardClasses]="'mb-8'" [cardBodyClasses]="'pt-0'" [header]="true" *ngIf="data | async as data">
  <!-- header start -->
  <ng-container cardHeader>
    <h5 class="fw-bolder d-flex w-100 align-items-center mb-0">
      <span>{{ data.cardTitle }}</span>
    </h5>
  </ng-container>

  <!-- body start -->
  <ng-container cardBody>
    <app-section-loader *ngIf="isLoading | async" />
    <div class="row">
      <ng-container *ngIf="data.cardBodyGroup as data">
        <ng-container [ngTemplateOutlet]="hasImageDocument(data.body) ? imgAsideTpl : dataBodyTpl" [ngTemplateOutletContext]="{ data }"></ng-container>
        <ng-template #dataBodyTpl>
          <ng-container *ngFor="let body of data.body; index as i">
            <ng-container *ngIf="i === 1 && data.label">
              <div class="col-12 my-6">
                <span class="fw-bold">{{ data.label }}</span>
              </div>
            </ng-container>
            <ng-container [ngTemplateOutlet]="bodyValueIsArrayGroup(body.value) ? bodyArrayGroupTpl : defaultLabelValueTpl" [ngTemplateOutletContext]="{ body }"></ng-container>
          </ng-container>
        </ng-template>

        <!-- map view -->
        <ng-container *ngIf="hasPinPointMap(data.body)">
          <ng-container
            [ngTemplateOutlet]="isAddressCard ? modalMapViewTpl : mapViewTpl"
            [ngTemplateOutletContext]="{ body: data.body }"
          ></ng-container>
        </ng-container>
      </ng-container>

      <ng-template #bodyArrayGroupTpl let-body="body">
        <ng-container *ngIf="!body.value.length" [ngTemplateOutlet]="noDataTpl" [ngTemplateOutletContext]="{ title: data.cardTitle }"></ng-container>
        <ng-container *ngFor="let val of body.value">
          <div class="col-12 col-lg-6 mb-7">
            <div class="mb-4">
              <span class="fw-bolder">{{ getLabelGroup(val) }}</span>
            </div>
            <div class="row">
              <ng-container *ngFor="let v of val">
                <ng-container *ngIf="v.label">
                  <div class="col-12 col-lg-6 my-2">
                    <span class="text-gray-700">{{ v.label }}</span>
                  </div>
                  <div class="col-12 col-lg-6 my-2">
                    <span class="text-gray-900">
                      <span class="me-1">:</span>
                      <span>
                        <ng-container
                          [ngTemplateOutlet]="
                            v.typeValue === EnumTypeBodyCardVerificationForm.CURRENCY
                              ? currencyValueTpl
                              : v.typeValue === EnumTypeBodyCardVerificationForm.DATE
                              ? dateValueTpl
                              : defaultValueTpl
                          "
                          [ngTemplateOutletContext]="{ $implicit: v }"
                        ></ng-container>
                      </span>
                    </span>
                  </div>
                </ng-container>
              </ng-container>
            </div>
          </div>
        </ng-container>
      </ng-template>
    </div>

    <ng-template #defaultLabelValueTpl let-body="body">
      <ng-container *ngIf="body.label">
        <div class="col-12 col-lg-3 my-2">
          <span class="text-gray-700">{{ body.label }}</span>
        </div>
        <div class="col-12 col-lg-9 my-2">
          <span class="text-gray-900">
            <span class="me-1">:</span>
            <span>
              <ng-container [ngTemplateOutlet]="getViewValueTpl(body.typeValue)" [ngTemplateOutletContext]="{ $implicit: body }"></ng-container>
            </span>
          </span>
        </div>
      </ng-container>
    </ng-template>
  </ng-container>

  <ng-template #currencyValueTpl let-item>{{ utils.toRupiah(item.value) }}</ng-template>

  <ng-template #dateValueTpl let-item>{{ utils.formatEpochToDate(+item.value) }}</ng-template>

  <ng-template #spaceMeasureValueTpl let-item><span [innerHTML]="renderValueMeasure(item)"></span></ng-template>

  <ng-template #defaultValueTpl let-item>{{ item.value }}</ng-template>

  <ng-template #mapViewTpl let-body="body">
    <div class="map-container mt-7">
      <google-map [center]="getPinPointMap(body)" [zoom]="16" height="180px" width="100%">
        <map-marker [options]="mapOptions" [position]="getPinPointMap(body)"></map-marker>
      </google-map>
      <div class="px-6 py-6">
        <h4 class="mb-2">{{ getMapLabel(body, 'name') }}</h4>
        <span>{{ getMapLabel(body, 'address') }}</span>
      </div>
    </div>
  </ng-template>

  <!-- view template with image aside -->
  <ng-template #imgAsideTpl let-data="data">
    <div class="col-12 col-lg-8 border-end border-gray-300">
      <div class="row">
        <ng-container *ngFor="let body of data.body">
          <ng-container
            *ngIf="body.typeValue !== EnumTypeBodyCardVerificationForm.IMG_URL"
            [ngTemplateOutlet]="defaultLabelValueTpl"
            [ngTemplateOutletContext]="{body}"
          ></ng-container>
        </ng-container>
      </div>
    </div>
    <div class="col-12 col-lg-4">
      <h5 class="fw-bolder mb-7 px-5">
        <span>{{ getAsideImageData(data.body)?.label }}</span>
      </h5>
      <app-image-preview [images]="getAsideImagePath(data.body)" [openTextLabel]="false" [height]="200" [titleModal]="getAsideImageData(data.body)?.label" />
    </div>
  </ng-template>

  <ng-template #modalMapViewTpl let-body="body">
    <div class="d-flex justify-content-end position-absolute bottom-0 end-0 p-8">
      <button class="btn btn-transparent p-0" (click)="modalPinPoint.open()">
        <span class="text-info fw-bolder text-decoration-underline">Lihat pinpoint</span>
      </button>
    </div>
    <app-modal
      #modalPinPoint
      [modalTitle]="getMapLabel(body, 'label')"
      [modalConfig]="modalPinPointConfig"
      [modalOptions]="{size:'lg'}"
      [modalClass]="'modal-map'">
      <div class="map-container">
        <google-map [center]="getPinPointMap(body)" [zoom]="16" width="100%">
          <map-marker [options]="mapOptions" [position]="getPinPointMap(body)"></map-marker>
        </google-map>
      </div>
    </app-modal>
  </ng-template>
</app-card>

<ng-template #noDataTpl let-title="title">
  <app-card-empty [icon]="STRING_CONSTANTS.ILLUSTRATIONS.IL_NO_DATA" [text]="'Belum Ada Data ' + title"></app-card-empty>
</ng-template>
