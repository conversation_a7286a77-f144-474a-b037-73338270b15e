import { IGenericLabelValueEnum } from '@shared/interface/generic';

export interface ICardTabSection<T> {
  cardTitle: string;
  cardEnum?: string;
  cardEnumSection?: T;
  cardBodyGroup?: ICardTabSectionProps_BodyGroup;
}

export interface ICardTabSectionProps_BodyGroup {
  labelEnum: string;
  label?: string;
  body: IGenericLabelValueEnum[];
}

enum EnumCardSectionScope {
  area_name = 'Area',
  sub_area_name = 'Sub Area',
  regional_head_name = 'Regional Head',
  regional_head_phone = 'No. Telephone',
  marketing_executive_name = 'Marketing Executive',
  marketing_executive_phone = 'No. Telephone',
}

enum EnumCardSectionDistributorDetail {
  name = '<PERSON>a',
  type = 'Tipe Distributor',
  phone = 'No. Telephone',
  email = 'Email',
  address = 'Alamat Usaha',
}

const EnumCardSectionBusinessInfo = {
  established_date: 'Tanggal Pendirian Usaha',
  business_activity: 'Kegiatan Usaha',
  selling_item: '<PERSON><PERSON>al',
  space_large: '<PERSON>as <PERSON>/<PERSON>',
  employee_qty: '<PERSON><PERSON><PERSON>',
  payment_method: 'Sistem Pembayaran',
  turnover_per_year: 'Omzet Per Tahun',
  deed_incorporation_url: 'Akta Pendirian',
};

enum EnumCardSectionPerson {
  name = 'Nama',
  nik = 'NIK',
  phone = 'No. Telephone',
  email = 'Email',
  address = 'Alamat',
}

const EnumCardSectionOwner = {
  ...EnumCardSectionPerson,
  ktp_url: 'KTP Pemilik Usaha',
};

const EnumCardSectionManager = {
  ...EnumCardSectionPerson,
  ktp_url: 'KTP Pengelola Usaha',
};

enum EnumCardSectionNPWP {
  number = 'Nomor NPWP',
  name = 'Nama Sesuai NPWP',
  address = 'Alamat Sesuai NPWP',
  pkp_information = 'Informasi PKP',
  npwp_document_url = 'NPWP',
}

enum EnumCardSectionSupplier {
  name = 'Nama Perusahaan',
  selling_item = 'Jenis Barang Dijual',
  facility = 'Fasilitas yang Disediakan',
  payment_method_string = 'Sistem Pembayaran',
  credit_term = 'Jangka Waktu Kredit',
}

enum EnumCardSectionBranch {
  name = 'Label Usaha',
  address = 'Alamat Lengkap',
}

enum EnumCardSectionCreditCeilingGranted {
  payment_method_string = 'Tipe Pembayaran',
  credit_limit = 'Plafon yang diberikan',
  reason = 'Alasan',
  credit_term = 'Jangka Waktu Kredit',
  analysis = 'Analisa Pembayaran',
  assurance = 'Jaminan',
}

enum EnumCardSectionSaleTarget {
  first_year_target = 'Tahun Ke-1',
  second_year_target = 'Tahun Ke-2',
  third_year_target = 'Tahun Ke-3',
  fourth_year_target = 'Tahun Ke-4',
  fifth_year_target = 'Tahun Ke-5',
  market_share_percentage = 'Presentase Pangsa Pasar',
}

enum EnumCardSectionCreditBilling {
  billing_to = 'Penagihan Ke',
  name = 'Nama',
  nik = 'NIK',
  phone = 'Nomor Telephone',
  whatsapp = 'Nomor Whatsapp',
  email = 'Email',
  full_address = 'Alamat',
}

enum EnumCardSectionDeliveryAddress {
  label = 'Label Alamat',
  pic_name = 'Nama PIC',
  pic_phone = 'No Telephone PIC',
  regency_name = 'Kota',
  district_name = 'Kecamatan',
  sub_district_name = 'Kelurahan',
  full_address = 'Alamat Lengkap',
}

enum EnumCardSectionReceivableInfo {
  credit_ceiling = "Plafon Kredit"
}

enum EnumCardSectionDocumentNonActive {
  npwp_document_url = 'NPWP',
}

export const CardSectionEnums = {
  EnumCardSectionBranch,
  EnumCardSectionBusinessInfo,
  EnumCardSectionCreditCeilingGranted,
  EnumCardSectionDistributorDetail,
  EnumCardSectionManager,
  EnumCardSectionNPWP,
  EnumCardSectionOwner,
  EnumCardSectionScope,
  EnumCardSectionSupplier,
  EnumCardSectionSaleTarget,
  EnumCardSectionCreditBilling,
  EnumCardSectionDeliveryAddress,
  EnumCardSectionReceivableInfo,
  EnumCardSectionDocumentNonActive
};
