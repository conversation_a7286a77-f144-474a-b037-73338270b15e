import { ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges, TemplateRef, ViewChild } from '@angular/core';
import { ICardVerificationDocument, ICardVerificationForm, ICardVerificationFormBranch, INoteChangeVerification } from '../../interfaces/detail-registration.interface';
import { DetailRegistrationDistributorService } from '../../distributor-detail/detail-pending/detail-registration/detail-registration.service';
import { EnumVerificationStatus } from '../../../sales-marketing/program-marketing/program-marketing.enum';
import { BehaviorSubject } from 'rxjs';
import { ActivatedRoute, Params } from '@angular/router';
import {
  EnumCardVerificationForm,
  EnumCardVerificationSection,
  EnumTypeBodyCardVerificationForm,
} from '../../distributor-detail/detail-pending/detail-registration/distributor-data/distributor-data.data';
import { GoogleMapsConfigService } from '@services/google-maps-config.service';
import { UtilitiesService } from '@services/utilities.service';
import { ModalConfig } from '@shared/components/modal/modal.interface';
import { NgbModalOptions } from '@ng-bootstrap/ng-bootstrap';
import { InputRevisionNoteComponent } from '@shared/components/input-revision-note/input-revision-note.component';
import { StatusChangeEnum } from '@config/enum/distributor.enum';
import { STRING_CONSTANTS } from '@config/constants/string.constants';

@Component({
  selector: 'app-card-verification-form',
  templateUrl: './card-verification-form.component.html',
  styleUrls: ['./card-verification-form.component.scss'],
})
export class CardVerificationFormComponent implements OnInit, OnChanges {
  @Input() type: EnumCardVerificationForm = EnumCardVerificationForm.DEFAULT;
  @Input() isLoading: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(true);
  @Input() typeSection: EnumCardVerificationSection;
  @Input() statusChangeEnum: StatusChangeEnum = StatusChangeEnum.UPDATED;
  @Input() title: string;
  @Input() dataValue!: BehaviorSubject<any>;
  @Input() status?: string | null;
  @Input() bodyData: ICardVerificationForm[];
  @Input() bodyDataUpdateDocument: ICardVerificationDocument;
  @Input() bodyDataBranch: ICardVerificationFormBranch[];
  @Input() cardClasses: string;
  @Input() cardNoteValue!: any;
  @Input() isNotHaveRevision: boolean = false;
  @Input() isViewOnly: boolean = false;
  @Input() withBadgeCheck: boolean = true;
  @Input() idShippingAddress: string = '';
  @Output() cardNoteChanged = new EventEmitter<INoteChangeVerification>();
  @Output() afterActionVerification = new EventEmitter<string | null>();

  @Input() customModalButton = { enable: false, text: '' };
  @Output() customModalButtonTrigger = new EventEmitter();

  @Input() sessionKey: string;

  @ViewChild('inputRevision') inputRevision!: InputRevisionNoteComponent;
  @ViewChild('defaultTpl') defaultTpl: TemplateRef<any>;
  @ViewChild('ktpTpl') ktpTpl: TemplateRef<any>;
  @ViewChild('groupTpl') groupTpl: TemplateRef<any>;
  @ViewChild('modalMapTpl') modalMapTpl: TemplateRef<any>;
  @ViewChild('documentTpl') documentTpl: TemplateRef<any>;
  @ViewChild('updateDocumentTpl') updateDocumentTpl: TemplateRef<any>;
  @ViewChild('asideDocumentTpl') asideDocumentTpl: TemplateRef<any>;

  id: string;
  snapshotParams: Params;

  modalPinpointConfig: ModalConfig = {
    modalTitle: 'Pinpoint Alamat Pengiriman ',
    showHeader: false,
    showFooter: false,
  };

  modalPinpointOptions: NgbModalOptions = {
    size: 'lg',
  };

  mapOptions: any = {};
  mapMarkerOptions: any = {};

  get ShowActionButton() {
    const status = [StatusChangeEnum.ADD, StatusChangeEnum.UPDATED];
    return status.includes(this.statusChangeEnum);
  }

  get IsSectionDeleted() {
    return this.statusChangeEnum === StatusChangeEnum.DELETE;
  }

  constructor(
    private registrationService: DetailRegistrationDistributorService,
    private activatedRoute: ActivatedRoute,
    private ref: ChangeDetectorRef,
    public utilities: UtilitiesService,
    private googleMapsConfig: GoogleMapsConfigService
  ) {
    this.activatedRoute.paramMap.subscribe((params) => {
      this.id = params.get('id') ?? '';
    });
    this.snapshotParams = this.activatedRoute.snapshot.params;
  }

  async ngOnInit() {
    // Initialize Google Maps configuration
    await this.initializeGoogleMaps();

    this.dataValue?.subscribe((value) => {
      if (!!Object.keys(value).length) {
        if (this.typeSection !== EnumCardVerificationSection.DELIVERY_ADDRESS) {
          const data = value[this.typeSection.toLowerCase()];
          this.status = data?.status_verified_enum;
          this.handleSessionInput();
        }
      }
    });
  }

  private async initializeGoogleMaps(): Promise<void> {
    try {
      const config = await this.googleMapsConfig.getCompleteConfig();
      this.mapOptions = config.options;
      this.mapMarkerOptions = config.markerOptions;
    } catch (error) {
      console.error('Failed to load Google Maps in CardVerificationFormComponent:', error);
    }
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes.status) this.handleSessionInput();
    this.ref.detectChanges();
  }

  getVerificationStatus = () => this.status as EnumVerificationStatus;

  hasVerificationStatus = () => !!this.status;

  // flag tidak ada perubahan
  // !hasVerificationStatus && isNotHaveRevision
  isSectionNoChanges() {
    return this.isNotHaveRevision && !this.hasVerificationStatus();
  }

  isSectionVerified() {
    const status = this.status as EnumVerificationStatus;
    return this.isNotHaveRevision || this.registrationService.isSectionVerified(status);
  }

  isSectionRequestRevision() {
    return !(this.isViewOnly && this.ShowActionButton) && !this.isViewOnly && this.status === EnumVerificationStatus.REQUEST_REVISION;
  }

  onNoteChanged = (e: string) => {
    const data = { note: e?.trim(), type: this.typeSection };
    this.cardNoteChanged.emit(data);
  };

  fetchUpdatedDetailData(status: EnumVerificationStatus | null) {
    this.afterActionVerification.emit(status);
  }

  updateVerification(status: EnumVerificationStatus | null) {
    const refId = this.typeSection === EnumCardVerificationSection.DELIVERY_ADDRESS ? this.idShippingAddress : null;
    const _payload = {
      type: this.typeSection,
      ref_id: refId,
      status: status,
    };

    this.isLoading.next(true);
    this.registrationService.updateSectionVerification(this.id, _payload).subscribe((resp) => {
      if (resp && resp.success) {
        this.fetchUpdatedDetailData(status);
      }
    });
  }

  handleActionVerification(e: EnumVerificationStatus | null) {
    this.updateVerification(e);
    if (!e) {
      this.registrationService.clearSessionBySection(this.typeSection, this.sessionKey);
    }

    return this.afterActionVerification.emit(e);
  }

  get viewTemplate() {
    let _tpl: TemplateRef<any>;
    switch (this.type) {
      case EnumCardVerificationForm.KTP:
        _tpl = this.ktpTpl;
        break;
      case EnumCardVerificationForm.GROUP:
        _tpl = this.groupTpl;
        break;
      case EnumCardVerificationForm.MODAL_MAP:
        _tpl = this.modalMapTpl;
        break;
      case EnumCardVerificationForm.DOCUMENT:
        _tpl = this.documentTpl;
        break;
      case EnumCardVerificationForm.UPDATE_DOCUMENT:
        _tpl = this.updateDocumentTpl;
        break;
      case EnumCardVerificationForm.ASIDE_DOCUMENT:
        _tpl = this.asideDocumentTpl;
        break;
      default:
        _tpl = this.defaultTpl;
        break;
    }

    return _tpl;
  }

  viewMap(val: { lat: number; lng: number }) {
    this.googleMapsConfig.openInGoogleMaps(val);
  }

  getDocumentUrl(bodyData: ICardVerificationForm[]) {
    return bodyData.find((value) => value.type === EnumTypeBodyCardVerificationForm.IMG_URL);
  }

  getDocuments(bodyData: ICardVerificationForm[]) {
    return bodyData.filter((value) => value.type === EnumTypeBodyCardVerificationForm.IMG_URL);
  }

  handleSessionInput() {
    if (!this.isSectionRequestRevision()) return;
    setTimeout(() => this.inputRevision.noteControl.setValue(this.cardNoteValue), 500);
  }

  renderClassCard() {
    if ((this.snapshotParams.tab === 'log' && !!this.status) || !this.withBadgeCheck) return '';
    if (this.IsSectionDeleted) return 'border border-danger';
    return this.isSectionVerified() ? 'border border-primary' : this.isSectionRequestRevision() ? 'border border-danger' : '';
  }

  showBtnGroup() {
    return this.registrationService.checkBtnVerification(this.snapshotParams.tab);
  }

  handleMapInitialized(map: google.maps.Map, item: any) {
    const marker = {
      position: {
        lat: item.lat,
        lng: item.long,
      },
    };
    return new google.maps.Marker({
      position: marker.position,
      map,
      ...this.mapMarkerOptions,
    });
  }

  showBadge() {
    return (!!this.status || this.isNotHaveRevision) && this.withBadgeCheck;
  }

  showBtnGroupVerify() {
    if (this.typeSection === EnumCardVerificationSection.DELIVERY_ADDRESS) {
      return !this.isViewOnly && this.ShowActionButton;
    } else {
      return !this.isSectionNoChanges() && !this.isViewOnly && this.ShowActionButton;
    }
  }

  protected readonly EnumTypeBodyCardVerificationForm = EnumTypeBodyCardVerificationForm;
  protected readonly STRING_CONSTANTS = STRING_CONSTANTS;
  protected readonly Array = Array;
  protected readonly EnumVerificationStatus = EnumVerificationStatus;
}
