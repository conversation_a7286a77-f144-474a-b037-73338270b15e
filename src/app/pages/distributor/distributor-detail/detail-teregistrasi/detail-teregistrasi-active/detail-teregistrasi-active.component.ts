import { After<PERSON>iewInit, ChangeDetector<PERSON><PERSON>, <PERSON>mponent, On<PERSON><PERSON>roy, OnInit, ViewChild } from '@angular/core';
import { TabLabelEnumDetailDistributor, TabListLabelDetailDistributor } from '../../../enum/tablist-label.enum';
import { ITabList } from '@shared/interface/tablist.interface';
import { UtilitiesService } from '@services/utilities.service';
import { PageLink } from '@metronic/layout';
import { ActivatedRoute, Router } from '@angular/router';
import { DistributorService } from '../../../distributor.service';
import { DetailRegistrationDistributorService } from '../../detail-pending/detail-registration/detail-registration.service';
import { RolePrivilegeService } from '@services/role-privilege.service';
import { BehaviorSubject, Observable, Subscription } from 'rxjs';
import { IDistributorImpact, IHeaderDistributorDetail } from '../../../interfaces/detail-header.interface';
import { STRING_CONSTANTS } from '@config/constants/string.constants';
import { ModalComponent } from '@shared/components/modal/modal.component';
import { ModalConfig } from '@shared/components/modal/modal.interface';
import { IDataResResetPasswordDistributor, IPrintKUL } from '../../../interfaces/distributor.interface';
import { DistributorStatusEnum, DistributorStatusEnumTabString, DistributorTabStatus } from '@config/enum/distributor.enum';
import { DistributorDetailService } from '../../../distributor-detail.service';
import { ModalNonActiveCustomerComponent } from '@shared/components/modal/modal-non-active-customer/modal-non-active-customer.component';

export interface IDataResetPassword {
  name: string;
  email: string;
  account_id: string;
}

@Component({
  selector: 'app-detail-teregistrasi-active',
  templateUrl: './detail-teregistrasi-active.component.html',
  styleUrls: ['./detail-teregistrasi-active.component.scss'],
})
export class DetailTeregistrasiActiveComponent implements OnInit, AfterViewInit, OnDestroy {
  id: any | null = null;
  status: any | null = null;
  tab: any | null = null;
  links: Array<PageLink>;
  isLoading = new BehaviorSubject(true);

  detailHeader = new BehaviorSubject<IHeaderDistributorDetail>({} as IHeaderDistributorDetail);
  impactedDistributor = new BehaviorSubject<IDistributorImpact>({} as IDistributorImpact);
  dataResetPassword = new BehaviorSubject<IDataResetPassword | undefined>(undefined);
  newPasswordResult = new BehaviorSubject<IDataResResetPasswordDistributor | undefined>(undefined);
  dataKUL$: Observable<IPrintKUL>;

  privilegePrintKUL: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  privilegeResetPassword: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  privilegeNonActive: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);

  tabIndex = 0;
  tabList!: ITabList[];

  @ViewChild('modalResetPassword') private modalResetPassword: ModalComponent;
  modalConfigResetPassword: ModalConfig = {
    modalTitle: 'RESET PASSWORD DISTRIBUTOR',
    dismissButtonLabel: 'Reset Password',
    closeButtonLabel: 'cancel',
  };

  @ViewChild('modalSuccessResetPassword') private modalSuccessResetPassword: ModalComponent;
  modalSuccessResetPasswordConfig: ModalConfig = {
    dismissButtonLabel: 'Oke',
    closeButtonLabel: 'Lihat Detail',
  };

  @ViewChild('modalNonActiveCustomer') private modalNonActiveCustomer: ModalNonActiveCustomerComponent;

  private unsubscribe: Subscription[] = [];

  constructor(
    public utilities: UtilitiesService,
    private activatedRoute: ActivatedRoute,
    private distributorService: DistributorService,
    public registrationService: DetailRegistrationDistributorService,
    private rolePrivilegeService: RolePrivilegeService,
    private ref: ChangeDetectorRef,
    private router: Router,
    private detailDistributorService: DistributorDetailService
  ) {
    const _routeParamSubs = this.activatedRoute.paramMap.subscribe((params) => {
      this.id = params.get('id');
      this.status = params.get('status');
      this.tab = params.get('tab');
    });
    this.unsubscribe.push(_routeParamSubs);
  }

  ngOnInit(): void {
    this.handlePrivilege();
    this.initPageInfo();
  }

  initPageInfo() {
    const header$ = this.distributorService.distributorDetailHeaderSubject.subscribe((value) => {
      if (!value || Object.keys(value).length <= 0) return;
      this.detailHeader.next(value);
      this.getDataPrintKul();
    });
    const impact$ = this.distributorService.distributorImpactSubject.subscribe((value) => {
      if (!value || Object.keys(value).length <= 0) return;
      this.impactedDistributor.next(value);
    });
    this.unsubscribe.push(header$);
    this.unsubscribe.push(impact$);
  }

  showAction() {
    const { status_enum } = this.detailHeader.value;
    return status_enum !== DistributorStatusEnum.NON_ACTIVATED;
  }

  getDataPrintKul() {
    this.dataKUL$ = this.distributorService.getDataPrintKUL(this.id);
  }

  ngAfterViewInit() {
    this.initTabList();
  }

  initTabList() {
    this.detailHeader.subscribe((value) => {
      if (!value || Object.keys(value).length <= 0) return;
      this.tabList = this.distributorService.generateDistributorDetailTabList(value.status_enum);

      this.tabList.map((tab) => {
        const isLog = tab.enum === TabLabelEnumDetailDistributor.TAB_LOG;
        tab.privilege = isLog ? true : this.rolePrivilegeService.checkPrivilege('DISTRIBUTOR', 'DETAIL_DISTRIBUTOR', tab.enum);
        tab.component = this.distributorService.setTabListComponent(tab.enum, value.status_enum);
      });

      this.tabList = this.tabList
        .filter((tab) => tab.privilege === true)
        .map((tab, index) => {
          tab.index = index;
          return tab;
        });
    });
  }

  handlePrivilege() {
    this.detailDistributorService.getDetailPrivilege();

    this.privilegePrintKUL.next(
      this.rolePrivilegeService.getPrivilegeApprovalCTA({
        parentMenu: 'DISTRIBUTOR',
        detailKey: 'DETAIL_DISTRIBUTOR',
        ctaKey: 'CTA_PRINT_KUL',
      })
    );

    this.privilegeResetPassword.next(
      this.rolePrivilegeService.getPrivilegeApprovalCTA({
        parentMenu: 'DISTRIBUTOR',
        detailKey: 'DETAIL_DISTRIBUTOR',
        ctaKey: 'CTA_RESET_PASSWORD',
      })
    );

    this.privilegeNonActive.next(
      this.rolePrivilegeService.getPrivilegeApprovalCTA({
        parentMenu: 'DISTRIBUTOR',
        detailKey: 'DETAIL_DISTRIBUTOR',
        ctaKey: 'CTA_NONACTIVATED_DISTRIBUTOR',
      })
    );
  }

  handleTabChange = (e: number) => (this.tabIndex = e);

  onClickResetPassword() {
    this.modalResetPassword.open().then();
  }

  onOkeResetPassword = () => {
    this.modalResetPassword.close().then(() => {
      this.distributorService.onResetPassword().subscribe((res) => {
        if (res?.data) {
          this.newPasswordResult.next(res.data);
          this.ref.detectChanges();
          this.modalSuccessResetPassword.open().then();
        }
      });
    });
  };

  onClickDetailPopup = () => {
    this.router.navigate(['/distributor/teregistrasi/active/' + this.newPasswordResult.value?.id]).then();
  };

  onSuccessResetPassword = () => {};

  async onClickNonActiveCustomer() {
    return this.modalNonActiveCustomer.open();
  }

  onPrint() {
    return this.router.navigate([`/distributor/kul/${this.id}`], {
      queryParams: {
        status: this.status,
        tab: this.tab,
      },
    });
  }

  ngOnDestroy() {
    this.unsubscribe.forEach((sb) => sb.unsubscribe());
  }

  protected readonly TabListLabelDetailDistributor = TabListLabelDetailDistributor;
  protected readonly STRING_CONSTANTS = STRING_CONSTANTS;
  protected readonly DistributorStatusEnumTabString = DistributorStatusEnumTabString;
  protected readonly DistributorTabStatus = DistributorTabStatus;
}
