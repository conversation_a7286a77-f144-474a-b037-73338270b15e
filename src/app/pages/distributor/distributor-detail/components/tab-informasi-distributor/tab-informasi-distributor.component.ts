import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Params } from '@angular/router';
import { DistributorService } from '../../../distributor.service';
import { IDistributorInformation } from '../../../interfaces/detail-informasi.interface';
import { BehaviorSubject } from 'rxjs';
import { EnumDistributorSectionCard } from '../../../enum/label-section.enum';
import { CardSectionEnums, ICardTabSection } from '../../../components/card-tab-section/card-tab-section.interface';
import {
  DistributorRegisteredTabType,
  DistributorStatusEnum,
  DistributorStatusEnumTabString,
  DistributorTabStatus,
  DistributorTypeEnum,
} from '@config/enum/distributor.enum';
import { CardTabUtilsService } from '../../../components/card-tab-section/card-tab-utils.service';
import { InterfaceDataMapperService } from '@services/interface-data-mapper.service';
import { UtilitiesService } from '@services/utilities.service';
import { IGenericNameUrl } from '@shared/interface/generic';

@Component({
  selector: 'app-tab-informasi-distributor',
  templateUrl: './tab-informasi-distributor.component.html',
  styleUrls: ['./tab-informasi-distributor.component.scss'],
})
export class TabInformasiDistributorComponent implements OnInit {
  isLoading = new BehaviorSubject(false);
  detailInformationSubject = new BehaviorSubject({} as IDistributorInformation);
  distributorID!: string;
  snapshotParams!: Params;

  // sections data
  bodyScope = new BehaviorSubject({ cardTitle: 'Cakupan' } as ICardTabSection<typeof CardSectionEnums.EnumCardSectionScope>);
  bodyDistributorDetail = new BehaviorSubject({ cardTitle: 'Detail Distributor' } as ICardTabSection<typeof CardSectionEnums.EnumCardSectionDistributorDetail>);
  bodyOwner = new BehaviorSubject({ cardTitle: 'Pemilik Usaha' } as ICardTabSection<typeof CardSectionEnums.EnumCardSectionOwner>);
  bodyManager = new BehaviorSubject({ cardTitle: 'Pengelola Usaha' } as ICardTabSection<typeof CardSectionEnums.EnumCardSectionManager>);
  bodyBusinessInfo = new BehaviorSubject({ cardTitle: 'Informasi Usaha' } as ICardTabSection<typeof CardSectionEnums.EnumCardSectionBusinessInfo>);
  bodyNpwpInfo = new BehaviorSubject({ cardTitle: 'Informasi NPWP' } as ICardTabSection<typeof CardSectionEnums.EnumCardSectionNPWP>);
  bodySupplier = new BehaviorSubject({ cardTitle: 'Informasi Supplier' } as ICardTabSection<typeof CardSectionEnums.EnumCardSectionSupplier>);
  bodyBusinessBranch = new BehaviorSubject({ cardTitle: 'Cabang Usaha' } as ICardTabSection<typeof CardSectionEnums.EnumCardSectionBranch>);
  bodyCreditCeilingGranted = new BehaviorSubject({ cardTitle: 'Plafon yang Diberikan' } as ICardTabSection<typeof CardSectionEnums.EnumCardSectionCreditCeilingGranted>);
  bodySaleTarget = new BehaviorSubject({ cardTitle: 'Target Penjualan' } as ICardTabSection<typeof CardSectionEnums.EnumCardSectionSaleTarget>);
  bodyCreditBilling = new BehaviorSubject({ cardTitle: 'Penagihan Kredit' } as ICardTabSection<typeof CardSectionEnums.EnumCardSectionSaleTarget>);
  bodyReceivableInfo = new BehaviorSubject({ cardTitle: 'Informasi Piutang' } as ICardTabSection<typeof CardSectionEnums.EnumCardSectionReceivableInfo>);
  bodyDocumentNonActive = new BehaviorSubject({ cardTitle: 'Informasi Piutang' } as ICardTabSection<typeof CardSectionEnums.EnumCardSectionDocumentNonActive>);

  documentNonActive: IGenericNameUrl[] = [];

  get DetailHeader() {
    return this.distributorService.DistributorDetailHeader;
  }

  get IsPendingTabNonActive() {
    const { status, tab } = this.snapshotParams;
    return status === DistributorTabStatus.PENDING && tab === DistributorStatusEnumTabString.NON_ACTIVE;
  }

  get DetailInformation() {
    return this.detailInformationSubject.value;
  }

  // get DataDocumentNonActive() {
  //   return [this.DetailInformation.document_non_active];
  // }

  constructor(
    private activatedRoute: ActivatedRoute,
    private distributorService: DistributorService,
    public utils: UtilitiesService,
    private interfaceMapperService: InterfaceDataMapperService,
    private cardTabUtilsService: CardTabUtilsService,
  ) {
  }

  ngOnInit(): void {
    this.snapshotParams = this.activatedRoute.snapshot.params;
    this.distributorID = this.activatedRoute.snapshot.params.id;
    this.getDetailInformation();
  }

  getDetailInformation() {
    // this.isLoading.next(true);
    // this.detailInformationSubject.next(ResponsePengajuanNonActiveDummy);
    // this.mapBodyGroupData();
    // this.isLoading.next(false);

    this.isLoading.next(true);
    this.distributorService.distributorDetailHeaderSubject.subscribe((value) => {
      if (!value || Object.keys(value).length === 0) return;
      this.distributorService.getDistributorInformation(this.distributorID, this.DetailHeader.status_enum).subscribe((resp) => {
        if (!resp) return;
        this.detailInformationSubject.next(resp.data);
        this.documentNonActive = resp.data.document_non_active ? [resp.data.document_non_active] : [{} as IGenericNameUrl];
        this.mapBodyGroupData();
        this.isLoading.next(false);
      });
    });
  }

  getBodySubject(key: EnumDistributorSectionCard) {
    const subjectMap: Record<EnumDistributorSectionCard, BehaviorSubject<any>> = {
      [EnumDistributorSectionCard.scope]: this.bodyScope,
      [EnumDistributorSectionCard.distributor_detail]: this.bodyDistributorDetail,
      [EnumDistributorSectionCard.owner]: this.bodyOwner,
      [EnumDistributorSectionCard.manager]: this.bodyManager,
      [EnumDistributorSectionCard.business_information]: this.bodyBusinessInfo,
      [EnumDistributorSectionCard.npwp_information]: this.bodyNpwpInfo,
      [EnumDistributorSectionCard.supplier_information]: this.bodySupplier,
      [EnumDistributorSectionCard.business_branch]: this.bodyBusinessBranch,
      [EnumDistributorSectionCard.credit_ceiling_granted]: this.bodyCreditCeilingGranted,
      [EnumDistributorSectionCard.sale_target]: this.bodySaleTarget,
      [EnumDistributorSectionCard.credit_billing]: this.bodyCreditBilling,
      [EnumDistributorSectionCard.information_receivable]: this.bodyReceivableInfo,
      [EnumDistributorSectionCard.document_non_active]: this.bodyDocumentNonActive,
      // [EnumDistributorSectionCard.delivery_address]: null,
    };
    return subjectMap[key];
  }

  mapBodyGroupData() {
    const _data = this.detailInformationSubject.value;
    for (const _k in _data) {
      if (_data.hasOwnProperty(_k)) this.generateBodyData(_k as EnumDistributorSectionCard, _data);
    }
  }

  generateBodyData(key: EnumDistributorSectionCard, data: IDistributorInformation) {
    const enumSection = this.cardTabUtilsService.getEnumSection(key);
    const bodySubject = this.getBodySubject(key);
    if (!bodySubject && !enumSection) return;

    const _bodyGroup = this.cardTabUtilsService.generateCardBodyGroup(key as keyof typeof data, data, this.snapshotParams.status);

    // filter out deed_incorporation_url for distributor type: individual
    if (key === 'business_information' && this.DetailHeader.business_type === DistributorTypeEnum.INDIVIDUAL) {
      _bodyGroup.body = _bodyGroup.body.filter((item) => item.label !== 'deed_incorporation_url');
    }

    bodySubject.next({
      ...bodySubject.value,
      cardEnum: key,
      cardBodyGroup: _bodyGroup,
    });
  }

  isPendingRegistrationFinance = () => this.DetailHeader.status_enum === DistributorStatusEnum.PENDING_REGISTRATION_FINANCE;

  toRupiah(value: number | string | null) {
    return this.utils.toRupiah(Number(value) ?? 0);
  }

  protected readonly DistributorTabStatus = DistributorTabStatus;
  protected readonly DistributorRegisteredTabType = DistributorRegisteredTabType;
  protected readonly Object = Object;
}
