import { AfterViewInit, Component, ComponentRef, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { BaseComponent } from '@shared/base/base.component';
import { ActivatedRoute, Router } from '@angular/router';
import { PageInfoService, PageLink } from '@metronic/layout';
import { CardPriceSummaryV1Component } from '@shared/components/card/v1/card-price-summary-v1/card-price-summary-v1.component';
import {
  ICreateSO,
  IFormCreateSO__ProductField,
  IInitCreateSO,
  IInitCreateSO__ProductOrder,
  IInitCreateSO__ProductQty,
  IPayloadProductQty,
  IPostSO,
  IPostSOFormat,
} from '@models/product-order.model';
import { BehaviorSubject, debounceTime, Observable } from 'rxjs';
import { PurchaseOrderService } from '../../purchase-order.service';
import { PoComponentModule } from '../../components/po-component.module';
import { FormSelectGudangComponent } from '../../components/form-select-gudang/form-select-gudang.component';
import { ISelectGudang } from '@shared/components/form/input-select2/input-select2.interface';
import { ComponentsModule } from '@shared/components/components.module';
import { FormProductOrderComponent } from '../../components/form-product-order/form-product-order.component';
import { ModalComponent } from '@shared/components/modal/modal.component';
import { ModalConfig } from '@shared/components/modal/modal.interface';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { InlineSVGModule } from 'ng-inline-svg-2';
import { STRING_CONSTANTS } from '@config/constants/string.constants';
import { ICardPriceSummary } from '@shared/components/card/v1/card-price-summary-v1/card-price-summary-v1.interface';
import { CardProductPromagComponent } from '@shared/components/card/card-product-promag/card-product-promag.component';
import { ICardProductPromag } from '@shared/components/card/card-product-promag/card-product-promag.interface';
import { SalesOrderModule } from '../../../sales-order/sales-order.module';
import { IProductOrder__ProductList } from '@shared/interface/product-order.interface';
import { CardProductOrderComponent } from '@shared/components/card/card-product-order/card-product-order.component';
import { StatusPurchaseOrder } from '@config/enum/status.enum';

@Component({
  selector: 'app-create-so.old',
  standalone: true,
  imports: [CommonModule, PoComponentModule, ComponentsModule, MatProgressSpinnerModule, InlineSVGModule, SalesOrderModule],
  templateUrl: './create-so.component.html',
  styleUrls: ['./create-so.component.scss'],
})
export class CreateSOComponent extends BaseComponent implements OnInit, AfterViewInit {
  id!: string;
  links!: PageLink[];

  initData$!: Observable<IInitCreateSO>;
  productOrder$!: Observable<IProductOrder__ProductList[]>;
  hasProgramMarketing!: boolean;
  hasProductProgramMarketing!: boolean;

  dataResponse = new BehaviorSubject('');
  loadingCreateSO = new BehaviorSubject(false);

  reasonConfirmPromag = new BehaviorSubject<string>('');

  @ViewChild('modalCancel') modalCancel!: ModalComponent;
  modalConfigCancel = <ModalConfig>{
    modalTitle: 'BATALKAN PEMBUATAN SO',
    closeButtonLabel: 'Cancel',
    dismissButtonLabel: 'Lanjutkan',
    onDismiss: () => this.router.navigate(['/purchase-order/list']),
  };

  @ViewChild('modalConfirmSO') modalConfirmSO!: ModalComponent;
  modalConfigConfirmSO = <ModalConfig>{
    modalTitle: 'BUAT SALES ORDER',
    closeButtonLabel: 'Cancel',
    dismissButtonLabel: 'Lanjutkan',
    onDismiss: () => this.handleCreateSO(),
  };

  @ViewChild('modalResponseCreateSO') modalResponseCreateSO!: ModalComponent;
  modalConfigResponseCreateSO = <ModalConfig>{
    dismissButtonLabel: 'Oke',
    closeButtonLabel: 'Lihat Detail',
    onClose: () => this.toDetailPO(),
    onDismiss: () => this.toDetailPO(true),
    disableDismissButton: () => this.loadingCreateSO.value,
    disableCloseButton: () => this.loadingCreateSO.value,
  };

  @ViewChild('cardSelectGudang') cardSelectGudang: FormSelectGudangComponent;
  selectedGudang = new BehaviorSubject({} as ISelectGudang);

  @ViewChild('cardProductOrder') cardProductOrder: CardProductOrderComponent;
  productOrderRef!: ComponentRef<FormProductOrderComponent>;

  @ViewChild('cardPriceSummary') cardPriceSummary: CardPriceSummaryV1Component;
  @ViewChild('cardProductPromag') cardProductPromag: CardProductPromagComponent;

  constructor(
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private pageInfoService: PageInfoService,
    private poService: PurchaseOrderService,
    private purchaseOrderService: PurchaseOrderService
  ) {
    super();
    this.id = this.activatedRoute.snapshot.params['id'];
    this.initData$ = this.poService.getInitCreateSO(this.id);
  }

  ngOnInit(): void {
    this.initPageInfo();
    this.initDataCreateSO();
  }

  ngAfterViewInit(): void {
    this.productOrderRef = this.cardProductOrder.componentRef;
    this.initLoadProductOrder();
  }

  initDataCreateSO() {
    this.initData$.subscribe((res) => {
      if (!res) return;
      const { is_have_program_marketing, purchase_order_code } = res;
      this.hasProgramMarketing = is_have_program_marketing;

      // update page breadcrumbs
      const _poStatus = history.state.data ? `${history.state.data}` : '';
      const _links = [
        { title: purchase_order_code, path: `/purchase-order/needfulfill/${_poStatus}/${this.id}`, isActive: false },
        { title: '', path: '', isActive: false, isSeparator: true },
        { title: 'Buat Sales Order', path: '', isActive: true },
      ];

      this.updateBreadcrumbs(_links);
    });
  }

  initLoadProductOrder() {
    this.productOrder$ = this.productOrderRef.instance.productOrderSubject.asObservable();
    this.productOrder$.subscribe((res) => {
      if (res && !!Object.keys(res).length) {
        this.handleChangeValue();
        this.mapToPayloadProductQty(res).then((val) => this.initProductPromag(val));
        this.mapToPayloadProductQty(res).then((val) => this.initPriceSummary(val));
      }
    });
  }

  initPriceSummary(payload: IPayloadProductQty) {
    this.cardPriceSummary.isLoading.next(true);
    return this.poService.getPatchPriceSummarySO<ICardPriceSummary>(this.id, payload).subscribe((res) => {
      if (!res) return;
      this.cardPriceSummary.dataValue = res;
      this.cardPriceSummary.isLoading.next(false);
    });
  }

  initProductPromag(payload: IPayloadProductQty) {
    if (!this.poService.InitCreateSO?.is_have_program_marketing) return;
    this.cardProductPromag.isLoading.next(true);
    return this.poService.getPatchProductPromagSO<ICardProductPromag[]>(this.id, payload).subscribe((res) => {
      if (!res) return;
      this.cardProductPromag.dataValue = res;
      this.cardProductPromag.isLoading.next(false);
    });
  }

  async mapToPayloadProductQty(data: IInitCreateSO__ProductOrder[]) {
    const products = <IInitCreateSO__ProductQty[]>[];
    data.forEach((item) => {
      products.push({
        product_id: item.product_id,
        qty: item.qty_outstanding,
      });
    });
    return <IPayloadProductQty>{ products };
  }

  initPageInfo() {
    this.links = [
      { title: 'Purchase Order', path: '/purchase-order/list', isActive: false },
      { title: '', path: '', isActive: false, isSeparator: true },
      { title: 'Need Fulfill', path: '/purchase-order/needfulfill', isActive: false },
      { title: '', path: '', isActive: false, isSeparator: true },
    ];
    this.updateBreadcrumbs();
    this.pageInfoService.updateTitle(`Buat Sales Order`, undefined, false);
  }

  updateBreadcrumbs = (links?: PageLink[]) => {
    if (links && !!links.length) links.forEach((link) => this.links.push(link));
    this.pageInfoService.updateBreadcrumbs(this.links);
  };

  handleChangeGudang(val: ISelectGudang) {
    this.selectedGudang.next(val);
  }

  handleCreateSO = () => {
    this.modalResponseCreateSO.open().then();
    this.loadingCreateSO.next(true);

    const myForm: ICreateSO[] = this.productOrderRef.instance.formProduct.value.products;
    const data: IPostSO[] = myForm.map((value) => {
      const { qty_fulfilled, product_id, note } = value;
      return {
        product_id: product_id,
        qty: qty_fulfilled,
        estimate_available: !!note ? note : null,
      };
    });

    const _payload: IPostSOFormat = {
      warehouse_id: this.selectedGudang.value.value,
      sales_order_item_requests: data,
    };

    this.purchaseOrderService.postCreateSO(this.id, _payload).subscribe((response: any) => {
      if (response && !response.success) {
        return this.modalResponseCreateSO.close().then(() => this.loadingCreateSO.next(false));
      }

      if (response && response.success) {
        this.dataResponse.next(response.data.message);
        this.loadingCreateSO.next(false);
      }
    });
  };

  handleCancel = () => this.modalCancel.open();

  toDetailPO(toPrevious = false) {
    if (toPrevious) {
      history.back();
      return true;
    }

    let _path = `/purchase-order`;
    _path += this.hasNoteNeedFullFill() ? `/needfulfill/${StatusPurchaseOrder.OUTSTANDING}` : `/inprogress/${StatusPurchaseOrder.ON_PROGRESS}`;
    _path += `/${this.id}`;

    return this.router.navigate([`${_path}`]);
  }

  hasNoteNeedFullFill() {
    const productWithNote = this.productOrderRef.instance.formProduct.value.products.filter((item: any) => item.note);
    return productWithNote.length > 0;
  }

  selectedGudangHasValue = () => this.selectedGudang.value && Object.keys(this.selectedGudang.value).length > 0;

  selectedGudangIsMostRequest = () => this.selectedGudang.value.is_most_request;

  getProductFormValue = () => this.productOrderRef.instance.FormProductsArray.value as IFormCreateSO__ProductField[];

  cardSectionsIsLoaded = () => !!(this.cardSelectGudang && this.cardProductOrder && this.cardPriceSummary);

  checkValidity() {
    let _invalid = false;
    const _invalidFormGudang = this.cardSelectGudang.FormGudang.invalid;

    //!check validate product field qty value
    const products = this.getProductFormValue().filter((item) => this.isValidProductFieldQty(item));
    if (products.length <= 0) return true;

    //!find empty note if qty_fulfilled < qty_order
    const shouldHaveNote = products.filter((item) => {
      const { qty_fulfilled, qty_order, note } = item;
      return +qty_fulfilled < +qty_order ? !note : +qty_fulfilled !== +qty_order;
    });

    _invalid = !!shouldHaveNote.length;

    //!check valid total product qtyy
    if (!shouldHaveNote.length) _invalid = !this.isValidProductTotalQty();

    return _invalidFormGudang || _invalid;
  }

  isValidProductFieldQty(item: IFormCreateSO__ProductField) {
    return item.qty_fulfilled >= 0 && item.qty_fulfilled <= item.qty_order && item.qty_fulfilled <= item.qty_order;
  }

  isValidProductTotalQty() {
    //!Don't let 0 fulfilled value to submit
    const products = this.productOrderRef.instance.FormProductsArray.value as IFormCreateSO__ProductField[];
    let _count = 0;
    products.map((item) => (_count += +item.qty_fulfilled));
    return _count > 0;
  }

  getProductSaleUnit(id: string) {
    if (!id) return;
    const products = <IProductOrder__ProductList[]>this.productOrderRef.instance.productOrderSubject.value;
    return products.find((item) => item.product_id === id)?.sale_unit;
  }

  getFulfilledProducts = () => this.getProductFormValue().filter((product) => product.qty_fulfilled > 0);
  hasUnmetProducts = () => !!this.getUnmetProducts().length;
  getUnmetProducts = () => this.getProductFormValue().filter((product) => product.qty_less > 0);
  hasBonusProducts = () => !!this.getBonusProducts().length;

  getBonusProducts() {
    const data: any[] = [];
    this.cardProductPromag?.data?.value.forEach((item) => {
      item.product_list.forEach((product) => {
        const { product_name, qty_outstanding, sale_unit } = product;
        data.push({ product_name, qty_outstanding, sale_unit });
      });
    });

    return data;
  }

  handleChangeValue() {
    this.productOrderRef.instance.payloadProductOrder$.pipe(debounceTime(2000)).subscribe((value) => {
      if (Object.keys(value).length > 0) {
        this.initProductPromag(value);
        this.initPriceSummary(value);
      }
    });
  }

  handleConfirmSO() {
    this.modalConfirmSO.open().then();
    const payloadProductOrder = this.productOrderRef.instance.payloadProductOrder$.value;
    this.purchaseOrderService.patchConfirmCreateSO(this.id, payloadProductOrder).subscribe((value: any) => {
      if (value && value.success) this.reasonConfirmPromag.next(value.data.reason);
    });
  }

  haveProductProgramMarketing(haveProduct: boolean) {
    this.hasProductProgramMarketing = haveProduct;
  }

  getNoteValue(type: string) {
    if (!type) return;
    const selectedNote = this.productOrderRef.instance.modalNoteEstimation.noteItems.find((value: any) => value.available_enum === type);
    return selectedNote?.display;
  }

  protected readonly STRING_CONSTANTS = STRING_CONSTANTS;
  protected readonly FormProductOrderComponent = FormProductOrderComponent;
}
