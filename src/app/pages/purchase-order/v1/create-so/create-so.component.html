<!-- Card: select gudang -->
<app-form-select-gudang #cardSelectGudang (selectedGudang)="handleChangeGudang($event)" />

<!-- Card: product order list -->
<app-card-product-order #cardProductOrder [tableData]="FormProductOrderComponent" />

<!-- Card: product program marketing -->
<app-card-product-promag #cardProductPromag [showView]="hasProgramMarketing" (haveProductProgramMarketing)="haveProductProgramMarketing($event)" [isSO]="true" />

<!-- Card: price summary -->
<app-card-price-summary-v1 #cardPriceSummary />

<ng-container *ngIf="cardSectionsIsLoaded()">
  <!-- Section: CTA group -->
  <div class="d-flex justify-content-between align-items-center mt-6">
    <button (click)="handleCancel()" class="btn btn-outline btn-outline-gray text-primary">Batal</button>
    <button (click)="handleConfirmSO()" [disabled]="checkValidity()" class="btn btn-primary ms-4">
      <span>Buat SO</span>
    </button>
  </div>

  <!-- Modals -->
  <app-modal #modalCancel [modalConfig]="modalConfigCancel">
    <div class="text-center">
      Apakah anda yakin untuk keluar dari halaman pembuatan sales order?<br />
      Data yang sudah diinput akan hilang.
    </div>
  </app-modal>

  <app-modal #modalConfirmSO [modalConfig]="modalConfigConfirmSO" [modalOptions]="{ size: 'md' }">
    <app-note-view
      *ngIf="selectedGudangHasValue() && selectedGudangIsMostRequest()"
      [color]="'info'"
      [icon]="STRING_CONSTANTS.ICON.IC_INFORMATION"
      [text]="'Sales Order akan diteruskan kepada Admin Pusat untuk proses approval.'"
    />

    <div class="text-left">
      <div class="mb-6"><span>Apakah yakin melanjutkan proses pembuatan Sales Order dengan detail:</span></div>
      <!-- Produk Dipenuhi -->
      <div class="mb-6 border-bottom border-gray-300 pb-6">
        <p class="label">
          <span [inlineSVG]="STRING_CONSTANTS.ICON.IC_BOX_PRODUCT" class="me-2"></span>
          <span class="fw-bold">Produk Dipenuhi</span>
        </p>
        <ul class="ps-6 mb-0">
          <li *ngFor="let item of getFulfilledProducts(); let first = first; let last = last" [class.py-2]="!last && !first">
            <span>{{ item.product_name }} ({{ item.qty_fulfilled }} {{ getProductSaleUnit(item.product_id) }})</span>
          </li>
        </ul>
      </div>

      <!-- Produk Hadiah -->
      <div *ngIf="hasProductProgramMarketing" class="mb-6 border-bottom border-gray-300 pb-6">
        <p class="label">
          <span [inlineSVG]="STRING_CONSTANTS.ICON.IC_BOX_REWARD" class="me-2"></span>
          <span class="fw-bold">Produk Hadiah</span>
        </p>
        <ul class="ps-6 mb-0">
          <li *ngFor="let item of getBonusProducts(); let first = first; let last = last" [class.py-2]="!last && !first">
            <span>{{ item.product_name }} ({{ item.qty_outstanding }} {{ item.sale_unit }})</span>
          </li>
        </ul>
        <div *ngIf="!!(reasonConfirmPromag | async)" class="d-flex align-items-center text-danger fw-bold">
          <span [inlineSVG]="STRING_CONSTANTS.ICON.IC_DANGER" class="svg-icon svg-icon-24 me-3"></span>
          <div>{{ reasonConfirmPromag | async }}</div>
        </div>
      </div>

      <!-- Produk Belum Dipenuhi -->
      <div *ngIf="hasUnmetProducts()" class="mb-6 border-bottom border-gray-300 pb-6">
        <p class="label">
          <span [inlineSVG]="STRING_CONSTANTS.ICON.IC_BOX_PRODUCT_UNMET" class="me-2"></span>
          <span class="fw-bold">Produk Belum Dipenuhi</span>
        </p>
        <ul class="ps-6 mb-0">
          <li *ngFor="let item of getUnmetProducts(); let first = first; let last = last" [class.py-2]="!last && !first">
            <div class="d-flex flex-column">
              <div>{{ item.product_name }} ({{ item.qty_less }} {{ getProductSaleUnit(item.product_id) }})</div>
              <div class="text-gray-600">Estimasi tersedia dalam {{ getNoteValue(item.note) }}</div>
            </div>
          </li>
        </ul>
      </div>

      <!-- Gudang Pengirim -->
      <div *ngIf="selectedGudang | async as gudang" class="mb-6">
        <p class="label">
          <span [inlineSVG]="STRING_CONSTANTS.ICON.IC_DELIVERY_TRUCK" class="me-2"></span>
          <span class="fw-bold">Gudang Pengirim</span>
        </p>
        <span>{{ gudang.label }}</span>
        <span class="d-block">{{ gudang.labelExtra }}</span>
      </div>
    </div>
  </app-modal>

  <app-modal #modalResponseCreateSO [modalConfig]="modalConfigResponseCreateSO">
    <div class="d-flex flex-column justify-content-center align-items-center">
      <div *ngIf="loadingCreateSO | async; else responseMessageTpl" class="mt-8">
        <mat-spinner></mat-spinner>
      </div>
    </div>
    <ng-template #responseMessageTpl>
      <div class="mt-8"><span [inlineSVG]="STRING_CONSTANTS.ICON.SUCCESS_ALERT"></span></div>
      <div>{{ dataResponse.value }}</div>
    </ng-template>
  </app-modal>
</ng-container>
