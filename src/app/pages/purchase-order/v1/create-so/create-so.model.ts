import {IInitCreateSO} from "@models/product-order.model";
import {IProductOrder__ProductList} from "@shared/interface/product-order.interface";

export class CreateSoModel {
  private headerData = <IInitCreateSO>{}
  private productOrderList = <IProductOrder__ProductList[]>[];

  get HeaderData() {
    return this.headerData;
  }
  set HeaderData(val) {
    this.headerData = val;
  }

  get ProductOrderList() {
    return this.productOrderList;
  }
  set ProductOrderList(val) {
    this.productOrderList = val;
  }
}
