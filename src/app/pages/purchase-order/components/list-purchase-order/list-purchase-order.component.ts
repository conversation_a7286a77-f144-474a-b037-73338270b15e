import { Component, Input, OnInit, ViewChild } from '@angular/core';
import { PageInfoService, PageLink } from '@metronic/layout';
import { IFilterStatusPO, PurchaseOrder } from '../../purchase.order.interface';
import { TableColumn } from '@shared/interface/table.interface';
import { BaseDatasource } from '@shared/base/base.datasource';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { UrlUtilsService } from '@utils/url-utils.service';
import { ActivatedRoute, Router } from '@angular/router';
import { BaseTableService } from '@shared/base/base-table-service.service';
import { Sort } from '@angular/material/sort';
import { BehaviorSubject } from 'rxjs';
import { UtilitiesService } from '@services/utilities.service';
import { STRING_CONSTANTS } from '@config/constants/string.constants';
import { FilterService } from '@services/filter.service';
import { MatChipListbox, MatChipListboxChange } from '@angular/material/chips';
import { StatusHistoryPurchaseOrderLabel, StatusPurchaseOrderLabel } from '../../submenu.enum';
import { PurchaseOrderService } from '../../purchase-order.service';

@Component({
  selector: 'app-list-purchase-order',
  templateUrl: './list-purchase-order.component.html',
  styleUrls: ['./list-purchase-order.component.scss'],
})
export class ListPurchaseOrderComponent implements OnInit {
  @ViewChild('filterPillsBox') filterPillsBox: MatChipListbox;

  @Input() title: string;
  @Input() links: Array<PageLink>;
  @Input() apiUrl: string;
  @Input() tableColumns: TableColumn[];
  @Input() subMenu: string;
  @Input() showFilterStatus: boolean = false;

  // table
  displayedColumns: string[];
  baseDatasource: BaseDatasource<PurchaseOrder>;
  checkActive: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  statusFilterList: BehaviorSubject<IFilterStatusPO[]> = new BehaviorSubject<IFilterStatusPO[]>([]);

  // filter data: keyword
  string_filter: string;
  filterPills: string[] = [];

  // filter data: advance
  isOpenFilter: boolean;
  isActiveFilter: boolean = false;
  filterForm: FormGroup;
  STRING_CONSTANTS = STRING_CONSTANTS;
  iconNone = STRING_CONSTANTS.ICON.IC_TROLLEY;

  constructor(
    private pageInfoService: PageInfoService,
    private paginationService: UrlUtilsService,
    private router: Router,
    private baseTableService: BaseTableService<PurchaseOrder>,
    private activeRoute: ActivatedRoute,
    private formBuilder: FormBuilder,
    private filterService: FilterService,
    public utilities: UtilitiesService,
    private purchaseOrderService: PurchaseOrderService
  ) {}

  get purchaseOrderStatusControl() {
    return <FormControl>this.filterForm.get('status');
  }

  ngOnInit(): void {
    this.baseTableService.responseDatabase.subscribe((response) => (this.baseDatasource = response));
    this.getStatusFilter();
    this.setTableData();
    this.initFilter();
    this.initFilterPills();
    this.queryHandler(this.apiUrl);
    this.initPageInfo();
  }

  initPageInfo() {
    this.pageInfoService.updateBreadcrumbs(this.links);
  }

  getStatusFilter() {
    this.purchaseOrderService.getFilterStatusPO().subscribe((response) => this.statusFilterList.next(response?.data));
  }

  // GET DATA
  queryHandler(apiUrl: string) {
    this.activeRoute.queryParams.subscribe((data) => {
      this.string_filter = data.string_filter;
      this.filterForm.controls['status'].setValue(data.status);
      this.filterForm.controls['exp_start_date'].setValue(data.exp_start_date);
      this.filterForm.controls['exp_end_date'].setValue(data.exp_end_date);
      this.isActiveFilter = !!(data.status || data.exp_start_date || data.exp_end_date);
      const param = this.paginationService.sliceQueryParams();
      this.baseTableService.loadDataTable(apiUrl, param ? param : '');
      this.filterService.filterCountList(this.baseDatasource, this.title);
    });
  }

  // TABLE
  setTableData() {
    this.displayedColumns = this.tableColumns.map((item) => item.key);
  }

  sortTable(param: Sort) {
    const sortby = this.tableColumns.find((column) => column.key === param.active);
    param.active = sortby?.key ?? '';
    this.filterService.sortDataSource(param).then();
  }

  // END TABLE

  // PAGINATION
  changePageEvent($event: BaseDatasource<any>) {
    this.filterService.changePageEvent($event, this.string_filter ?? '');
  }

  // FILTER
  initFilter() {
    this.filterForm = this.formBuilder.group({
      status: [''],
      exp_start_date: new FormControl({ value: '', disabled: true }),
      exp_end_date: new FormControl({ value: '', disabled: true }),
    });
  }

  initFilterPills() {
    const _keys = Object.keys(StatusHistoryPurchaseOrderLabel);
    return (this.filterPills = _keys.map((_item) => Object(StatusHistoryPurchaseOrderLabel)[_item]));
  }

  onSearch(event: string) {
    this.checkActive.next(!this.checkActive.value);
    this.filterService.onSearch(event);
    this.string_filter = '';
  }

  onClickFilter() {
    this.isOpenFilter = !this.isOpenFilter;
  }

  handleResetFilter = () => {
    this.isActiveFilter = false;
    this.isOpenFilter = false;
    const _keyFilter = ['status', 'exp_start_date', 'exp_end_date'];
    if (this.showFilterStatus) {
      this.resetFilterPillsBox();
    }
    this.filterService.resetFilter(this.filterForm, _keyFilter);
  };

  resetFilterPillsBox = () => (this.filterPillsBox.value = []);

  handleSubmitFilter() {
    this.isActiveFilter = true;
    this.isOpenFilter = false;

    this.checkActive.next(!this.checkActive.value);

    const _extras = {
      queryParams: {
        string_filter: this.string_filter,
        status: this.purchaseOrderStatusControl.value ?? undefined,
        exp_start_date: this.utilities.timeStampToDate(this.filterForm.controls['exp_start_date'].value, 'yyyy-MM-dd'),
        exp_end_date: this.utilities.timeStampToDate(this.filterForm.controls['exp_end_date'].value, 'yyyy-MM-dd'),
      },
    };
    this.filterService.submitFilter(this.filterForm, _extras);
  }

  // OTHERS
  handleAction(record: PurchaseOrder) {
    return this.router.navigate([`purchase-order/${this.subMenu}/${record.status_enum}/${record.id}`]);
  }

  handlePOStatusChange(e: MatChipListboxChange) {
    const _selectedKey: string[] = [];
    e.value.forEach((item: string) => _selectedKey.push(this.utilities.getEnumKeyByValue(StatusPurchaseOrderLabel, item)));
    this.purchaseOrderStatusControl.patchValue(_selectedKey.join());
  }
}
