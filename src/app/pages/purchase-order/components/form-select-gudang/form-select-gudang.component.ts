import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { BaseComponent } from '@shared/base/base.component';
import { PurchaseOrderService } from '../../purchase-order.service';
import {BehaviorSubject, Observable} from 'rxjs';
import { BaseResponse } from '@shared/base/base-response';
import { IWarehouseList } from '@shared/interface/warehouse.interface';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { ISelectGudang } from '@shared/components/form/input-select2/input-select2.interface';
import { STRING_CONSTANTS } from '@config/constants/string.constants';

@Component({
  selector: 'app-form-select-gudang',
  templateUrl: './form-select-gudang.component.html',
  styleUrls: ['./form-select-gudang.component.scss'],
})
export class FormSelectGudangComponent extends BaseComponent implements OnInit {
  @Output() selectedGudang = new EventEmitter<ISelectGudang>();

  formGudang!: FormGroup;
  listGudang$!: Observable<BaseResponse<IWarehouseList>>;
  listGudang!: ISelectGudang[];
  isLoading = new BehaviorSubject(true);

  constructor(private fb: FormBuilder, private poService: PurchaseOrderService) {
    super();
    this.listGudang$ = this.poService.getListGudang();
  }

  get FormGudang() {
    return <FormGroup>this.formGudang;
  }

  get Gudang() {
    return <FormControl>this.FormGudang.controls['gudang'];
  }

  ngOnInit() {
    this.initForm();
    this.getListGudang();
  }

  initForm() {
    this.formGudang = this.fb.group({
      gudang: ['', Validators.required],
    });
  }

  // get list gudang
  getListGudang() {
    this.isLoading.next(true);
    this.listGudang$.subscribe((resp) => {
      if (!resp) return;
      this.listGudang = this.poService.mapInputSelectGudang(resp.data);
      this.isLoading.next(false);
    });
  }

  selectedGudangHasValue() {
    if (!this.Gudang.value) return false;
    // uncomment if it still needs to check most request attribute.
    const _selectedGudang = this.mapSelectedGudang(this.Gudang.value);
    return this.FormGudang.valid && _selectedGudang.is_most_request;
  }

  // handle change
  // filter selected gudang from list then emit selected value.
  handleChangeGudang = () => this.selectedGudang.emit(this.mapSelectedGudang(this.Gudang.value));

  mapSelectedGudang = (id: string) => this.listGudang.filter((gd) => gd.value === id)[0];

  protected readonly STRING_CONSTANTS = STRING_CONSTANTS;
}
