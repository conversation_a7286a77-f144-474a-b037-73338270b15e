<app-card-header-loader *ngIf="isLoading|async; else cardViewTpl" />
<ng-template #cardViewTpl>
  <app-card [header]="true" cardHeaderTitle="Silahkan Pilih Gudang Pengirim" [cardClasses]="'z-index-1 mb-8'" [cardBodyClasses]="'pt-0'" >
    <ng-container cardBody>
<!--      <pre>formGudang: {{ formGudang.value | json }}</pre>-->
      <form [formGroup]="formGudang">
        <app-input-select2
          [class]="'list-gudang'"
          [maxHeight]="'360px'"
          [options]="listGudang"
          formControlName="gudang"
          (handleChangeData)="handleChangeGudang()"
          label="Gudang Pengirim"
          placeholder="Silahkan pilih gudang pengiriman"
          ngDefaultControl
        />
      </form>
      <app-note-view
        *ngIf="selectedGudangHasValue()"
        [color]="'info'"
        [text]="'Sales Order akan diteruskan kepada Admin Pusat untuk proses approval.'"
        [icon]="STRING_CONSTANTS.ICON.IC_INFORMATION"
      ></app-note-view>
    </ng-container>
  </app-card>
</ng-template>
