import { Component, OnInit, ViewChild } from '@angular/core';
import { BaseComponent } from '@shared/base/base.component';
import { FormArray, FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { TableColumn } from '@shared/interface/table.interface';
import { PurchaseOrderService } from '../../purchase-order.service';
import { IFormCreateSO__ProductField, IInitCreateSO__ProductOrder, IPayloadProductQty } from '@models/product-order.model';
import { UtilitiesService } from '@services/utilities.service';
import { BehaviorSubject } from 'rxjs';
import { ICSourceData } from '@shared/components/form/input-counter-order/input-counter-order.interface';
import { ModalNoteEstimationComponent } from '../form-note-estimation/modal-note-estimation.component';

@Component({
  selector: 'app-form-product-order',
  templateUrl: './form-product-order.component.html',
  styleUrls: ['./form-product-order.component.scss'],
})
export class FormProductOrderComponent extends BaseComponent implements OnInit {
  @ViewChild('modalNoteEstimation') modalNoteEstimation: ModalNoteEstimationComponent;
  id!: string;

  tableColumns!: TableColumn[];
  displayedColumns!: string[];

  productOrderSubject = new BehaviorSubject(this.poService.ProductOrderList);
  isFinishLoadingSubject = new BehaviorSubject(true);
  payloadProductOrder$ = new BehaviorSubject({} as IPayloadProductQty);

  formProduct: FormGroup = this.fb.group({
    products: this.fb.array([]),
  });

  get FormProductOrder() {
    return <FormGroup>this.formProduct;
  }

  get FormProductsArray() {
    return <FormArray>this.FormProductOrder.get('products');
  }

  getProductFormControl(idx: number) {
    return <FormControl>this.FormProductsArray.at(idx);
  }

  getProductNote(idx: number) {
    return <FormControl>this.FormProductsArray.at(idx).get('note');
  }

  getProductName(idx: number) {
    return <FormControl>this.FormProductsArray.at(idx).get('product_name');
  }

  getValueQtyLess(idx: number) {
    return this.getProductFormControl(idx).value.qty_less;
  }

  getValueQtyFulFilled(idx: number) {
    return this.getProductFormControl(idx).value.qty_fulfilled;
  }

  getValueQtyOrder(idx: number) {
    return this.getProductFormControl(idx).value.qty_order;
  }

  getValueNote(idx: number) {
    const noteEnum = this.getProductNote(idx).value;
    const noteSelect = this.modalNoteEstimation.noteItems.filter((item) => item.available_enum === noteEnum)[0];
    return !!Object.keys(noteSelect).length ? noteSelect.display : noteEnum;
  }

  constructor(private activeRoute: ActivatedRoute, private fb: FormBuilder, private poService: PurchaseOrderService, private utils: UtilitiesService) {
    super();
    this.id = this.activeRoute.snapshot.params['id'];
  }

  ngOnInit() {
    this.setTableData();
    this.getProductOrder();
  }

  setTableData() {
    this.tableColumns = this.poService.tableProductCreateSO;
    this.displayedColumns = this.tableColumns.map((head) => head.key);
  }

  getProductOrder() {
    this.isFinishLoadingSubject.next(false);
    this.poService.getListProductOrderSO(this.id).subscribe((data) => {
      if (!data) return;

      // need to filter out any item product with no quantity
      const _data = data.filter((item) => item.qty_outstanding > 0);
      this.productOrderSubject.next(_data);

      this.initForm();
      this.isFinishLoadingSubject.next(true);
    });
  }

  initForm() {
    this.productOrderSubject.value.map((item) => this.FormProductsArray.push(this.createProductField(item)));
    this.setPayloadProduct();
    this.handleQtyChanges();
  }

  createProductField(data: IInitCreateSO__ProductOrder) {
    const { product_id, product_image, product_name, qty_outstanding } = data;
    return this.fb.group<IFormCreateSO__ProductField>({
      product_id,
      product_name,
      product_image,
      qty_order: qty_outstanding,
      qty_fulfilled: qty_outstanding,
      qty_less: 0,
      note: '',
    });
  }

  handleNoImage = (e: Event) => this.utils.setDefaultImageProduct(e);

  async handleNote(idx: number) {
    this.modalNoteEstimation.noteTitle = this.getProductName(idx).value;

    // reset selected note from previous value
    // or set value from form control value
    if (!this.noteHasValue(idx)) this.modalNoteEstimation.selectedNote = '';
    else this.modalNoteEstimation.selectedNote = this.getProductNote(idx).value;

    await this.modalNoteEstimation.openDialog();
    this.getProductNote(idx).patchValue(this.modalNoteEstimation.selectedNote);
  }

  noteHasValue = (idx: number) => !!this.getProductNote(idx).value;

  handleQtyChanges() {
    this.FormProductsArray.controls.map((ctrl, index) => {
      ctrl.get('qty_fulfilled')?.valueChanges.subscribe((val) => {
        const qtyOrder = this.getValueQtyOrder(index);
        ctrl.get('qty_less')?.patchValue(qtyOrder - val);
        if (val === qtyOrder) ctrl.get('note')?.patchValue(null);

        // payload for parent - Create SO
        this.setPayloadProduct();
      });
    });
  }

  get orderListAsSourceData() {
    return this.productOrderSubject.value.map((product) => this.mapToSourceDataItem(product));
  }

  setPayloadProduct() {
    this.payloadProductOrder$.next(this.mapToPayload());
  }

  mapToPayload() {
    const payload: IPayloadProductQty = { products: [] };
    this.formProduct.value.products.map((item: IFormCreateSO__ProductField) => {
      payload.products.push({ product_id: item.product_id, qty: item.qty_fulfilled });
    });
    return payload;
  }

  mapToSourceDataItem(item: IInitCreateSO__ProductOrder) {
    const { qty_outstanding, available_stock } = item;
    return <ICSourceData>{ order_item: qty_outstanding, available_stock };
  }
}
