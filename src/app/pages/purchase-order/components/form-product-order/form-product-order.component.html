<div class="table-responsive">
  <ng-container [ngTemplateOutlet]="(isFinishLoadingSubject | async) ? formProductTpl : tableLoaderTpl"></ng-container>
</div>

<ng-template #formProductTpl>
  <form [formGroup]="formProduct">
    <table [dataSource]="productOrderSubject.value" class="table w-100 gy-5 table-row-bordered align-middle" formArrayName="products" mat-table>
      <ng-container *ngFor="let tc of tableColumns" [matColumnDef]="tc.key">
        <!-- COLUMN HEADER-->
        <th *matHeaderCellDef class="min-w-125px px-3" mat-header-cell>
          {{ tc.title }}
        </th>
        <!-- COLUMN DATA -->
        <td *matCellDef="let element; let i = index" [formGroupName]="i" class="px-3" mat-cell>
          <ng-container [ngSwitch]="tc.key">
            <div *ngSwitchCase="'product_name'">
              <div class="d-flex align-items-center">
                <div class="d-flex justify-content-center w-48 me-2">
                  <img (error)="handleNoImage($event)" alt="" class="img-product-card" src="{{ element['product_image'] }}" />
                </div>
                <span class="d-block fw-bold">{{ element['product_name'] }}</span>
              </div>
            </div>

            <div *ngSwitchCase="'qty_order'">
              <span class="d-block">{{ element['qty_outstanding'] }} {{ element['sale_unit'] }}</span>
            </div>

            <div *ngSwitchCase="'qty_fulfilled'">
              <app-input-counter-order [fieldIndex]="i" [formControlName]="'qty_fulfilled'" [sourceData]="orderListAsSourceData" [withValidateStock]="false" ngDefaultControl />
            </div>

            <div *ngSwitchCase="'qty_less'">
              <span class="d-block">{{ getValueQtyLess(i) }} {{ element['sale_unit'] }}</span>
            </div>

            <div *ngSwitchCase="'note'">
              <ng-container [ngTemplateOutlet]="noteHasValue(i) ? editNoteTpl : addNoteTpl"></ng-container>

              <ng-template #addNoteTpl>
                <button (click)="handleNote(i)" [disabled]="getValueQtyLess(i) === 0" class="btn btn-bg-none fw-bold text-hover-info">
                  <span [class.text-danger]="getValueQtyFulFilled(i) < getValueQtyOrder(i)" class="text-decoration-underline">Tulis Catatan</span>
                </button>
              </ng-template>

              <ng-template #editNoteTpl>
                <div class="ms-6">Tersedia dalam {{ getValueNote(i) }}</div>
                <button (click)="handleNote(i)" class="btn btn-bg-none text-decoration-underline fw-bold text-info">Ubah</button>
              </ng-template>
            </div>

            <div *ngSwitchDefault>
              <span>{{ element[tc.key] }} </span>
            </div>
          </ng-container>
        </td>
      </ng-container>
      <tr *matHeaderRowDef="displayedColumns" class="fw-bold text-start text-uppercase" mat-header-row></tr>
      <tr *matRowDef="let row; columns: displayedColumns" mat-row></tr>
    </table>
  </form>
</ng-template>

<ng-template #tableLoaderTpl>
  <app-table-list-loader [isFinishLoading]="isFinishLoadingSubject" />
</ng-template>

<!-- modal note -->
<app-modal-note-estimation #modalNoteEstimation />
