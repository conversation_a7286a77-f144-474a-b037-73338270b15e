import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FilterListPOComponent } from './filter-list-po/filter-list-po.component';
import { ComponentsModule } from '@shared/components/components.module';
import { InlineSVGModule } from 'ng-inline-svg-2';
import { Select2Module } from 'ng-select2-component';
import { MatChipsModule } from '@angular/material/chips';
import { MatButtonModule } from '@angular/material/button';
import { FormSelectGudangComponent } from './form-select-gudang/form-select-gudang.component';
import { FormProductOrderComponent } from './form-product-order/form-product-order.component';
import {MatTableModule} from "@angular/material/table";
import {MatRadioModule} from "@angular/material/radio";
import { ModalNoteEstimationComponent } from './form-note-estimation/modal-note-estimation.component';

@NgModule({
  declarations: [FilterListPOComponent, FormSelectGudangComponent, FormProductOrderComponent, ModalNoteEstimationComponent],
  exports: [FilterListPOComponent, FormSelectGudangComponent, FormProductOrderComponent],
  imports: [CommonModule, ComponentsModule, InlineSVGModule, Select2Module, MatChipsModule, MatButtonModule, MatTableModule, MatRadioModule],
})
export class PoComponentModule {}
