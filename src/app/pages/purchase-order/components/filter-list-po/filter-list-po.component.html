<app-input-search [isFinishLoadingSubject]="finishLoadingSubject" [value]="searchInputValue" (actionFilter)="actionSearch($event)" placeholder="'Cari PO ID / Nama Distributor'" />
<div class="ms-auto position-relative">
  <div class="d-flex align-items-center justify-content-center">
    <div class="ms-3">
      <app-filter-table
        [isActiveFilter]="filterInputActivated"
        [isOpenFilter]="filterInputOpened"
        [isFinishLoadingSubject]="finishLoadingSubject"
        (actionClick)="toggleOpenFilter()"
        (actionReset)="actionResetFilter()"
      >
        <div class="menu menu-sub menu-sub-dropdown w-300px w-md-350px show filter-body" id="kt-toolbar-filter">
          <div class="px-7 py-5">
            <div class="d-flex align-items-center justify-content-between">
              <div class="fs-4 text-dark fw-bold">Filter</div>
              <div>
                <span class="svg-icon svg-icon2 cursor-pointer" [inlineSVG]="STRING_CONSTANTS.ICON.IC_CLOSE_MODAL" (click)="toggleOpenFilter()"></span>
              </div>
            </div>
          </div>

          <div class="separator border-gray-300"></div>

          <form [formGroup]="filterForm" class="form w-100" (ngSubmit)="actionSubmitFilter()">
            <div class="px-7 py-5">
              <div class="mb-10">
                <label class="form-label fs-5 mb-6">Status</label>
                <div class="filter-pills">
                  <mat-chip-listbox #filterPillsBox multiple (change)="actionStatusChange($event)">
                    <ng-container formArrayName="status">
                      <mat-chip-option class="chips" *ngFor="let item of filterPills" [value]="item">
                        <div class="d-flex align-items-center justify-content-between">
                          <span>{{ item }}</span>
                          <span class="custom-x-icon"></span>
                        </div>
                      </mat-chip-option>
                    </ng-container>
                  </mat-chip-listbox>
                </div>
              </div>

              <div *ngIf="useFilterExpiredDate" class="mb-10">
                <label class="form-label fs-5 mb-6">Tanggal Expired</label>
                <app-date-range-picker [formGroup]="filterForm" [nameEnd]="'exp_end_date'" [nameStart]="'exp_start_date'" />
              </div>

              <!-- CTA -->
              <div class="d-flex justify-content-end">
                <button mat-stroked-button (click)="actionResetFilter()" class="btn btn-outline me-4" type="reset" [disabled]="isSubmitDisabled()">
                  <span class="text-primary">Reset</span>
                </button>
                <button mat-raised-button color="primary" class="btn btn-primary" type="submit" [disabled]="isSubmitDisabled()">
                  <span class="text-white">Terapkan</span>
                </button>
              </div>
            </div>
          </form>
        </div>
      </app-filter-table>
    </div>
  </div>
</div>
