import { AfterViewInit, Component, HostBinding, Input, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, ActivatedRouteSnapshot, Router } from '@angular/router';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { UtilitiesService } from '@services/utilities.service';
import { FilterService } from '@services/filter.service';
import { BehaviorSubject } from 'rxjs';
import { EnumStatusPurchaseOrderString } from '@config/enum/status.enum';
import { MatChipListbox, MatChipListboxChange } from '@angular/material/chips';
import { STRING_CONSTANTS } from '@config/constants/string.constants';

@Component({
  selector: 'app-filter-list-po',
  templateUrl: './filter-list-po.component.html',
  styleUrls: ['./filter-list-po.component.scss'],
})
export class FilterListPOComponent implements OnInit, AfterViewInit {
  @HostBinding('class') class = 'd-flex w-100 justify-content-between';

  @Input() finishLoadingSubject = new BehaviorSubject(true);
  @Input() searchInputValue: string = '';
  @Input() filterInputActivated = false;
  @Input() useFilterExpiredDate = true;

  filterForm: FormGroup;
  filterInputOpened = false;
  filterPills: string[] = [];

  activeRouteSnapshot!: ActivatedRouteSnapshot;
  @ViewChild('filterPillsBox') filterPillsBox: MatChipListbox;

  constructor(private activatedRoute: ActivatedRoute, private router: Router, private fb: FormBuilder, private utils: UtilitiesService, private filterService: FilterService) {}

  get statusControl() {
    return <FormControl>this.filterForm.get('status');
  }

  get expStartDateControl() {
    return <FormControl>this.filterForm.get('exp_start_date');
  }

  get expEndDateControl() {
    return <FormControl>this.filterForm.get('exp_end_date');
  }

  ngOnInit() {
    this.activeRouteSnapshot = this.activatedRoute.snapshot;
    this.initFilterForm();
  }

  initFilterForm() {
    this.filterForm = this.fb.group({
      status: new FormControl(''),
      exp_start_date: new FormControl({ value: '', disabled: true }),
      exp_end_date: new FormControl({ value: '', disabled: true }),
    });

    if (!this.useFilterExpiredDate) {
      this.removeControl(['exp_start_date', 'exp_end_date']);
    }
    this.initFilterPills();
  }

  initFilterPillsBox() {
    const { queryParams } = this.activeRouteSnapshot;
    const queryParamIsEmpty = !Object.keys(queryParams).length;
    if (queryParamIsEmpty) return;
  }

  isSubmitDisabled = () => !this.statusControl.value && !this.expEndDateControl.value && !this.expStartDateControl.value;

  actionSearch = (e: string) => this.filterService.onSearch(e);

  actionStatusChange(e: MatChipListboxChange) {
    let _selectedKeys = <string[]>[];
    e.value.forEach((item: string) => _selectedKeys.push(this.utils.getEnumKeyByValue(EnumStatusPurchaseOrderString, item)));
    this.statusControl.patchValue(_selectedKeys.join());
  }

  actionSubmitFilter() {
    if (!this.statusControl.value) return;
    this.filterInputOpened = false;

    const _extras = {
      queryParams: {
        string_filter: this.searchInputValue,
        status: this.statusControl.value,
        exp_start_date: this.expStartDateControl ? this.utils.timeStampToDate(this.expStartDateControl.value, 'yyyy-MM-dd') : '',
        exp_end_date: this.expEndDateControl ? this.utils.timeStampToDate(this.expEndDateControl.value, 'yyyy-MM-dd') : '',
      },
    };

    return this.filterService.submitFilter(this.filterForm, _extras);
  }

  actionResetFilter() {
    this.filterInputOpened = false;
    this.resetFilterForm();
    this.resetFilterPillsBox();
  }

  toggleOpenFilter = () => (this.filterInputOpened = !this.filterInputOpened);

  resetFilterForm() {
    let _filterKeys = ['status', 'exp_start_date', 'exp_end_date'];
    if (!this.useFilterExpiredDate) _filterKeys = _filterKeys.slice(0, 1);
    return this.filterService.resetFilter(this.filterForm, _filterKeys);
  }

  resetFilterPillsBox = () => (this.filterPillsBox.value = []);

  private initFilterPills() {
    const _keys = Object.keys(EnumStatusPurchaseOrderString);
    return (this.filterPills = _keys.map((_item) => Object(EnumStatusPurchaseOrderString)[_item]));
  }

  private removeControl(ctrls: string[]) {
    ctrls.forEach((ctrl) => this.filterForm.removeControl(ctrl));
  }

  ngAfterViewInit() {
    this.initFilterPillsBox();
  }

  protected readonly STRING_CONSTANTS = STRING_CONSTANTS;
}
