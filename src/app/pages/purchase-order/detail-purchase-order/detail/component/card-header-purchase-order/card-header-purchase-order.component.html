<app-card [cardClasses]="'mt-7'">
  <ng-container cardBody>
    <span class="resume-title resume-title-space">{{ dataValue.code }}</span>
    <span class="fw-bold badge badge__status {{ 'badge__status--' + dataValue.status_enum }}">
      {{ dataValue.status_string }}
    </span>
  </ng-container>

  <ng-container cardBody>
    <div class="space-resume"></div>

    <div style="display: flex; justify-content: space-between">
      <div style="width: 25%">
        <div class="resume-label">Jumlah Produk</div>
        <div class="resume-value">
          {{ dataValue.total_product }}
        </div>
      </div>
      <div style="width: 25%">
        <div class="resume-label">Total Box</div>
        <div class="resume-value">
          {{ dataValue.total_sale_unit }}
        </div>
      </div>
      <div style="width: 25%">
        <div class="resume-label">Metode Pembayaran</div>
        <div class="resume-value">
          {{ dataValue.payment_type }}
        </div>
      </div>
      <div style="width: 25%">
        <div class="resume-label">Total Harga</div>
        <div class="resume-value">Rp {{ dataValue.final_price | number : '.2' : 'id' }}</div>
      </div>
    </div>

    <div class="border-top space"></div>
    <div class="space"></div>
  </ng-container>

  <ng-container cardBody>
    <div class="row">
      <div *ngFor="let approval of resumeApprovalDate | async" [ngClass]="{ 'col-md-3': !!dataValue.approved_finance_changes, 'col-md-4': !dataValue.approved_finance_changes }">
        <div class="d-flex">
          <div class="symbol symbol-40px symbol-fixed position-relative">
            <app-skeleton-text [isLoading]="isLoading" type="image">
              <div class="icon">
                <span [inlineSVG]="approval.icon"></span>
              </div>
            </app-skeleton-text>
          </div>

          <div style="margin-left: 16px">
            <app-skeleton-text [isLoading]="isLoading" class="resume-label" type="text">
              {{ approval.label }}
            </app-skeleton-text>
            <app-skeleton-text [isLoading]="isLoading" class="resume-value flex-column" type="text">
              <div *ngIf="approval.approve_by">
                {{ approval.approve_by }}
              </div>
              <div>
                {{ approval.value ?? '-' }}
              </div>
            </app-skeleton-text>
          </div>
        </div>
      </div>
    </div>
  </ng-container>
</app-card>
