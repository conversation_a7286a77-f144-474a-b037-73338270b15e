import { Component, Input, OnInit } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { IApprovalDatePurchaseOrder, IPurchaseOrderDetail } from '../../../../purchase.order.interface';
import { STRING_CONSTANTS } from '@config/constants/string.constants';
import { UtilitiesService } from '@services/utilities.service';

@Component({
  selector: 'app-card-header-purchase-order',
  templateUrl: './card-header-purchase-order.component.html',
  styleUrls: ['./card-header-purchase-order.component.scss'],
})
export class CardHeaderPurchaseOrderComponent implements OnInit {
  @Input() isLoading: BehaviorSubject<boolean>;
  @Input() data: BehaviorSubject<IPurchaseOrderDetail>;

  resumeApprovalDate: BehaviorSubject<IApprovalDatePurchaseOrder[]> = new BehaviorSubject<IApprovalDatePurchaseOrder[]>([]);

  get dataValue() {
    return this.data.value;
  }

  constructor(private utilities: UtilitiesService) {}

  ngOnInit(): void {
    this.setData();
  }

  setData() {
    this.data.subscribe((value) => {
      if (value) {
        this.generateApprovalDate(value);
      }
    });
  }

  getDateFormat(dateStr: number): string {
    if (dateStr) {
      const date = this.utilities.formatEpochToDate(dateStr, 'dd-MM-yyyy');
      const hour = this.utilities.formatEpochToDate(dateStr, 'HH:mm:ss');
      return `${date} ${hour}`;
    } else {
      return '-';
    }
  }

  generateApprovalDate(val: IPurchaseOrderDetail) {
    if (!val) return;

    const dataResumeApprovalDate: IApprovalDatePurchaseOrder[] = [
      { icon: STRING_CONSTANTS.ICON.IC_DATE_CREATE, label: 'Tanggal Dibuat', value: null },
      { icon: STRING_CONSTANTS.ICON.IC_PEOPLE, label: 'Approval Regional Head', value: null },
      { icon: STRING_CONSTANTS.ICON.IC_PEOPLE, label: 'Approval Regional Director', value: null },
    ];

    this.resumeApprovalDate.next(dataResumeApprovalDate);

    const { approved_regional_head_changes, approved_regional_director_changes, approved_finance_changes, created_changes } = val;

    if (!!created_changes) {
      const createdDateData = this.resumeApprovalDate.value.find((i) => i.label === 'Tanggal Dibuat');
      if (createdDateData) {
        createdDateData.value = this.getDateFormat(created_changes.date);
      }
    }

    if (!!approved_regional_head_changes) {
      const regionalHeadData = this.resumeApprovalDate.value.find((i) => i.label === 'Approval Regional Head');
      if (regionalHeadData) {
        regionalHeadData.value = this.getDateFormat(approved_regional_head_changes.date);
        regionalHeadData.approve_by = approved_regional_head_changes.actor;
      }
    }

    if (!!approved_regional_director_changes) {
      const regionalDirectorData = this.resumeApprovalDate.value.find((i) => i.label === 'Approval Regional Director');
      if (regionalDirectorData) {
        regionalDirectorData.value = this.getDateFormat(approved_regional_director_changes.date);
        regionalDirectorData.approve_by = approved_regional_director_changes.actor;
      }
    }

    if (!!approved_finance_changes) {
      this.resumeApprovalDate.value.push({
        icon: STRING_CONSTANTS.ICON.IC_PEOPLE,
        label: 'Approval Finance',
        approve_by: approved_finance_changes.actor,
        value: this.getDateFormat(approved_finance_changes.date),
      });
    }
  }
}
