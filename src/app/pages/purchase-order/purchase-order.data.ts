export const purchaseOrderTableColumns = [
  {
    key: 'code',
    title: 'PO ID',
    isSortable: false,
  },
  {
    key: 'distributor_name',
    title: 'Nama Distributor',
    isSortable: false,
  },
  {
    key: 'total_box_item',
    title: 'Total Box',
    isSortable: false,
  },
  // purchaseOrder.expirationDate
  {
    key: 'expirationDate',
    title: 'Tanggal Expired',
    isSortable: true,
  },
  {
    key: 'status',
    title: 'Status',
    isSortable: false,
  },
  {
    key: 'actions',
    title: 'Actions',
  },
];

export const purchaseOrderTablePendingColumns = [
  {
    key: 'code',
    title: 'PO ID',
    isSortable: false,
  },
  {
    key: 'distributor_name',
    title: 'Nama Distributor',
    isSortable: false,
  },
  {
    key: 'total_qty_item',
    title: 'Total QTY',
    isSortable: false,
  },
  {
    key: 'regional_head',
    title: 'Regional Head',
  },
  {
    key: 'regional_director',
    title: 'Regional Director',
  },
  {
    key: 'status',
    title: 'Status',
    isSortable: false,
  },
];

export const productCreateSoTableColumns = [
  {
    key: 'product_name',
    title: 'Produk',
    isSortable: false,
  },
  {
    key: 'qty_order',
    title: 'QTY PESAN',
    isSortable: false,
  },
  {
    key: 'qty_fulfilled',
    title: 'QTY DIPENUHI',
    isSortable: false,
  },
  {
    key: 'qty_less',
    title: 'QTY KURANG',
    isSortable: false,
  },
  {
    key: 'note',
    title: '',
    isSortable: false,
  },
];

export const productCreateSoTablePendingColumns = [
  {
    key: 'product_name',
    title: 'PRODUK',
  },
  {
    key: 'qty_sale_unit',
    title: 'TOTAL QUANTITY',
  },
  {
    key: 'qty_delivery_unit',
    title: 'TOTAL BERAT/VOLUME',
  },
  {
    key: 'total_price',
    title: 'TOTAL HARGA',
  },
];
