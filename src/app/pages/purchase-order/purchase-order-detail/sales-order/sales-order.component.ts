import { ChangeDetector<PERSON><PERSON>, Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { STRING_CONSTANTS } from '@config/constants/string.constants';
import { BehaviorSubject, Subscription } from 'rxjs';
import { TableColumn } from '@shared/interface/table.interface';
import { BaseDatasource } from '@shared/base/base.datasource';
import { ActivatedRoute, Router } from '@angular/router';
import { BaseTableService } from '@shared/base/base-table-service.service';
import { API } from '@config/constants/api.constant';
import { UtilitiesService } from '@services/utilities.service';
import { StatusSalesOrder } from '@config/enum/status.enum';
import { RolePrivilegeService } from '@services/role-privilege.service';
import { UrlUtilsService } from '@utils/url-utils.service';
import { PurchaseOrderService } from '../../purchase-order.service';

export interface TableSoListDetailP0 {
  code: string;
  id: string;
  distributor_name: any;
  total_box: number;
  exp_date: string;
  status_enum: string;
  status_enum_string: string;
  product_item_list: ProductItemList[];
}

interface ProductItemList {
  image_url: string;
  name: string;
}

interface ProductItemDataMapping {
  name: string;
  image_url: string;
  count: number;
}

@Component({
  selector: 'app-sales-order',
  templateUrl: './sales-order.component.html',
  styleUrls: ['./sales-order.component.scss'],
})
export class SalesOrderComponent implements OnInit, OnDestroy {
  // @Input() id: string | null = '';

  isLoading: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(true);
  isHasRecord = true;
  string_filter = '';

  STRING_CONSTANTS = STRING_CONSTANTS;
  iconNone = STRING_CONSTANTS.ICON.IC_TASK;
  // table
  SOList: TableSoListDetailP0[];
  tableList: BehaviorSubject<TableSoListDetailP0[]> = new BehaviorSubject<TableSoListDetailP0[]>([]);
  tableColumns: TableColumn[];
  displayedColumns: string[];
  baseDatasource: BaseDatasource<TableSoListDetailP0>;
  statusPurchaseOrder = StatusSalesOrder;
  privilegeDetailSO: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);

  private unsubscribe: Subscription[] = [];

  constructor(
    private activeRoute: ActivatedRoute,
    private baseTableService: BaseTableService<TableSoListDetailP0>,
    private urlParamService: UrlUtilsService,
    private ref: ChangeDetectorRef,
    public utilities: UtilitiesService,
    private router: Router,
    private rolePrivilegeService: RolePrivilegeService,
    private purchaseOrderService: PurchaseOrderService
  ) {}

  ngOnInit(): void {
    const _dataSubs = this.baseTableService.responseDatabase.subscribe((response) => (this.baseDatasource = response));

    this.handlePrivilege();
    this.setTableData();
    this.queryHandler();
    this.initPageInfo();

    this.unsubscribe.push(_dataSubs);
  }

  handlePrivilege() {
    this.privilegeDetailSO.next(this.rolePrivilegeService.checkPrivilege('PURCHASE_ORDER', 'DETAIL_PURCHASE_ORDER', 'TAB_SALES_ORDER_IN_PO', 'CTA_VIEW_DETAIL'));
  }

  setTableData() {
    this.tableColumns = [
      {
        key: 'code',
        title: 'SO ID',
      },
      {
        key: 'name',
        title: 'PRODUK',
      },
      {
        key: 'total_box',
        title: 'TOTAL QTY',
      },
      {
        key: 'createdDate',
        title: 'TANGGAL DIBUAT',
      },
      {
        key: 'status',
        title: 'STATUS',
      },
    ];

    if (this.privilegeDetailSO.value) {
      this.tableColumns.push({ key: 'actions', title: 'ACTION' });
    }

    this.displayedColumns = this.tableColumns.map((item) => item.key);
    this.baseDatasource = new BaseDatasource();

    if (this.tableList.value) {
      this.baseDatasource.tableSubject.next(this.tableList.value);
      this.ref.detectChanges();
    }
    this.baseDatasource.tableSubject.subscribe((value) => {
      if (value) {
        this.isLoading.next(false);
      }
    });
  }

  queryHandler() {
    const paramSubs = this.activeRoute.queryParams.subscribe((data) => {
      this.string_filter = data.string_filter;
      const _queryParams = this.urlParamService.sliceQueryParams();
      this.loadDataTable(_queryParams ? _queryParams : '');
    });

    this.unsubscribe.push(paramSubs);
  }

  loadDataTable(params: string) {
    this.baseTableService.loadDataTable(API.LIST_SO_DETAIL_PO + this.purchaseOrderService.PoDetailID, params);
  }

  initPageInfo() {
    const dataSubs = this.baseDatasource.tableSubject.subscribe((resp) => (this.SOList = resp));
    this.isLoading.next(false);
    this.unsubscribe.push(dataSubs);
  }

  setProductItem(item: ProductItemList[]): ProductItemDataMapping | undefined {
    if (item?.length) {
      const data = item[0];
      return {
        count: item.length - 1,
        image_url: data.image_url,
        name: data.name,
      };
    }
  }

  handleAction(id: string, status: string) {
    this.router.navigate([`/sales-order/${status.toLowerCase()}/${id}`]).then();
  }

  handleNoImage = (e: Event) => this.utilities.setDefaultImageProduct(e);

  ngOnDestroy() {
    this.unsubscribe.forEach((sb) => sb.unsubscribe());
  }
}
