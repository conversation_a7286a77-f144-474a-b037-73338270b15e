<app-card *ngIf="showReason()" [borderHeader]="true" [border]="true" [cardClasses]="'mb-7'" [cardHeaderTitle]="reasonNote.value.title" [header]="true">
  <ng-container cardBody>
    <div *ngIf="reasonNote.value.suggestion; else notesByFinance" class="row font-14 fw-semibold">
      <div class="col-lg-2 mb-4">Saran</div>
      <div class="col-lg-10 mb-4">: {{ reasonNote.value.suggestion }}</div>
      <div class="col-lg-2">Keterangan</div>
      <div class="col-lg-10">: {{ reasonNote.value.description }}</div>
    </div>
    <ng-template #notesByFinance>
      <ul>
        <li>{{ reasonNote.value.description }}</li>
      </ul>
    </ng-template>
  </ng-container>
</app-card>

<!--expired PO 5 hari lagi-->
<app-card-notif-expired *ngIf="expiredDays === 5" [date]="expiredDate" [days]="expiredDays"></app-card-notif-expired>

<!--waktu expired PO >5 hari-->
<app-card-in-progress *ngIf="isExpired('progress', 5)" [expirationDate]="expiredDate"></app-card-in-progress>

<!--PO Expired, >31 hari-->
<app-card-expired *ngIf="isExpired('expired', 0)" [expirationDate]="expiredDate"></app-card-expired>

<!--Card Header PO-->
<app-card-header-purchase-order [data]="dataDetail" [isLoading]="isLoading" />

<div class="container-fluid g-0 overflow-hidden">
  <div class="row">
    <div class="col-12 col-md-6">
      <app-card-information-distributor [data]="dataDetail" [isLoading]="isLoading" />
    </div>
    <div class="col-12 col-md-6">
      <app-card-information-delivery [data]="dataDetail" [isLoading]="isLoading" />
    </div>
  </div>
</div>

<app-card *ngIf="!!dataDetail.value.distributor_note_order" [cardClasses]="'mt-7'" [cardHeaderClasses]="'pb-0'" [cardHeaderTitle]="'Catatan Distributor'" [header]="true">
  <ng-container cardBody>
    <div>{{ dataDetail.value.distributor_note_order }}</div>
  </ng-container>
</app-card>

<app-card-program-marketing-list [id]="id" [url]="API.PROGRAM_MARKETING.GET_PROGRAM_MARKETING_PURCHASE_ORDER" />

<app-card-product-order-list #cardProductOrder [id]="id" [url]="API.PURCHASE_ORDER.GET_PRODUCT_LIST_DETAIL_PO" />

<app-card-price-summary-v1 [url]="API.PURCHASE_ORDER.GET_PRICE_SUMMARY_DETAIL_PO + id" />

<ng-container *ngIf="shouldShowApprovalSection(dataDetail.value.status_enum)">
  <!-- APPROVAL SECTION -->
  <div class="btn-print d-flex justify-content-end align-items-center mt-6 mb-20">
    <div class="action-button-right d-flex justify-content-end align-items-center">
      <button (click)="handleReject()" class="btn btn-outline btn-outline-danger"><span>Reject</span></button>
      <button (click)="handleApprove()" class="btn btn-primary ms-4"><span>Approve</span></button>
    </div>
  </div>

  <app-modal #modalApprove [modalConfig]="modalApproveConfig">
    <div>
      <span>Apakah anda yakin menyetujui diskon dari produk:</span>
      <ul>
        <li *ngFor="let item of cardProductOrder.listProduct">
          <span>{{ item.product_name }} - {{ item.discount_sales }}%</span>
        </li>
      </ul>
    </div>
  </app-modal>

  <app-modal #modalReject [modalConfig]="modalRejectConfig">
    <div>
      <p>Silahkan input alasan penolakan Purchase Order.</p>
      <textarea [(ngModel)]="rejectReason" class="form-control form-control-solid min-h-100px" cols="10" placeholder="Silahkan input alasan penolakan PO" rows="3"></textarea>
    </div>
  </app-modal>

  <app-modal #modalApproveResponse [modalConfig]="modalApproveResponseConfig">
    <div class="d-flex flex-column justify-content-center align-items-center mt-8">
      <ng-container *ngIf="(isLoadingApproval | async) === false; else loaderSpinner">
        <span [inlineSVG]="iconSuccess" class="mb-6"></span>
        <div class="text-center">{{ approvalResponseMessage }}</div>
      </ng-container>

      <ng-template #loaderSpinner>
        <mat-spinner></mat-spinner>
      </ng-template>
    </div>
  </app-modal>
</ng-container>
