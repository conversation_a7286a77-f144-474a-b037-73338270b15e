import { Component, Input, OnInit } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { IPurchaseOrderDetail } from '../../../../purchase.order.interface';
import { ICardDetailBody } from '@models/card.model';

@Component({
  selector: 'app-card-information-delivery',
  templateUrl: './card-information-delivery.component.html',
  styleUrls: ['./card-information-delivery.component.scss'],
})
export class CardInformationDeliveryComponent implements OnInit {
  @Input() data: BehaviorSubject<IPurchaseOrderDetail>;
  @Input() isLoading: BehaviorSubject<boolean>;

  detailDelivery: BehaviorSubject<ICardDetailBody[]> = new BehaviorSubject<ICardDetailBody[]>([]);

  constructor() {}

  ngOnInit(): void {
    this.setData();
  }

  setData() {
    this.data.subscribe((value) => {
      if (Object.keys(value).length === 0) return;
      const delivery = value.delivery_information;
      const detailDelivery: ICardDetailBody[] = [
        {
          label: 'PIC Distributor',
          value: delivery.receiver_name,
        },
        {
          label: 'No Telephone',
          value: delivery.receiver_number,
        },
        {
          label: 'Alamat Pengiriman',
          // value: `${value.address_sub_district}, ${value.address_district}, ${value.address_regency}, ${value.address_province}`,
          value: `${delivery.label}`,
          value_bottom: `${delivery.full_address}`,
        },
      ];
      this.detailDelivery.next(detailDelivery);
    });
  }
}
