import { Component, Input, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { STRING_CONSTANTS } from '@config/constants/string.constants';
import { BehaviorSubject } from 'rxjs';

@Component({
  selector: 'app-card-expired',
  templateUrl: './card-expired.component.html',
  styleUrls: ['./card-expired.component.scss'],
})
export class CardExpiredComponent implements OnInit {
  @Input() expirationDate: string;

  STRING_CONSTANTS = STRING_CONSTANTS;
  isLoading: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(true);
  id: any | null = null;

  constructor(private activatedRoute: ActivatedRoute) {}

  ngOnInit(): void {
    this.activatedRoute.paramMap.subscribe((params) => {
      this.id = params.get('id');
    });
    if (this.id) {
      this.isLoading.next(false);
    }
  }
}
