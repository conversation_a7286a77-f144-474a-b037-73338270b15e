import { Component, Input, OnInit, ViewChild } from '@angular/core';
import { STRING_CONSTANTS } from '@config/constants/string.constants';
import { IProgramMarketing, IProgramMarketingList } from '../../../../purchase.order.interface';
import { UtilitiesService } from '@services/utilities.service';
import { BehaviorSubject } from 'rxjs';
import { ModalDetailPromagComponent } from '@shared/components/modal-detail-promag/modal-detail-promag.component';
import {IProgramMarketingDetail} from "../../../../../sales-marketing/program-marketing/program-marketing.interface";
import {
  EnumProgramMarketingDiscountCategory,
  EnumProgramMarketingType
} from "../../../../../sales-marketing/program-marketing/program-marketing.enum";
import {ProgramMarketingService} from "../../../../../sales-marketing/program-marketing/program-marketing.service";

@Component({
  selector: 'app-program-marketing-note-view',
  templateUrl: './program-marketing-note-view.component.html',
  styleUrls: ['./program-marketing-note-view.component.scss'],
})
export class ProgramMarketingNoteViewComponent implements OnInit {
  @Input() data!: IProgramMarketingList;

  STRING_CONSTANTS = STRING_CONSTANTS;
  detail: BehaviorSubject<IProgramMarketingDetail> = new BehaviorSubject<IProgramMarketingDetail>({} as IProgramMarketingDetail);

  @ViewChild('modalDetail') private modalDetail: ModalDetailPromagComponent;

  get informationProgram() {
    return this.detail.value.information;
  }

  get termProgram() {
    return this.detail.value.program_term;
  }

  get orderTermProgram() {
    return this.detail.value.order_term;
  }

  constructor(public utils: UtilitiesService, private programMarketingService: ProgramMarketingService) {}

  ngOnInit() {}

  openModal(value: IProgramMarketing, title: string) {
    return this.modalDetail.openModal(value.detail, title);
  }

  getProgramMarketingType(detail: IProgramMarketingDetail) {
    return !this.utils.isObjectEmpty(detail) ? detail.information.program_type_enum : this.programMarketingService.ProgramMarketingType;
  }

  protected readonly EnumProgramMarketingType = EnumProgramMarketingType;
  protected readonly EnumProgramMarketingDiscountCategory = EnumProgramMarketingDiscountCategory;
}
