<div class="note-view align-items-center primary">
  <div *ngFor="let program of data.program_marketing_list; let i = index; last as isLast">
    <div class="d-flex">
      <span class="svg-icon icon-note-view me-6" inlineSVG="{{ STRING_CONSTANTS.ICON.IC_PROMO }}"></span>
      <div class="text-black font-14 w-100 d-flex justify-content-between">
        <p class="mb-0 fw-bold">{{ program.program_name }}</p>
        <p (click)="openModal(program, program.program_name)" class="mb-0 cursor-pointer fw-bold text-primary">
          <u>Lihat Detail</u>
        </p>
      </div>
    </div>
    <div class="ms-13">
      <ng-container [ngSwitch]="getProgramMarketingType(program.detail)">
        <ng-container *ngSwitchCase="EnumProgramMarketingType.ONE_SHOOT" [ngTemplateOutlet]="oneShootTpl"></ng-container>
        <ng-container *ngSwitchCase="EnumProgramMarketingType.DISCOUNT_PRODUCT" [ngTemplateOutlet]="discountProductTpl"></ng-container>
        <ng-container *ngSwitchCase="EnumProgramMarketingType.DISCOUNT_PURCHASE" [ngTemplateOutlet]="discountPurchaseTpl"></ng-container>
        <ng-container *ngSwitchDefault [ngTemplateOutlet]="oneShootTpl"></ng-container>
      </ng-container>

      <ng-template #oneShootTpl>
        <ul class="my-2">
          <li>
            <ng-container *ngIf="program.detail.reward?.non_mai_product; else rewardViewTpl">
              Produk Hadiah:
              <span class="text-gray-700">
                {{ modalDetail.displayRewardItem(program.detail.reward)
                }}<span class="text-black"> -> {{ program.detail.reward?.non_mai_product?.product_other_reward ?? '-' }}</span>
              </span>
            </ng-container>

            <ng-template #rewardViewTpl> Hadiah: {{ program.reward }} </ng-template>
          </li>
        </ul>
      </ng-template>

      <ng-template #discountProductTpl>
        <div>Diskon :</div>
        <ul>
          <li *ngFor="let item of program.discounts">{{ item }}</li>
        </ul>
      </ng-template>

      <ng-template #discountPurchaseTpl>
        <div>Diskon :</div>
        <ul>
          <li *ngFor="let item of program.discounts">{{ item }}</li>
        </ul>
      </ng-template>
    </div>
    <hr *ngIf="!isLast" class="my-5" />
  </div>
</div>

<app-modal-detail-promag #modalDetail [haveValue]="true" />
