import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { BehaviorSubject, Subscription } from 'rxjs';
import { IProgramMarketingList } from '../../../../purchase.order.interface';
import { BaseService } from '@services/base-service.service';

@Component({
  selector: 'app-card-program-marketing-list',
  templateUrl: './card-program-marketing-list.component.html',
  styleUrls: ['./card-program-marketing-list.component.scss'],
})
export class CardProgramMarketingListComponent implements OnInit, OnDestroy {
  @Input() id!: string;
  @Input() url!: string;

  isLoading: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  programMarketingList: BehaviorSubject<IProgramMarketingList> = new BehaviorSubject<IProgramMarketingList>({} as IProgramMarketingList);

  private unsubscribe: Subscription[] = [];

  constructor(private baseService: BaseService) {}

  ngOnInit() {
    this.getProgramMarketing();
  }

  getProgramMarketing() {
    this.isLoading.next(true);
    const data = this.baseService.getData<IProgramMarketingList>(this.url + this.id).subscribe((value) => {
      if (!value) return;
      this.programMarketingList.next(value.data);
      this.isLoading.next(false);
    });
    this.unsubscribe.push(data);
  }

  ngOnDestroy() {
    this.unsubscribe.forEach((sb) => sb.unsubscribe());
  }
}
