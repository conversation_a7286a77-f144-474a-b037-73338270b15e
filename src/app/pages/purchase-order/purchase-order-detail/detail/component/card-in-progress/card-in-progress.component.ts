import { Component, Input, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { STRING_CONSTANTS } from '@config/constants/string.constants';
import { BehaviorSubject } from 'rxjs';

@Component({
  selector: 'app-card-in-progress',
  templateUrl: './card-in-progress.component.html',
  styleUrls: ['./card-in-progress.component.scss'],
})
export class CardInProgressComponent implements OnInit {
  @Input() expirationDate: string;

  STRING_CONSTANTS = STRING_CONSTANTS;
  isLoading: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(true);
  id: any | null = null;

  constructor(private activatedRoute: ActivatedRoute) {}

  ngOnInit(): void {
    this.activatedRoute.paramMap.subscribe((params) => {
      this.id = params.get('id');
    });
    if (this.id) {
      this.isLoading.next(false);
    }
  }
}
