import { Component, Input, OnInit } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { IPurchaseOrderDetail } from '../../../../purchase.order.interface';
import { ICardDetailBody } from '@models/card.model';

@Component({
  selector: 'app-card-information-distributor',
  templateUrl: './card-information-distributor.component.html',
  styleUrls: ['./card-information-distributor.component.scss'],
})
export class CardInformationDistributorComponent implements OnInit {
  @Input() data: BehaviorSubject<IPurchaseOrderDetail>;
  @Input() isLoading: BehaviorSubject<boolean>;

  detailDistributor: BehaviorSubject<ICardDetailBody[]> = new BehaviorSubject<ICardDetailBody[]>([]);

  constructor() {}

  ngOnInit() {
    this.setData();
  }

  setData() {
    this.data.subscribe((value) => {
      if (Object.keys(value).length === 0) return;
      const distributor = value.detail_distributor;
      const detailDistributor: ICardDetailBody[] = [
        {
          label: 'Distributor ID',
          value: distributor.distributor_code,
        },
        {
          label: 'Nama Distributor',
          value: distributor.distributor_name,
        },
        {
          label: 'Alamat',
          value: `${distributor.full_address}`,
        },
      ];
      this.detailDistributor.next(detailDistributor);
    });
  }
}
