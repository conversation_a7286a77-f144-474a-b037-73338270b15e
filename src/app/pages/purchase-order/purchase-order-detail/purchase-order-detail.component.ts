import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Data, Router } from '@angular/router';
import { PageInfoService, PageLink } from '@metronic/layout';
import { DetailPurchaseOrderTab, StatusPurchaseOrderEnum, SubMenu } from '../submenu.enum';
import { StatusPurchaseOrder, StatusSalesOrder } from '@config/enum/status.enum';
import { IInitPurchaseOrderDetail } from '../purchase.order.interface';
import { BehaviorSubject } from 'rxjs';
import { UtilitiesService } from '@services/utilities.service';
import { STRING_CONSTANTS } from '@config/constants/string.constants';
import { AuthService } from '@services/auth.service';
import { ProductComponent } from './product/product.component';
import { DetailComponent } from './detail/detail.component';
import { SalesOrderComponent } from './sales-order/sales-order.component';
import { PurchaseOrderService } from '../purchase-order.service';
import { OrderService } from '@services/order.service';
import { MatTabChangeEvent } from '@angular/material/tabs';
import { ITabComponent } from '@shared/interface/mai-tab-group.interface';
import { PermissionService } from '@services/permission.service';
import { IPrivilegeChild2 } from '@shared/interface/role.interface';

@Component({
  selector: 'app-purchase-order-detail',
  templateUrl: './purchase-order-detail.component.html',
})
export class PurchaseOrderDetailComponent implements OnInit {
  // params
  routeData!: Data;
  subMenu: string | null = '';
  id: any | null = null;
  isWithTab: boolean = false;
  links: Array<PageLink>;

  STRING_CONSTANTS = STRING_CONSTANTS;
  // New
  isLoading: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(true);
  userRole: BehaviorSubject<string> = new BehaviorSubject<string>('');
  initDetail: BehaviorSubject<IInitPurchaseOrderDetail | null> = new BehaviorSubject<IInitPurchaseOrderDetail | null>(null);
  defaultTabIndex = 0;
  tabComponents: ITabComponent[] = [
    {
      index: 0,
      title: 'Produk Menunggu',
      component: ProductComponent,
      enum: 'LIST_NEED_FULLFILL',
      enum_privilege: 'TAB_LIST_NEED_FULLFILL',
      disabled: false,
    },
    {
      index: 1,
      title: 'Detail PO',
      component: DetailComponent,
      enum: 'DETAIL_PO',
      enum_privilege: 'TAB_DETAIL_PO',
      disabled: false,
    },
    {
      index: 2,
      title: 'Sales Order',
      component: SalesOrderComponent,
      enum: 'SALES_ORDER',
      enum_privilege: 'TAB_SALES_ORDER_IN_PO',
      disabled: false,
    },
  ];
  privilegeTabs!: Array<IPrivilegeChild2>;

  constructor(
    private activatedRoute: ActivatedRoute,
    private router: Router,
    private pageInfoService: PageInfoService,
    public utilities: UtilitiesService,
    private purchaseOrderService: PurchaseOrderService,
    public auth: AuthService,
    public permissionService: PermissionService,
    private orderService: OrderService
  ) {}

  get isAllowProductNeedFullFillTab(): boolean {
    const noTabNeedFullfill = [
      StatusPurchaseOrder.CREATED,
      StatusPurchaseOrder.NEED_CHANGE_ORDER_BY_DISTRIBUTOR,
      StatusPurchaseOrder.NEED_CHANGE_ORDER_BY_RH,
      StatusPurchaseOrder.ON_PROGRESS,
      StatusPurchaseOrder.COMPLETED,
      StatusPurchaseOrder.NEED_APPROVE_REGIONAL_FINANCE,
      StatusPurchaseOrder.NEED_APPROVE_REGIONAL_DIRECTOR,
      StatusPurchaseOrder.NEED_CHANGE_DISCOUNT,
      StatusPurchaseOrder.CANCEL_BY_DISTRIBUTOR,
      StatusPurchaseOrder.CANCEL_BY_RH,
      StatusPurchaseOrder.CANCEL_BY_MARKETING,
      StatusPurchaseOrder.EXPIRED,
    ];

    const filter = !noTabNeedFullfill.find((val) => val === this.initDetail.value?.po_status);
    return this.isAllowAccess('TAB_LIST_NEED_FULLFILL') && filter;
  }

  get isAllowSalesOrderTab(): boolean {
    const noTabSO = [
      StatusPurchaseOrder.CREATED,
      StatusPurchaseOrder.NEED_APPROVE_REGIONAL_FINANCE,
      StatusPurchaseOrder.NEED_CHANGE_ORDER_BY_DISTRIBUTOR,
      StatusPurchaseOrder.NEED_APPROVE_REGIONAL_DIRECTOR,
      StatusPurchaseOrder.NEED_CHANGE_DISCOUNT,
      StatusPurchaseOrder.NEED_CHANGE_ORDER_BY_RH,
      StatusPurchaseOrder.CANCEL_BY_DISTRIBUTOR,
      StatusPurchaseOrder.CANCEL_BY_RH,
      StatusPurchaseOrder.CANCEL_BY_MARKETING,
      StatusPurchaseOrder.RESERVED,
      StatusPurchaseOrder.EXPIRED,
    ];
    const filter = !noTabSO.find((val) => val === this.initDetail.value?.po_status);
    return this.isAllowAccess('TAB_SALES_ORDER_IN_PO') && filter;
  }

  async ngOnInit() {
    this.routeData = history.state.data;
    this.handleQueryParam();
    this.handlePrivilege();
    if (!!this.routeData) return await this.handleReturnUrl();
    this.initPageInfo();
    this.userRole.next(this.auth.getRoleFromToken());
    this.checkingTab();
  }

  handleQueryParam() {
    const { subMenu, id } = this.activatedRoute.snapshot.params;
    this.subMenu = subMenu;
    this.purchaseOrderService.PoDetailID = id;
  }

  initPageInfo() {
    this.pageInfoService.updateTitle('Detail PO');

    this.purchaseOrderService.getInitialDetailSO(this.purchaseOrderService.PoDetailID).subscribe((value) => {
      if (!value) return;
      const { data } = value;
      this.initDetail.next(data);
      this.handleDetail();
      this.isLoading.next(false);
    });
    return;
  }

  handleDetail() {
    const { po_code } = this.purchaseOrderService.InitPoDetail;

    this.links = [
      {
        title: 'Purchase Order',
        path: '',
        isActive: false,
      },
      {
        title: '',
        path: '',
        isActive: false,
        isSeparator: true,
      },
      {
        title: this.getSubMenuTitle(this.subMenu as string),
        path: `purchase-order/${this.subMenu}`,
        isActive: false,
      },
      {
        title: '',
        path: '',
        isActive: false,
        isSeparator: true,
      },
      {
        title: '',
        path: '',
        isActive: true,
        attributes: po_code,
      },
    ];

    this.pageInfoService.updateBreadcrumbs(this.links);
  }

  getSubMenuTitle(subMenu: string) {
    if (!subMenu) {
      return;
    }

    let title;
    const textFromEnum: string = this.utilities.mapKeyToString(SubMenu, subMenu);
    title = textFromEnum ? textFromEnum : 'List Purchase Order';

    return title;
  }

  handlePrivilege() {
    this.privilegeTabs = this.permissionService.checkPrivilegeTabs('PURCHASE_ORDER', 'DETAIL_PURCHASE_ORDER');
  }

  isAllowAccess(tabName: string) {
    return this.privilegeTabs.filter((value) => value.name === tabName).length > 0;
  }

  checkingTab() {
    const _statusSO = [StatusSalesOrder.REQUEST, StatusSalesOrder.CREATED, StatusSalesOrder.CONFIRMED, StatusSalesOrder.PARTIAL_COMPLETED, StatusSalesOrder.OUTSTANDING];
    this.initDetail.subscribe((value) => {
      if (value) {
        const status = value.po_status;
        if (status) {
          this.isWithTab = !!this.orderService.filterStatusOrder(_statusSO, status);
        }
      }
    });
  }

  async handleReturnUrl() {
    // parsing url
    const _params = this.activatedRoute.snapshot.params['id'];
    const _batchID = _params.split('?')[0];

    let _batchCode, _batchEnum;
    let _data!: { status_enum: string; status_string: string; id: string; job_code: string };

    if (_params.split('?')[1]) {
      // handle params from login page
      const _batch = _params.split('?')[1].split('=');
      _batchCode = _batch[1].split('&tab')[0];
      _batchEnum = _batch[2];

      _data = {
        status_enum: _batchEnum,
        status_string: this.utilities.mapKeyToString(StatusPurchaseOrderEnum, _batchEnum),
        id: _batchID,
        job_code: _batchCode,
      };

      return this.handleTabRouteData(_data).then(() => location.reload());
    } else {
      return this.handleInitPage();
    }
  }

  async handleInitPage() {
    this.initPageInfo();
    this.getSelectedTabIndex();
  }

  getSelectedTabIndex(tabEnum = '') {
    const _tabEnum = !!tabEnum ? tabEnum : this.activatedRoute.snapshot.queryParams['tab'];
    const _selectedTab = this.tabComponents.find((item) => item.enum === _tabEnum);
    this.defaultTabIndex = (_selectedTab?.index as number) ?? 0;
  }

  handleTabChange(e: MatTabChangeEvent) {
    const _submenu = this.activatedRoute.snapshot.params['subMenu'];
    const _status = this.activatedRoute.snapshot.params['statusPurchaseOrder'];
    const _id = this.activatedRoute.snapshot.params['id'];
    const _tabEnum = e.tab.textLabel;
    const _tabString = this.utilities.mapKeyToString(DetailPurchaseOrderTab, _tabEnum);

    let _routeData = this.routeData;
    _routeData =
      _routeData !== undefined
        ? {
            ..._routeData,
            status_enum: _tabEnum,
            status_string: _tabString,
          }
        : {
            status_enum: _tabEnum,
            status_string: _tabString,
            submenu: _submenu,
            status: _status,
            id: _id,
          };

    return this.handleTabRouteData(_routeData);
  }

  handleTabRouteData(data: Data) {
    const _url = `/purchase-order/${data['submenu']}/${data['status']}/${data['id']}`;
    return this.router.navigate([_url], {
      state: { data },
      queryParams: {
        tab: data['status_enum'],
      },
    });
  }

  isAllowAccessTab(tc: ITabComponent) {
    if (tc.title === DetailPurchaseOrderTab.LIST_NEED_FULLFILL) {
      return this.isAllowProductNeedFullFillTab;
    } else if (tc.title === DetailPurchaseOrderTab.SALES_ORDER) {
      return this.isAllowSalesOrderTab;
    } else {
      return this.permissionService.isAllowAccessTab(this.privilegeTabs, tc.enum_privilege ?? '');
    }
  }
}

// todo: remove unused
