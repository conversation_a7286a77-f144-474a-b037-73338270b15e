import { AfterViewInit, Component, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { STRING_CONSTANTS } from '@config/constants/string.constants';
import { BehaviorSubject } from 'rxjs';
import { TableColumn } from '@shared/interface/table.interface';
import { StatusPurchaseOrder } from '@config/enum/status.enum';
import { DatePipe, registerLocaleData } from '@angular/common';
import localeId from '@angular/common/locales/id';
import { UtilitiesService } from '@services/utilities.service';
import { RolePrivilegeService } from '@services/role-privilege.service';
import { API } from '@config/constants/api.constant';
import { productCreateSoTablePendingColumns } from '../../purchase-order.data';
import {
  CardProductNeedFullFillComponent,
} from '@shared/components/card/card-product-need-full-fill/card-product-need-full-fill.component';

registerLocaleData(localeId);

@Component({
  selector: 'app-product',
  templateUrl: './product.component.html',
  styleUrls: ['./product.component.scss'],
})
export class ProductComponent implements OnInit, AfterViewInit {
  // params
  id: string | null;
  role: string = '';

  statusPurchaseOrder: string | null = null;
  isLoading: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(true);
  enableCreateSO: boolean = false;

  expiredDate: string;
  expiredDays: number;

  STRING_CONSTANTS = STRING_CONSTANTS;
  statusPurchaseOrderEnum = StatusPurchaseOrder;

  tableColumns: TableColumn[] = productCreateSoTablePendingColumns;

  keyDetailPrivilege: string | null = 'DETAIL_PURCHASE_ORDER';
  privilegeCreateSO: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);

  @ViewChild('cardProductNeedFullFill') cardProductNeedFullFill: CardProductNeedFullFillComponent;

  constructor(private activatedRoute: ActivatedRoute, private router: Router, public utilities: UtilitiesService, private rolePrivilegeService: RolePrivilegeService) {
  }

  ngOnInit(): void {
    this.handleQueryParam();
    this.handleCheckPrivilege();
  }

  ngAfterViewInit() {
    this.setDataExpired();
  }

  handleQueryParam() {
    const { id, statusPurchaseOrder } = this.activatedRoute.snapshot.params;
    this.id = id;
    this.statusPurchaseOrder = statusPurchaseOrder;
  }

  setDataExpired() {
    this.cardProductNeedFullFill?.data.subscribe((value) => {
      if (Object.keys(value).length === 0) return;

      const { expiration_date, is_distributor_edited } = value;

      this.enableCreateSO = is_distributor_edited;

      const pipe = new DatePipe('id-ID');
      const dateId = pipe.transform(expiration_date, 'dd MMMM yyyy');
      this.expiredDate = dateId ? dateId : '';

      const oneDay = 24 * 60 * 60 * 1000;
      const firstDate = new Date(expiration_date).valueOf();
      const secondDate = new Date().valueOf();
      this.expiredDays = Math.round(Math.abs((firstDate - secondDate) / oneDay));

      setTimeout(() => this.isLoading.next(false), 750);
    });
  }

  createSo = () => this.router.navigate(['/purchase-order/create/sales-order/' + this.id], { state: { data: this.statusPurchaseOrder } });

  hasPrivilegeCreateSO = () => this.privilegeCreateSO.value && this.hasValidStatusCreateSO();

  hasValidStatusCreateSO = () => this.statusPurchaseOrder === this.statusPurchaseOrderEnum.RESERVED || this.statusPurchaseOrder === this.statusPurchaseOrderEnum.OUTSTANDING;

  handleCheckPrivilege = () =>
    this.privilegeCreateSO.next(this.rolePrivilegeService.checkPrivilege('PURCHASE_ORDER', this.keyDetailPrivilege, 'TAB_LIST_NEED_FULLFILL', 'CTA_CREATE_SO'));

  showExpirationCard = () => this.statusPurchaseOrder === this.statusPurchaseOrderEnum.OUTSTANDING && this.expiredDays <= 3;

  protected readonly API = API;
}

// todo: refactor;remove unused
