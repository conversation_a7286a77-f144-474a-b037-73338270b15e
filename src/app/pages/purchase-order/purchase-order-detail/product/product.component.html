<app-note-view
  *ngIf="enableCreateSO"
  [color]="'info'"
  [classNoteView]="'mt-7'"
  [icon]="STRING_CONSTANTS.ICON.IC_INFORMATION"
  [text]="'SO tidak dapat dibuat karena pengajuan perubahan data distributor belum disetujui.'"
></app-note-view>

<app-card-notif-expired *ngIf="showExpirationCard()" [date]="expiredDate" [days]="expiredDays"></app-card-notif-expired>

<div class="space"></div>

<app-card-product-need-full-fill #cardProductNeedFullFill [tableColumns]="tableColumns" [url]="API.PURCHASE_ORDER.GET_PRODUCT_NEED_FULL_FILL + id" />

<div *ngIf="hasPrivilegeCreateSO()" class="button">
  <button (click)="createSo()" [disabled]="enableCreateSO" class="btn btn-primary" style="width: 184px" type="button">Buat SO</button>
</div>
