import { Injectable } from '@angular/core';
import { BaseService } from '@services/base-service.service';
import { IWarehouseList } from '@shared/interface/warehouse.interface';
import { API } from '@config/constants/api.constant';
import { map } from 'rxjs/operators';
import { ISelectGudang } from '@shared/components/form/input-select2/input-select2.interface';
import { BehaviorSubject, shareReplay, tap } from 'rxjs';
import { IFilterStatusPO, IInitPurchaseOrderDetail, PurchaseOrderDetailOld } from './purchase.order.interface';
import { StatusPurchaseOrder } from '@config/enum/status.enum';

import { PurchaseOrderLegacyService } from '@services/purchase-order.service';
import { IInitCreateSO, IPayloadProductQty } from '@models/product-order.model';
import { CreateSoModel } from './v1/create-so/create-so.model';
import { IProductOrder__ProductList } from '@shared/interface/product-order.interface';

@Injectable({
  providedIn: 'root',
})
export class PurchaseOrderService extends PurchaseOrderLegacyService {
  private initPODetailSubject = new BehaviorSubject({} as IInitPurchaseOrderDetail);

  // Id PO
  private purchaseOrderDetailIDSubject = new BehaviorSubject('');

  // Detail PO
  private purchaseOrderDetailSubject = new BehaviorSubject({} as PurchaseOrderDetailOld);
  private createSoModel = new CreateSoModel();

  constructor(baseService: BaseService) {
    super(baseService);
  }

  get InitPoDetail() {
    return this.initPODetailSubject.value;
  }

  set InitPoDetail(data: IInitPurchaseOrderDetail) {
    this.initPODetailSubject.next(data);
  }

  get PoDetail() {
    return this.purchaseOrderDetailSubject.value;
  }

  set PoDetail(data: PurchaseOrderDetailOld) {
    this.purchaseOrderDetailSubject.next(data);
  }

  get PoDetailID() {
    return this.purchaseOrderDetailIDSubject.value;
  }

  set PoDetailID(id: string) {
    this.purchaseOrderDetailIDSubject.next(id);
  }

  get InitCreateSO() {
    return this.createSoModel.HeaderData;
  }

  get ProductOrderList() {
    return this.createSoModel.ProductOrderList;
  }

  getListGudang() {
    return this.baseService.getData<IWarehouseList>(`${API.LIST_USER_WAREHOUSE}`).pipe(map((resp) => resp));
  }

  mapInputSelectGudang(data: IWarehouseList) {
    return data.map((resp) => {
      let inputSelector: ISelectGudang = new ISelectGudang();
      inputSelector.setGudangOptions(resp);
      return inputSelector;
    });
  }

  getInitialDetailSO(id: string) {
    return this.baseService.getData<IInitPurchaseOrderDetail>(API.GET_INIT_DETAIL_PURCHASE_ORDER + id).pipe(
      map((resp) => {
        if (resp && resp.success) this.InitPoDetail = resp.data;
        return resp;
      })
    );
  }

  getDetailPurchaseOrder() {
    let detailApi = this.getAPIByPOStatus();
    return this.baseService.getData<PurchaseOrderDetailOld>(detailApi + this.PoDetailID).pipe(
      map((resp) => {
        if (resp && resp.success) this.PoDetail = resp.data;
        return resp;
      })
    );
  }

  getFilterStatusPO() {
    return this.baseService.getData<IFilterStatusPO[]>(API.LIST_FILTER_STATUS);
  }

  // create SO
  getInitCreateSO(poID: string) {
    return this.baseService.getData<IInitCreateSO>(API.PURCHASE_ORDER.INIT_CREATE_SO + poID).pipe(
      tap((resp) => {
        if (resp?.success) this.createSoModel.HeaderData = resp.data;
      }),
      map((res) => res?.data)
    );
  }

  getListProductOrderSO(poID: string) {
    return this.baseService.getData<IProductOrder__ProductList[]>(API.PURCHASE_ORDER.GET_LIST_PRODUCT_ORDER + poID).pipe(
      tap((resp) => {
        if (resp?.success) this.createSoModel.ProductOrderList = resp.data;
      }),
      map((res) => res?.data)
    );
  }

  getPatchProductPromagSO<T>(poID: string, payload: IPayloadProductQty) {
    return this.baseService.patchDataWithBody<T>(API.PURCHASE_ORDER.GET_LIST_PRODUCT_PROGRAM_MARKETING + poID, payload).pipe(
      shareReplay(),
      map((resp) => resp && resp.data)
    );
  }

  getPatchPriceSummarySO<T>(poID: string, payload: IPayloadProductQty) {
    return this.baseService.patchDataWithBody<T>(API.PURCHASE_ORDER.GET_INIT_PRICE_SUMMARY + poID, payload).pipe(
      shareReplay(),
      map((resp) => resp && resp.data)
    );
  }

  patchConfirmCreateSO<T>(poID: string, payload: IPayloadProductQty) {
    return this.baseService.patchDataWithBody<T>(API.PURCHASE_ORDER.CONFIRM_CREATE_SO + poID, payload);
  }

  postCreateSO<T>(poID: string, payload: T) {
    return this.baseService.postData(API.PURCHASE_ORDER.POST_CREATE_SO + poID, payload);
  }

  private getAPIByPOStatus = () =>
    this.initPODetailSubject.value.po_status === StatusPurchaseOrder.NEED_APPROVE_REGIONAL_FINANCE
      ? API.DETAIL_PURCHASE_ORDER_PENDING
      : API.DETAIL_PURCHASE_ORDER_NEED_FULFILL_PRODUCT;
}
