export type privilegesData = {
  parentMenu: string | undefined;
  pageView: string | undefined | null;
  detailPage: string | null;
  subDetailPage: string | null;
  sequence: number;
};

export interface IRoleRoot {
  enum_value: string;
  enum_string: string;
}

export interface IRolePrivilegesData {
  role: string;
  parent_menu?: any;
  application_type: string | null;
  privileges: Array<privilegesData>;
}

export interface IRolePrivilege {
  role: string;
  privilege: IPrivileges[];
}

export interface IPrivilegeChecklist {
  name: string;
  enumName: string;
  completed: boolean;
  list?: IPrivilegeChild2[];
  child?: any[];
  parentMenu?: string;
  pageView?: string;
  sequence?: number;
  checked?: boolean;
  ticked?: boolean;
}

export interface IPrivilegeList {
  privilege: IPrivileges[];
}

export interface IPrivileges {
  id: string;
  sequence: number;
  displayName: string;
  name: string;
  parentMenu: string;
  child: IPrivilegeChild[];
  icon?: string;
  active?: boolean;
  ticked?: boolean;
}

export interface IPrivilegeChild {
  pageView: string;
  name: string;
  displayName: string;
  child: IPrivilegeChild2[];
  sequence: number;
  id: string;
  checked?: boolean;
}

export interface IPrivilegeChild2 {
  detailPage: string;
  displayName: string;
  name: string;
  sequence: number;
  child?: IPrivilegeChild3[];
  checked?: boolean;
  completed?: boolean;
  list?: any;
  id: string;
}

export interface IPrivilegeChild3 {
  subDetailPage: string;
  name: string;
  displayName: string;
  sequence: number;
  checked?: boolean;
  completed?: boolean;
  id: string;
}

export enum PLATFORM_TYPE {
  BO = 'BACK_OFFICE',
  MMPLUS = 'MM_PLUS',
  BACK_OFFICE = 'Back Office',
  MM_PLUS = 'Maxxi Marketing',
}

// export enum BACKOFFICE_ROLE {
//   SUPER_ADMIN = 'Super Admin',
//   ADMIN = 'Admin',
//   ADMIN_GUDANG = 'Admin Gudang',
//   FINANCE = 'Finance',
//   SUPER_FINANCE = 'Super Finance',
//   REGIONAL_DIRECTOR = 'Regional Director',
//   AREA_MANAGER = 'Regional Head',
//   SALES_STAFF = 'Marketing Executive',
// }

export enum ALL_ROLES {
  SUPER_ADMIN = 'Super Admin',
  ADMIN = 'Admin',
  ADMIN_GUDANG = 'Admin Gudang',
  ADMIN_MARKETING = 'Admin Marketing',
  FINANCE = 'Finance',
  SUPER_FINANCE = 'Super Finance',

  REGIONAL_DIRECTOR = 'Regional Director',
  AREA_MANAGER = 'Regional Head',
  SALES_STAFF = 'Marketing Executive',
  SALES_AGRO = 'Sales Agronomist',
}
