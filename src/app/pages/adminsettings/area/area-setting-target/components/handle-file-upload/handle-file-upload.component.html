<ng-container>
  <div class="d-flex flex-wrap align-items-start mb-6">
    <label *ngIf="showLabel" class="col-12 col-lg-4 col-form-label" [class.required]="required">{{ label }}</label>
    <div [class.ms-auto]="idx > 0" class="col-12" [ngClass]="{ 'col-lg-8': showLabel || idx > 0 }">
      <ng-container *ngIf="fileName && !isLoading.value; else uploaderTpl">
        <div class="dropzone dropzone-Theme1 dropzone-panel" style="border-width: 2px">
          <ng-container [ngTemplateOutlet]="previewImage" [ngTemplateOutletContext]="{ url: fileName }"></ng-container>

          <ng-container *ngIf="enableDelete && fileName">
            <input type="file" id="input-file__{{ idx }}" class="d-none" (change)="onFileChange($event)" accept="text/csv" />

            <label for="input-file__{{ idx }}" class="cursor-pointer mt-4">
              <span class="text-decoration-underline caption fw-bold mt-n6">{{ ctaLabelFile }}</span>
            </label>
          </ng-container>
        </div>
      </ng-container>

      <ng-template #uploaderTpl>
        <div appDnd class="dropzone dropzone-Theme1 dropzone-panel" [class.error]="hasError" (filesChangeEmitter)="onFileChange($event)">
          <input type="file" id="input-file__{{ idx }}" class="d-none" (change)="onFileChange($event)" accept="text/csv" [disabled]="isDisabled" [class.disabled]="isDisabled" />

          <ng-container *ngIf="isLoading.value">
            <ng-container *ngTemplateOutlet="loaderSpinner"></ng-container>
          </ng-container>

          <label
            for="input-file__{{ idx }}"
            class="d-flex align-items-center justify-content-center flex-column mt-6"
            [class.cursor-pointer]="enableDelete"
            *ngIf="!isLoading.value"
          >
            <ng-container>
              <span class="svg-icon svg-icon-uploader" [inlineSVG]="STRING_CONSTANTS.ICON.IC_UPLOAD_FILE"></span>
              <span class="my-6 fw-bold">
                Drag file here or click to upload,<br />
                or <span class="text-info-text">browse</span>.
              </span>
            </ng-container>
          </label>
        </div>
      </ng-template>
    </div>
  </div>
</ng-container>

<ng-template #loaderSpinner>
  <div class="d-flex cursor-pointer align-items-center justify-content-center">
    <mat-spinner diameter="32"></mat-spinner>
  </div>
</ng-template>

<ng-template #previewImage let-url="url">
  <div class="position-relative mw-100 m-auto" [ngStyle]="{ width: '50%' }">
    <span *ngIf="enableDelete" class="img-close-icon" [inlineSVG]="STRING_CONSTANTS.ICON.IC_BULLET_CLOSE" (click)="onRemoveFile()"></span>
    <p class="img-thumbnail img-fluid p-2">{{ url }}</p>
  </div>
</ng-template>
