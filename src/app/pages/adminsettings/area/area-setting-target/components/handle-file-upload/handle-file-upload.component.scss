.dropzone {
  background-color: transparent !important;

  &:not(.dropzone-Theme1) {
    border: 1px dashed #D1D1D1 !important;
    height: 176px;
    width: 176px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &.error {
    background: #CC443510!important;
    border-color: var(--kt-danger) !important;
  }

  .svg-icon-uploader svg {
    fill: var(--kt-gray-700);
  }
}

.dropzone-Theme1 {
  border-color: var(--kt-gray-300) !important;
  color: #414141;

  ::ng-deep .svg-icon svg {
    width: 51px !important;
    height: auto !important;
    fill: var(--kt-gray-700);
  }

  .caption {
    color: #5EC8F2;
  }

  &:has(input[type=file].disabled) {
    background: #ebebeb!important;
    pointer-events: none;

    &,
    & .text-info-text {
      color: var(--kt-gray-300) !important;
    }

    ::ng-deep .svg-icon svg {
      fill: var(--kt-gray-300);
    }
  }

}

.img-close-icon {
  position: absolute;
  top: -5px;
  right: -5px;
  width: auto !important;
}
