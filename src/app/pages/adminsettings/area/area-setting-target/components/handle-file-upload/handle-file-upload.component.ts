import { Component, ElementRef, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { STRING_CONSTANTS } from '@config/constants/string.constants';
import { BehaviorSubject, Observable } from 'rxjs';
import { UtilitiesService } from '@services/utilities.service';
import { IHeaderExportTarget } from '../../../area.interface';

@Component({
  selector: 'app-handle-file-upload',
  templateUrl: './handle-file-upload.component.html',
  styleUrls: ['./handle-file-upload.component.scss'],
})
export class HandleFileUploadComponent implements OnInit {
  @ViewChild('imageUpload') imageUpload: ElementRef;

  @Output() fileOutput = new EventEmitter<{ index: number; json: any; name: string; }>();
  @Output() loadingState = new EventEmitter();
  @Input() type: string | null;
  @Input() label: string = '';
  @Input() required: boolean = false;
  @Input() idx: number = 0;
  @Input() showLabel: boolean = true;

  @Input() enableDelete: boolean | undefined = true;
  @Input() isDisabled: undefined | boolean = false;

  hasError = false;
  ctaLabelFile = 'Pilih File';
  fileName: string = '';
  fileData: File;
  jsonData: any[] = [];


  ALLOWED_EXTENSIONS = ['text/csv'];

  constructor(private utils: UtilitiesService) {
    this.fileUrl$ = this.fileUrl.asObservable();
    this.isLoading$ = this.isLoading.asObservable();
  }

  ngOnInit(): void {}

  public files: any;
  public fileUrl: BehaviorSubject<string | undefined> = new BehaviorSubject<string | undefined>(undefined);

  isLoading: BehaviorSubject<boolean> = new BehaviorSubject(false);
  STRING_CONSTANTS = STRING_CONSTANTS;
  fileUrl$: Observable<any>;
  isLoading$: Observable<boolean>;

  extractYear(filename:string) {
    const match = filename.match(/\b(20\d{2})\b/);
    return match ? match[1] : null;
  }

  onFileChange(e: any) {
    this.setLoading(true);
    const file = e.target.files[0];
    const name: any = this.extractYear(file.name);
    if (file) {
      this.handleFileInput(this.idx, file, name);
      this.fileName = file.name;
      this.fileData = file;
      this.setLoading(false);
    }
  }

  public handleFileInput(index: number, event: any, name: string) {
    const file = event;

    if (!file) { return }

    this.utils.parseCsvToJson<IHeaderExportTarget>(file)
      .then(data => {
        if (!Array.isArray(this.jsonData)) {
          this.jsonData = [];
        }
        this.jsonData[index] = data;
        this.fileOutput.emit({
          index: this.idx,
          json: data,
          name: name
        });
      })
      .catch(err => {
      });
  }

  onRemoveFile() {
    this.fileName = '';
    this.fileOutput.emit({ index: this.idx, json: [], name: '2025' });
    this.hasError = false;
    this.ctaLabelFile = 'Pilih File';
  }

  setLoading(state: boolean) {
    this.isLoading.next(state);
    this.loadingState.emit(state);
  }
}
