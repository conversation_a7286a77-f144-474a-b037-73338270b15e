<form (ngSubmit)="handleSubmit()" [formGroup]="fbSettingTargetForm">
  <ng-container>
    <div class="mb-10 pt-10 border-gray-300">
      <app-input-select-material
        #inputPeriodeList
        [label]="'Tahun'"
        [required]="true"
        [optionsData]="listPeriodeSubject"
        [useFilterList]="false"
        (handleChangeData)="handlePeriodeChange($event)"
        placeholder="Pilih Tahun"
        formControlName="id_periode"
        ngDefaultControl
      ></app-input-select-material>
      <ng-container *ngFor="let file of inputFile; let i = index">
        <app-handle-file-upload
          (fileOutput)="handleFileInput(i, $event)"
          [label]="file.title"
          [required]="false"
          [showLabel]="true"
          formControlName="{{ file.name }}"
          ngDefaultControl
          type="file"
          [idx]="i"
        ></app-handle-file-upload>
      </ng-container>
    </div>
  </ng-container>

  <div class="d-flex align-items center justify-content-between mt-6">
    <button (click)="handleCancel.emit(true)" class="btn btn-outline btn-outline-secondary min-w-150px" type="button">Batal</button>
    <button [disabled]="validateForm()" class="btn btn-primary min-w-150px" type="submit">Simpan</button>
  </div>
</form>

<ng-container>
  <app-modal #modalResponseCreate [modalConfig]="modalResponseCreateConfig">
    <div class="d-flex flex-column justify-content-center align-items-center">
      <ng-container *ngIf="isLoadingPostData | async; else responseDone">
        <div class="mt-8">
          <mat-spinner></mat-spinner>
        </div>
      </ng-container>

      <ng-template #responseDone>
        <span [inlineSVG]="STRING_CONSTANTS.ICON.SUCCESS_ALERT"></span>
        <div class="mb-n8">
          <span>Data Berhasil Ditambahkan</span>
        </div>
      </ng-template>
    </div>
  </app-modal>
</ng-container>
