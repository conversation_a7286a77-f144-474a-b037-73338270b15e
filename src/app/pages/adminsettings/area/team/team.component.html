<div class="my-10">
  <h1 class="d-flex align-items-center mb-0">
    <span
      (click)="onClickBack()"
      class="inline-svg svg-icon-48 d-block cursor-pointer"
      [inlineSVG]="STRING_CONSTANTS.ICON.IC_ARROW_LINE_RIGHT"
      style="transform: rotate(180deg)"
    ></span>
    <span class="fw-bold ms-4">Anggota Tim</span>
  </h1>
</div>

<app-card [cardClasses]="'mt-10'" [header]="true">
  <ng-container cardHeader>
    <app-filter-list-team [showAddTeamCTA]="PrivilegeAddTeam" [searchInputValue]="string_filter" [filterInputActivated]="isActiveFilter" (addTeamAction)="onAddTeam()" />
  </ng-container>

  <ng-container cardBody>
    <ng-container [ngTemplateOutlet]="baseDatasource.isTableLoaded && baseDatasource.totalItem$.getValue() === 0 ? noDataTpl : tableDataCardTpl"></ng-container>

    <ng-template #tableDataCardTpl>
      <div class="table-responsive">
        <table mat-table [dataSource]="baseDatasource.tableSubject" class="table w-100 gy-5 table-row-bordered align-middle" matSort>
          <ng-container *ngFor="let tableColumn of tableColumns" [matColumnDef]="tableColumn.key">
            <ng-container *ngIf="tableColumn.isSortable; else notSortable">
              <th
                *matHeaderCellDef
                [ngClass]="{
                  'min-w-125px': tableColumn.key !== 'actions',
                  'min-w-70px text-center': tableColumn.key === 'actions',
                  'mw-150px': tableColumn.key === 'code'
                }"
                [arrowPosition]="'after'"
                [mat-sort-header]="tableColumn.key"
                mat-header-cell
              >
                <app-table-content [width]="70" [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject">{{ tableColumn.title }}</app-table-content>
              </th>
            </ng-container>
            <ng-template #notSortable>
              <th
                *matHeaderCellDef
                [ngClass]="{
                  'min-w-125px': tableColumn.key !== 'code' && tableColumn.key !== 'actions',
                  'min-w-70px text-center': tableColumn.key === 'actions'
                }"
                class="px-3"
                mat-header-cell
              >
                <app-table-content [width]="70" [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject">{{ tableColumn.title }}</app-table-content>
              </th>
            </ng-template>

            <td *matCellDef="let element" class="px-3" [class.mw-250px]="tableColumn.key === 'warehouse_list' || tableColumn.key === 'sub_area_list'">
              <ng-container [ngSwitch]="tableColumn.key">
                <div *ngSwitchCase="'sub_area_list'">
                  <app-table-content [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject">
                    <span class="block-ellipsis-customs">{{ renderArrayString(element[tableColumn.key], 'name') }}</span>
                  </app-table-content>
                </div>

                <div *ngSwitchCase="'warehouse_list'">
                  <app-table-content [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject">
                    <span class="block-ellipsis-customs">{{ renderArrayString(element[tableColumn.key], 'name') }}</span>
                  </app-table-content>
                </div>

                <div *ngSwitchCase="'actions'" class="text-center">
                  <app-table-content [type]="'icon'" [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject">
                    <ng-container [ngTemplateOutlet]="actionsTpl" [ngTemplateOutletContext]="{ data: element }"></ng-container>
                  </app-table-content>
                </div>

                <div *ngSwitchDefault>
                  <app-table-content [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject">
                    <span>{{ element[tableColumn.key] }}</span>
                  </app-table-content>
                </div>
              </ng-container>
            </td>
          </ng-container>

          <tr *matHeaderRowDef="displayedColumns" class="fw-bold text-start text-uppercase" mat-header-row></tr>
          <tr *matRowDef="let row; columns: displayedColumns" mat-row></tr>
        </table>
      </div>
      <div class="d-flex justify-content-between py-4">
        <app-mai-material-bottom-table
          (changePage)="onChangePageEvent($event)"
          [baseDataTableComponent]="baseDatasource"
          [isFinishLoadingSubject]="baseDatasource.isFinishLoadingSubject"
          class="w-100"
        ></app-mai-material-bottom-table>
      </div>
    </ng-template>
  </ng-container>
</app-card>

<ng-template #noDataTpl>
  <app-card-empty [icon]="STRING_CONSTANTS.ICON.IC_PROFILE_NONE" text="Belum terdapat Anggota Tim" />
</ng-template>

<ng-template #actionsTpl let-data="data">
  <button mat-button [matMenuTriggerFor]="menu">
    <span class="inline-svg" [inlineSVG]="STRING_CONSTANTS.ICON.IC_DOTS_MENU"></span>
  </button>

  <mat-menu #menu="matMenu">
    <div class="menu-item px-5">
      <div class="menu-link" (click)="onClickDetailUser(data.id)">
        <span class="svg-icon svg-icon-2 me-3" [inlineSVG]="STRING_CONSTANTS.ICON.IC_ACTIVITY"></span>
        <span>Lihat Detail User</span>
      </div>
    </div>
    <div class="menu-item px-5" *ngIf="PrivilegeUnassignTeam">
      <div class="menu-link" (click)="onClickUnAssignUser(data)">
        <span class="svg-icon svg-icon-2 me-3" [inlineSVG]="STRING_CONSTANTS.ICON.IC_REMOVE_ACCOUNT"></span>
        <span>Unassign dari Area</span>
      </div>
    </div>
  </mat-menu>
</ng-template>

<!-- modal: unassign -->
<app-modal-unassign-team #modalUnassignTeam />

<!-- modal: add team -->
<app-modal-add-area-team #modalAddTeam />
