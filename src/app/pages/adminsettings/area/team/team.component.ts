import { AfterViewInit, Component, OnInit, ViewChild } from '@angular/core';
import { PageInfoService, PageLink } from '@metronic/layout';
import { ActivatedRoute, Router } from '@angular/router';
import { TableColumn } from '@shared/interface/table.interface';
import { STRING_CONSTANTS } from '@config/constants/string.constants';
import { AreaTeamService } from './area-team.service';
import { MatSort } from '@angular/material/sort';
import { BaseDatasource } from '@shared/base/base.datasource';
import { IAreaTeam } from './area-team.interface';
import { UrlUtilsService } from '@utils/url-utils.service';
import { UtilitiesService } from '@services/utilities.service';
import { ModalAddAreaTeamComponent } from './components/modal-add-area-team/modal-add-area-team.component';
import { ModalUnassignTeamComponent } from './components/modal-unassign-team/modal-unassign-team.component';
import { FilterService } from '@services/filter.service';

@Component({
  selector: 'app-team',
  templateUrl: './team.component.html',
  styleUrls: ['./team.component.scss'],
})
export class TeamComponent implements OnInit, AfterViewInit {
  @ViewChild(MatSort, { static: false }) matSort: MatSort;
  baseDatasource: BaseDatasource<IAreaTeam>;

  areaId!: string;
  routeState: any;
  links!: PageLink[];

  tableColumns!: TableColumn[];
  displayedColumns: string[];

  string_filter!: string;
  isActiveFilter = false;

  @ViewChild('modalAddTeam') modalAddTeam: ModalAddAreaTeamComponent;
  @ViewChild('modalUnassignTeam') modalUnassignTeam: ModalUnassignTeamComponent;

  get PrivilegeAddTeam() {
    return this.areaTeamService.PrivilegeAddTeamMember;
  }

  get PrivilegeUnassignTeam() {
    return this.areaTeamService.PrivilegeUnassignTeam;
  }

  constructor(
    private router: Router,
    private activeRoute: ActivatedRoute,
    private pageInfoService: PageInfoService,
    private urlParamService: UrlUtilsService,
    private areaTeamService: AreaTeamService,
    private utilsService: UtilitiesService,
    private filterService: FilterService
  ) {
    this.routeState = history.state;
    this.areaId = this.activeRoute.snapshot.params.id;
  }

  ngAfterViewInit(): void {}

  ngOnInit() {
    this.initPageInfo();
    this.setTableData();
    this.queryHandler();
  }

  initPageInfo() {
    const { title, bc } = this.routeState;
    if (!title || !bc) return this.onClickBack();
    const id = this.activeRoute.snapshot.params.id;

    bc.map((item: PageLink, index: number) => {
      item.isActive = false;
      if (index === bc.length - 1) item.path = `/settings/area/${id}`;
    });
    bc.push(this.pageInfoService.BreadcrumbSeparator, {
      title: 'Anggota Tim',
      path: '',
      isActive: true,
    });

    this.pageInfoService.updateTitle(title);
    this.pageInfoService.updateBreadcrumbs(bc);
    return;
  }

  setTableData() {
    this.areaTeamService.ResponseDatabase$.subscribe((resp) => (this.baseDatasource = resp));
    this.tableColumns = this.areaTeamService.TableColumns;
    this.displayedColumns = this.areaTeamService.DisplayedTableColumns;
  }

  queryHandler() {
    this.activeRoute.queryParams.subscribe((params) => {
      const { string_filter, role } = params;

      this.string_filter = string_filter;
      this.isActiveFilter = !!role;

      const _param = this.urlParamService.sliceQueryParams();
      this.areaTeamService.loadTableTeamList(this.areaId, _param);
    });
  }

  renderArrayString(arr: [], key = '') {
    if (!arr || (arr && !arr.length)) return '-';
    return this.utilsService.arrayStringJoin(arr, key, ',', true);
  }

  onClickBack() {
    const _url = `/settings/area/${this.activeRoute.snapshot.params.id}`;
    return this.router.navigate([_url]);
  }

  onClickDetailUser(id: string) {
    const _url = `settings/user/${id}`;
    return this.router.navigate([_url]);
  }

  onClickUnAssignUser(data: IAreaTeam) {
    return this.modalUnassignTeam.openDialog(this.routeState.data.area_name, data);
  }

  onAddTeam() {
    return this.modalAddTeam.openDialog();
  }

  onChangePageEvent(e: BaseDatasource<IAreaTeam>) {
    this.filterService.changePageEvent(e, this.string_filter, this.routeState);
  }

  protected readonly STRING_CONSTANTS = STRING_CONSTANTS;
}
