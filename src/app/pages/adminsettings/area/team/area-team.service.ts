import { Injectable } from '@angular/core';
import { TableColumn } from '@shared/interface/table.interface';
import { BaseTableService } from '@shared/base/base-table-service.service';
import { IAreaTeam, IPayloadAddTeam, IPayloadUnassignTeam, IResponseAddTeam, IResponseUnassignTeam } from './area-team.interface';
import { API } from '@config/constants/api.constant';
import { BaseService } from '@services/base-service.service';
import { IInputSelectOptions } from '@shared/components/v1/input/input-select-autocomplete/input-select-autocomplete.interface';
import { map } from 'rxjs/operators';
import { shareReplay } from 'rxjs';
import { EnumRole } from '@models/role.model';
import { RolePrivilegeService } from '@services/role-privilege.service';

@Injectable({
  providedIn: 'root',
})
export class AreaTeamService {
  constructor(
    private baseService: BaseService,
    private baseTableService: BaseTableService<IAreaTeam>,
    private rolePrivilegeService: RolePrivilegeService
  ) {}

  get PrivilegeAddTeamMember() {
    return this.rolePrivilegeService.checkPrivilege('SETTINGS', 'AREA_MANAGEMENT', 'LIST_MEMBER_TEAM_AREA', 'CTA_ADD_MEMBER_TEAM');
  }

  get PrivilegeUnassignTeam() {
    return this.rolePrivilegeService.checkPrivilege('SETTINGS', 'AREA_MANAGEMENT', 'LIST_MEMBER_TEAM_AREA', 'CTA_UNASSIGN_USER');
  }

  get TableColumns() {
    return <TableColumn[]>[
      {
        key: 'code',
        title: 'Kode User',
        isSortable: false,
      },
      {
        key: 'name',
        title: 'Nama',
        isSortable: false,
      },
      {
        key: 'area',
        title: 'Area',
        isSortable: false,
      },
      {
        key: 'sub_area_list',
        title: 'Sub area',
        isSortable: false,
      },
      {
        key: 'warehouse_list',
        title: 'Gudang',
        isSortable: false,
      },
      {
        key: 'role_string',
        title: 'Role',
        isSortable: true,
      },
      {
        key: 'actions',
        title: 'Action',
        isSortable: false,
      },
    ];
  }

  get DisplayedTableColumns() {
    return this.baseTableService.mapperDisplayColumn(this.TableColumns);
  }

  get ResponseDatabase$() {
    return this.baseTableService.responseDatabase$;
  }

  loadTableTeamList(id: string, param?: string) {
    return this.baseTableService.loadDataTable(API.AREA.GET_TEAM_LIST + id, param ?? '');
  }

  getRoleListByArea(id: string) {
    return this.baseService.getData<IInputSelectOptions[]>(API.AREA.GET_ROLE_BY_AREA + id).pipe(map((resp) => resp));
  }

  getUserListByRoleAndArea(params: { areaId: string; role?: EnumRole; page?: number; size?: number }) {
    return this.baseService.getData<IInputSelectOptions[]>(API.AREA.GET_UNASSINED_USER_BY, undefined, params).pipe(map((resp) => resp));
  }

  getScopeList(areaID: string, param: { employee_id: string }) {
    const { employee_id } = param;
    const params = `?employee_id=${employee_id}`;
    return this.baseService.getData<IInputSelectOptions[]>(`${API.AREA.GET_LIST_SCOPE}/${areaID}` + params).pipe(map((resp) => resp));
  }

  submitAddTeam(areaID: string, payload: IPayloadAddTeam) {
    return this.baseService.putData<IResponseAddTeam>(API.AREA.ADD_TEAM + areaID, payload).pipe(
      shareReplay(),
      map((resp) => resp && resp.data)
    );
  }

  getRegionalDirectorList(params: { page?: number; size?: number }) {
    return this.baseService.getData<any>(API.AREA.GET_RD_LIST, undefined, params);
  }

  getRegionalHeadList(params: { page?: number; size?: number }) {
    return this.baseService.getData<any>(API.AREA.GET_RH_LIST, undefined, params);
  }

  getMarketingExecutiveList(areaID?: string, params?: { page: number; size: number }) {
    return this.baseService.getData<IInputSelectOptions[]>(`${API.SUBAREA.GET_ME_LIST}/${areaID}`, undefined, params);
  }

  getEmployeeReplacementList(employeeID: string, params?: { page: number; size: number }) {
    return this.baseService.getData<IInputSelectOptions[]>(`${API.USER_REPLACEMENT_LIST}/${employeeID}`, undefined, params);
  }

  patchUserUnassign(employeeID: string, payload?: IPayloadUnassignTeam) {
    const _payload = payload ?? {
      replacement_list: null,
      unassign_area_ids: null,
    };

    return this.baseService.patchDataWithBody<IResponseUnassignTeam>(`${API.USER_UNASSIGN}/${employeeID}`, _payload);
  }
}
