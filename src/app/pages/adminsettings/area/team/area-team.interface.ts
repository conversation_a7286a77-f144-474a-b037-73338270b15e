import { IGenericIdName, IGenericResponseSuccess } from '@shared/interface/generic';
import { EnumRole } from '@models/role.model';

export interface IAreaTeam {
  id: string;
  code: string;
  name: string;
  area: string;
  role_enum: EnumRole;
  role_string: string;
  sub_area_list: IGenericIdName[];
  warehouse_list: IGenericIdName[];
}

export interface IPayloadAddTeam {
  role_enum: EnumRole;
  employee_id: string;
  sub_area_ids: string[];
  warehouse_ids: string[];
}

export interface IResponseAddTeam extends IGenericResponseSuccess {
  data: string; // some enpdoint use data for response message
}

export interface IPayloadUnassignTeam {
  replacement_list?:
    | {
        employee_id: string;
        area_ids?: string[] | null;
        sub_area_ids?: string[] | null;
        warehouse_ids?: string[] | null;
      }[]
    | null;
  unassign_area_ids?: string[] | null;
  unassign_warehouse_ids?: string[] | null;
}

export interface IResponseUnassignTeam {
  scope_list: string[];
  replacement: { scope: string; user: string }[];
}
