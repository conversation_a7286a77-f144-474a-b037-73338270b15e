<app-modal #modalAddTeam [modalConfig]="modalConfigAddTeam">
  <app-input-select-autocomplete
    #inputSelectRole
    label="Pilih Role"
    placeholder="Pilih role"
    [icon]="STRING_CONSTANTS.ICON.IC_ADD_ACCOUNT"
    [usePagination]="false"
    [optionLabelFn]="displayLabelFn"
    (selectedValue)="onSelectedValue($event, 'ROLE')"
  />

  <ng-container [ngTemplateOutlet]="separatorTpl"></ng-container>
  <app-input-select-autocomplete
    #inputSelectTeam
    label="Pilih Anggota Tim"
    placeholder="Pilih anggota tim"
    [icon]="STRING_CONSTANTS.ICON.IC_ROLE_ACCESS"
    [optionLabelFn]="displayLabelFn"
    [readOnly]="!hasRoleSelected()"
    (selectedValue)="onSelectedValue($event, 'USER')"
  />

  <ng-container *ngIf="isShowSubAreaInput()">
    <ng-container [ngTemplateOutlet]="separatorTpl"></ng-container>
    <app-input-select-autocomplete
      #inputSelectSubArea
      label="Tambah Sub Area"
      placeholder="Silahkan tambahkan cakupan sub area"
      [icon]="STRING_CONSTANTS.ICON.IC_LOCATION"
      [optionLabelFn]="displayLabelFn"
      [readOnly]="!hasUserSelected()"
      [useChips]="isEnableMultipleSubArea()"
      (selectedValue)="onSelectedValue($event, 'SUBAREA')"
    />
  </ng-container>

  <ng-container *ngIf="isShowWarehouseInput()">
    <ng-container [ngTemplateOutlet]="separatorTpl"></ng-container>
    <app-input-select-autocomplete
      #inputSelectWarehouse
      label="Tambah Gudang"
      placeholder="Silahkan tambahkan cakupan gudang"
      [icon]="STRING_CONSTANTS.ICON.IC_WAREHOUSE"
      [optionLabelFn]="displayLabelFn"
      [readOnly]="!hasUserSelected()"
      [useChips]="true"
      (selectedValue)="onSelectedValue($event, 'WAREHOUSE')"
    />
  </ng-container>

  <ng-template #separatorTpl>
    <hr class="my-8 border-gray-700" />
  </ng-template>
</app-modal>

<!-- modal:response -->
<app-modal #modalResponse [modalConfig]="modalConfigResponse">
  <div class="d-flex flex-column justify-content-center align-items-center mb-n6">
    <div class="mt-8 animate animate-fade-out" *ngIf="isLoading | async; else responseTpl">
      <mat-spinner />
    </div>

    <ng-template #responseTpl>
      <div class="text-center animate animate-fade-in">
        <span [inlineSVG]="STRING_CONSTANTS.ICON.SUCCESS_ALERT"></span>
        <p class="my-4">{{ responsePost | async }}</p>
      </div>
    </ng-template>
  </div>
</app-modal>
