import { Component, OnInit, ViewChild } from '@angular/core';
import { ModalComponent } from '@shared/components/modal/modal.component';
import { ModalConfig } from '@shared/components/modal/modal.interface';
import { STRING_CONSTANTS } from '@config/constants/string.constants';
import { InputSelectAutocompleteComponent } from '@shared/components/v1/input/input-select-autocomplete/input-select-autocomplete.component';
import { IInputSelectOptions } from '@shared/components/v1/input/input-select-autocomplete/input-select-autocomplete.interface';
import { ActivatedRoute, Router } from '@angular/router';
import { AreaTeamService } from '../../area-team.service';
import { EnumRole } from '@models/role.model';
import { BehaviorSubject } from 'rxjs';
import { IPayloadAddTeam } from '../../area-team.interface';
import { map } from 'rxjs/operators';

@Component({
  selector: 'app-modal-add-area-team',
  templateUrl: './modal-add-area-team.component.html',
  styleUrls: ['./modal-add-area-team.component.scss'],
})
export class ModalAddAreaTeamComponent implements OnInit {
  @ViewChild('modalAddTeam') modalAddTeam: ModalComponent;
  modalConfigAddTeam = <ModalConfig>{
    modalTitle: 'Tambah Anggota Tim',
    closeButtonLabel: 'Batal',
    dismissButtonLabel: 'Simpan',
    onClose: () => this.resetSelectedValue(),
    onDismiss: () => this.handleSubmit(),
    disableDismissButton: () => !this.isValidToSubmit(),
  };

  @ViewChild('modalResponse') modalResponse: ModalComponent;
  modalConfigResponse = <ModalConfig>{
    closeButtonLabel: 'Lihat Detail',
    dismissButtonLabel: 'Oke',
    disableCloseButton: () => this.isLoading.value,
    disableDismissButton: () => this.isLoading.value,
    onClose: () => this.goToDetailUser(),
    onDismiss: () => location.reload(),
  };

  isLoading = new BehaviorSubject(false);
  responsePost = new BehaviorSubject('user berhasil ditambahkan');

  areaID!: string;
  selectedRole!: EnumRole;

  // todo: optimize selected input values
  selectedUser!: IInputSelectOptions;
  selectedSubArea: IInputSelectOptions[] = [];
  selectedWarehouse: IInputSelectOptions[] = [];

  @ViewChild('inputSelectRole') inputSelectRole: InputSelectAutocompleteComponent;
  @ViewChild('inputSelectTeam') inputSelectTeam: InputSelectAutocompleteComponent;
  @ViewChild('inputSelectSubArea') inputSelectSubArea: InputSelectAutocompleteComponent;
  @ViewChild('inputSelectWarehouse') inputSelectWarehouse: InputSelectAutocompleteComponent;

  constructor(private router: Router, private activatedRoute: ActivatedRoute, private areaTeamService: AreaTeamService) {
    this.areaID = this.activatedRoute.snapshot.params.id;
  }

  ngOnInit() {}

  onSelectedValue(e: IInputSelectOptions, type: 'USER' | 'ROLE' | 'SUBAREA' | 'WAREHOUSE') {
    // switch handler type
    if (type === 'ROLE') {
      this.selectedRole = e.value as EnumRole;

      if (this.hasUserSelected()) {
        // reset user selectd on changing role
        this.selectedUser = {} as IInputSelectOptions;
        this.inputSelectTeam.clearSearchInput();
        this.handleSwitchLoadData();
      }

      this.inputSelectTeam.clearOptions();
      return this.loadUserListData();
    }

    if (type === 'USER') {
      this.selectedUser = e;
      return this.handleSwitchLoadData();
    }

    if (type === 'SUBAREA') {
      if (this.isEnableMultipleSubArea()) {
        this.selectedSubArea = [...this.selectedSubArea, e];
        return this.inputSelectSubArea.clearSearchInput();
      }

      this.selectedSubArea = [e];
    }

    if (type === 'WAREHOUSE') {
      this.selectedWarehouse = [...this.selectedWarehouse, e];
      this.inputSelectWarehouse.clearSearchInput();
    }
  }

  openDialog = () => {
    this.loadRoleListData();
    return this.modalAddTeam.open();
  };

  handleSwitchLoadData() {
    if (!this.hasRoleSelected()) return;

    const _roleForSubArea = [EnumRole.SALES_STAFF, EnumRole.FIELD_ASSISTANT];
    const _roleForWarehouse = [EnumRole.ADMIN_GUDANG, EnumRole.ADMIN_SCALES];

    if (_roleForSubArea.includes(this.selectedRole)) {
      this.loadWithTimeout(() => this.loadSubAreaListData());
    }

    if (_roleForWarehouse.includes(this.selectedRole)) {
      this.loadWithTimeout(() => this.loadWarehouseListData());
    }
  }

  loadWithTimeout = (fn: () => void, ms = 100) => setTimeout(fn, ms);

  loadRoleListData() {
    this.inputSelectRole.fetchDataFn = this.getRoleList.bind(this);
    this.inputSelectRole.fetchData();
  }

  loadUserListData() {
    this.inputSelectTeam.setParams({
      areaId: this.areaID,
      role: this.selectedRole,
    });

    this.inputSelectTeam.fetchDataFn = this.getUserList.bind(this);
    this.inputSelectTeam.fetchData();
  }

  loadSubAreaListData = () => {
    if (!this.hasUserSelected()) return;
    this.inputSelectSubArea.fetchDataFn = this.getScopeList.bind(this);
    this.inputSelectSubArea.fetchData();
  };

  loadWarehouseListData = () => {
    if (!this.hasUserSelected()) return;
    this.inputSelectWarehouse.fetchDataFn = this.getScopeList.bind(this);
    this.inputSelectWarehouse.fetchData();
  };

  displayLabelFn = (val: IInputSelectOptions) => val && val.label;

  getRoleList = () =>
    this.areaTeamService.getRoleListByArea(this.areaID).pipe(
      map((resp) => {
        const _res = resp;
        if (resp) _res.data = resp?.data.filter((item) => item.value !== EnumRole.SUPER_ADMIN);
        return _res;
      })
    );

  getUserList = (params: { areaId: string; role?: EnumRole; page?: number; size?: number; string_filter?: string }) => this.areaTeamService.getUserListByRoleAndArea(params);

  getScopeList = () => {
    const _param = {
      employee_id: this.selectedUser.value,
    };

    return this.areaTeamService.getScopeList(this.areaID, _param);
  };

  hasRoleSelected = () => !!this.selectedRole;

  hasUserSelected = () => this.selectedUser && !!Object.keys(this.selectedUser).length;

  hasSubAreaSelected = () => this.selectedSubArea && !!Object.keys(this.selectedSubArea).length;

  hasWarehouseSelected = () => this.selectedWarehouse && !!Object.keys(this.selectedWarehouse).length;

  // show add sub-area input for ME/FA
  isShowSubAreaInput = () => this.hasRoleSelected() && (this.selectedRole === EnumRole.SALES_STAFF || this.selectedRole === EnumRole.FIELD_ASSISTANT);

  // show add warehouse input for admin gudang/timbangan
  isShowWarehouseInput = () => this.hasRoleSelected() && (this.selectedRole === EnumRole.ADMIN_GUDANG || this.selectedRole === EnumRole.ADMIN_SCALES);

  isEnableMultipleSubArea = () => this.hasRoleSelected() && this.selectedRole === EnumRole.SALES_STAFF;

  handleSubmit() {
    this.isLoading.next(true);
    this.modalResponse.open();

    const _selectedSubArea = this.mapToStringArray(this.selectedSubArea);
    const _selectedWarehouse = this.mapToStringArray(this.selectedWarehouse);

    const _payload = <IPayloadAddTeam>{
      role_enum: this.selectedRole,
      employee_id: this.selectedUser.value,
      sub_area_ids: _selectedSubArea,
      warehouse_ids: _selectedWarehouse,
    };

    this.areaTeamService.submitAddTeam(this.areaID, _payload).subscribe((res) => {
      if (!res) return;
      const msg = res as unknown as string; // need BE fix response format.
      this.responsePost.next(msg);
      this.isLoading.next(false);
    });
  }

  resetSelectedValue() {
    if (this.hasRoleSelected()) this.inputSelectRole.clearSearchInput();
    this.selectedRole = '' as EnumRole;

    if (this.hasUserSelected()) this.inputSelectTeam.clearSearchInput();
    this.selectedUser = {} as IInputSelectOptions;

    if (this.hasSubAreaSelected()) this.inputSelectSubArea.clearSearchInput();
    this.selectedWarehouse = [];

    if (this.hasWarehouseSelected()) this.inputSelectWarehouse.clearSearchInput();
    this.selectedSubArea = [];
  }

  mapToStringArray(data: IInputSelectOptions[]) {
    let _result: string[] = [];

    data.map((item) => {
      _result.push(item.value);
    });

    return _result;
  }

  isValidToSubmit() {
    const _hasRoleAndUser = this.hasRoleSelected() && this.hasUserSelected();
    if (this.isShowSubAreaInput()) return _hasRoleAndUser && this.hasSubAreaSelected();
    if (this.isShowWarehouseInput()) return _hasRoleAndUser && this.hasWarehouseSelected();
    return _hasRoleAndUser;
  }

  goToDetailUser = () => this.router.navigate([`/settings/user/${this.selectedUser.value}`]);

  protected readonly STRING_CONSTANTS = STRING_CONSTANTS;
}
