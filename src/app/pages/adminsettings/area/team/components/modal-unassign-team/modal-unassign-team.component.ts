import { Component, OnInit, ViewChild } from '@angular/core';
import { ModalComponent } from '@shared/components/modal/modal.component';
import { ModalConfig } from '@shared/components/modal/modal.interface';
import { IAreaTeam, IPayloadUnassignTeam, IResponseUnassignTeam } from '../../area-team.interface';
import { BehaviorSubject } from 'rxjs';
import { EnumRole } from '@models/role.model';
import { UtilitiesService } from '@services/utilities.service';
import { STRING_CONSTANTS } from '@config/constants/string.constants';
import { InputSelectAutocompleteComponent } from '@shared/components/v1/input/input-select-autocomplete/input-select-autocomplete.component';
import { AreaTeamService } from '../../area-team.service';
import { ActivatedRoute, Router } from '@angular/router';
import { IInputSelectOptions } from '@shared/components/v1/input/input-select-autocomplete/input-select-autocomplete.interface';
import { IGenericIdName } from '@shared/interface/generic';
import { FormArray, FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';

@Component({
  selector: 'app-modal-unassign-team',
  templateUrl: './modal-unassign-team.component.html',
  styleUrls: ['./modal-unassign-team.component.scss'],
})
export class ModalUnassignTeamComponent implements OnInit {
  @ViewChild('modalUnassign') modalUnassign: ModalComponent;
  modalConfigUnassign = <ModalConfig>{
    modalTitle: 'Unassign user dari area',
    closeButtonLabel: 'Batal',
    dismissButtonLabel: 'Lanjutkan',
    onClose: () => {
      return this.inputSelectUser ? this.inputSelectUser.clearSearchInput() : true;
    },
    onDismiss: () => this.onSubmitUnassign(),
    disableDismissButton: () => this.checkInvalidSubmit(),
  };

  @ViewChild('modalResponse') modalResponse: ModalComponent;
  modalConfigResponse = <ModalConfig>{
    closeButtonLabel: 'Lihat Detail Pengganti',
    dismissButtonLabel: 'Oke',
    onClose: () => this.goToUserDetail(this.getInputSelectedValue().value),
    onDismiss: () => location.reload(),
    disableCloseButton: () => this.isLoading.value,
    disableDismissButton: () => this.isLoading.value,
    showFooter: true,
  };

  dataSubject = new BehaviorSubject({} as IAreaTeam); //selected asignee
  areaName!: string;
  areaId!: string;
  multipleAsigneeCheck = false;
  isLoading = new BehaviorSubject(false);

  savedOptionsData!: IInputSelectOptions[];

  // form for multiple assign replacement
  formReplacement!: FormGroup;

  dataAssignee = {} as IPayloadUnassignTeam;
  dataResponse = new BehaviorSubject({} as IResponseUnassignTeam);

  @ViewChild('inputSelectUser') inputSelectUser: InputSelectAutocompleteComponent;

  constructor(
    private utilsService: UtilitiesService,
    private areaTeamService: AreaTeamService,
    private router: Router,
    private fb: FormBuilder,
    private activeRoute: ActivatedRoute
  ) {}

  get ReplacementFormArray() {
    return <FormArray>this.formReplacement.get('replacement_list');
  }

  ngOnInit() {
    this.formReplacement = this.fb.group({
      replacement_list: this.fb.array([]),
    });
    this.areaId = this.activeRoute.snapshot.params.id;
  }

  openDialog(area: string, val: IAreaTeam) {
    this.areaName = area;
    this.dataSubject.next(val);

    // reset previous state
    this.multipleAsigneeCheck = false;
    if (this.ReplacementFormArray) this.removeFormReplacement();

    // init fetch data user replacement list
    setTimeout(() => this.handleInitInputSelect());
    return this.modalUnassign.open();
  }

  handleInitInputSelect() {
    if (!this.isRoleNeedReplacement()) return;
    this.inputSelectUser.fetchDataFn = this.getReplacementList.bind(this, this.dataSubject.value.id);
    this.inputSelectUser.fetchData();
  }

  async setModalConfigResponse() {
    let _objVal: ModalConfig;

    _objVal = {
      ...this.modalConfigResponse,
      showHeader: true,
      hideCloseButton: () => this.multipleAsigneeCheck || !this.isRoleNeedReplacement() || (this.isOptionalReplacement() && !this.hasSelectedAssignee()),
    };

    this.modalConfigResponse = _objVal;
  }

  async onSubmitUnassign() {
    this.isLoading.next(true);

    // update modal response config
    // mapping payload - post data
    await this.modalUnassign.close();
    await this.setModalConfigResponse();

    const _payload = this.payloadMapper();

    const patchData$ = this.handlePostData(_payload);
    patchData$.subscribe((resp) => {
      if (!resp) return;
      this.dataResponse.next(resp && resp.data);
      this.isLoading.next(false);
    });

    return this.modalResponse.open();
  }

  payloadMapper() {
    if (!this.isRoleNeedReplacement()) return undefined;

    if (!this.multipleAsigneeCheck && this.isOptionalReplacement()) this.handleMapInputReplacement();

    if (this.multipleAsigneeCheck) this.handleMultipleReplacement();

    return this.dataAssignee;
  }

  handlePostData(payload?: IPayloadUnassignTeam) {
    const { id } = this.dataSubject.value;
    return this.areaTeamService.patchUserUnassign(id, payload);
  }

  handleMapInputReplacement() {
    if (this.multipleAsigneeCheck) return;

    this.getInitDataAssignee();
    const _id = this.getInputSelectedValue().value;
    const { sub_area_list, warehouse_list, role_enum } = this.dataSubject.value;

    const _subAreaIds = !!sub_area_list.length ? sub_area_list.map((item) => item.id) : null;
    const _warehouseIds = !!warehouse_list.length ? warehouse_list.map((item) => item.id) : null;

    this.dataAssignee.replacement_list?.push({
      employee_id: _id,
      area_ids: [this.areaId],
      sub_area_ids: _subAreaIds,
      warehouse_ids: _warehouseIds,
    });

    if (this.isOptionalReplacement()) {
      const _unassignIds = {
        unassign_warehouse_ids: this.isShowWarehouseList(role_enum) ? _warehouseIds : null,
        unassign_area_ids: this.isNeedUnassignAreaId() ? [this.areaId] : null,
      };

      // if optional replacement and has selected value
      this.dataAssignee = _id ? { ...this.dataAssignee, ..._unassignIds } : _unassignIds;
    }

    return this.dataAssignee;
  }

  handleMultipleReplacement() {
    const formVal = this.ReplacementFormArray.value as IInputSelectOptions[];

    this.getInitDataAssignee();
    const { sub_area_list, warehouse_list } = this.dataSubject.value;

    formVal.map((item, i) => {
      this.dataAssignee.replacement_list?.push({
        employee_id: item.value,
        area_ids: [this.areaId],
        sub_area_ids: !!sub_area_list.length ? [sub_area_list[i].id] : null,
        warehouse_ids: !!warehouse_list.length ? [warehouse_list[i].id] : null,
      });
    });

    return this.dataAssignee;
  }

  isAssignedMultiple = () => {
    const { role_enum, sub_area_list, warehouse_list } = this.dataSubject.value;
    const _data = this.isShowSubAreaList(role_enum) ? sub_area_list : this.isShowWarehouseList(role_enum) ? warehouse_list : [];

    return _data.length > 1;
  };

  getAsignedListToReplace() {
    const { role_enum, sub_area_list, warehouse_list } = this.dataSubject.value;

    const _dataToMap = this.isThisRole(EnumRole.SALES_STAFF, role_enum)
      ? sub_area_list
      : this.isThisRole(EnumRole.ADMIN_GUDANG, role_enum) || this.isThisRole(EnumRole.ADMIN_SCALES, role_enum)
      ? warehouse_list
      : [];

    return this.mapArrayNameValue(_dataToMap);
  }

  mapArrayNameValue = (data: IGenericIdName[]) => data.map((item) => item.name);

  renderArrayString(arr: any[], key = '') {
    if (!arr || (arr && !arr.length)) return '';
    return this.utilsService.arrayStringJoin(arr, key, ',', true);
  }

  onChangeMultipleAssignee = (checked: boolean) => {
    this.multipleAsigneeCheck = checked;

    if (checked) {
      this.buildFormReplacement();
    } else {
      this.removeFormReplacement();
      setTimeout(() => this.handleInitInputSelect());
    }

    if (this.inputSelectUser) this.savedOptionsData = this.inputSelectUser.options;
  };

  checkInvalidSubmit() {
    // hasEmptyObject?
    if (this.isAssignedMultiple() && this.multipleAsigneeCheck && !this.isOptionalReplacement()) {
      return this.ReplacementFormArray.value.some((item: IInputSelectOptions) => !item.label && !item.value);
    }

    if (this.isRoleNeedReplacement() && !this.isOptionalReplacement()) {
      return this.dataAssignee && !Object.keys(this.dataAssignee).length;
    }

    return false;
  }

  buildFormReplacement() {
    const _data = this.getAsignedListToReplace();
    _data.forEach(() => this.addInputReplacementControl());
  }

  addInputReplacementControl = () => {
    const _initCtrl = this.fb.control('', Validators.required);
    _initCtrl.setValue('');
    this.ReplacementFormArray.push(_initCtrl);
  };

  removeFormReplacement() {
    if (this.ReplacementFormArray) {
      while (this.ReplacementFormArray.length !== 0) this.ReplacementFormArray.removeAt(0);
    }
  }

  getInitDataAssignee = () => {
    this.dataAssignee = { replacement_list: [] };
    return this.dataAssignee;
  };

  getInputReplacementLabel = (i: number) => this.getAsignedListToReplace()[i];

  getInputReplacementControl = (i: number) => <FormControl>this.ReplacementFormArray.at(i);

  showInfoUnassign = (role: EnumRole) => ![EnumRole.REGIONAL_DIRECTOR].includes(role);

  isShowSubAreaList = (role: EnumRole) => [EnumRole.SALES_STAFF, EnumRole.FIELD_ASSISTANT].includes(role);

  isShowWarehouseList = (role: EnumRole) => [EnumRole.ADMIN_GUDANG, EnumRole.ADMIN_SCALES].includes(role);

  isThisRole = (role: EnumRole, checkVal: EnumRole) => role === checkVal;

  isRoleNeedReplacement = () => ![EnumRole.FIELD_ASSISTANT].includes(this.dataSubject.value.role_enum);

  isOptionalReplacement = () => [EnumRole.ADMIN, EnumRole.ADMIN_GUDANG, EnumRole.ADMIN_SCALES, EnumRole.FINANCE, EnumRole.ACCOUNTANT].includes(this.dataSubject.value.role_enum);

  isNeedUnassignAreaId = () => [EnumRole.ADMIN, EnumRole.FINANCE, EnumRole.ACCOUNTANT, EnumRole.ADMIN_GUDANG, EnumRole.ADMIN_SCALES].includes(this.dataSubject.value.role_enum);

  getReplacementList = (id: string, params?: { page: number; size: number }) => this.areaTeamService.getEmployeeReplacementList(id, params);

  goToUserDetail = (id: string) => this.router.navigate([`/settings/user/${id}`]);

  getInputSelectedValue = () => this.inputSelectUser && this.inputSelectUser.SelectedValue;

  hasSelectedAssignee = () => this.getInputSelectedValue() && !!Object.keys(this.getInputSelectedValue()).length;

  protected readonly STRING_CONSTANTS = STRING_CONSTANTS;
  protected readonly EnumRole = EnumRole;
  protected readonly FormControl = FormControl;
}
