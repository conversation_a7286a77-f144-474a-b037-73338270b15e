<app-modal #modalUnassign [modalConfig]="modalConfigUnassign" [modalOptions]="{ size: 'md' }">
  <div class="mb-n6">
    <ng-container *ngIf="dataSubject | async as data">
      <p>
        <span>{{ data.name }} ({{ data.role_string }}) akan diunassign dari </span>
        <ng-container *ngIf="isShowSubAreaList(data.role_enum)" [ngTemplateOutlet]="renderArrayStringTpl" [ngTemplateOutletContext]="{ data: data.sub_area_list }"></ng-container>

        <ng-container
          *ngIf="isShowWarehouseList(data.role_enum)"
          [ngTemplateOutlet]="renderArrayStringTpl"
          [ngTemplateOutletContext]="{ data: data.warehouse_list }"
        ></ng-container>

        <ng-container [ngTemplateOutlet]="areaNameTpl" [ngTemplateOutletContext]="{ role: data.role_enum }"></ng-container>
      </p>

      <!-- noteview: unassign info -->
      <div *ngIf="showInfoUnassign(data.role_enum)" class="mt-6">
        <app-noteview-unassign />
      </div>

      <ng-container *ngIf="isRoleNeedReplacement()">
        <hr class="my-6 border-gray-700" />

        <label class="mb-3">
          <span class="svg-icon svg-icon-2 me-2" [inlineSVG]="STRING_CONSTANTS.ICON.IC_REPLACEMENT_ACCOUNT"></span>
          <span class="fw-bolder"> Pilih {{ data.role_string }} pengganti {{ isOptionalReplacement() ? '(Opsional)' : '' }} </span>
        </label>

        <!-- checkbox assign multiple replacement -->
        <ng-container *ngIf="isAssignedMultiple()">
          <div class="mb-3 mx-n3">
            <mat-checkbox [color]="'primary'" [disableRipple]="true" [checked]="multipleAsigneeCheck" (change)="onChangeMultipleAssignee($event.checked)">
              <span>Digantikan beberapa {{ data.role_string }}</span>
            </mat-checkbox>
          </div>
        </ng-container>

        <ng-template #defaultInputAsignee>
          <app-input-select-autocomplete
            #inputSelectUser
            [useCustomLabel]="true"
            [placeholder]="'Pilih ' + data.role_string + ' pengganti'"
            (selectedValue)="handleMapInputReplacement()"
          />
        </ng-template>

        <!-- multiple replacement -->
        <ng-container *ngIf="multipleAsigneeCheck; else defaultInputAsignee">
          <form [formGroup]="formReplacement">
            <div *ngFor="let item of ReplacementFormArray.controls; let i = index; let last = last" class="animate animate-fade-in" [class.mb-6]="!last">
              <app-input-select-autocomplete
                [label]="data.role_string + ' pengganti (' + getInputReplacementLabel(i) + ')'"
                [placeholder]="'Silahkan pilih ' + data.role_string + ' pengganti'"
                [useCustomLabel]="true"
                [fetchDataFn]="getReplacementList.bind(this, data.id)"
                [optionsData]="savedOptionsData"
                [formControl]="getInputReplacementControl(i)"
                ngDefaultControl
              />
            </div>
          </form>
        </ng-container>
      </ng-container>
    </ng-container>
  </div>
</app-modal>

<app-modal #modalResponse [modalConfig]="modalConfigResponse" [modalOptions]="{ size: 'md' }">
  <div class="d-flex flex-column justify-content-center align-items-center mb-n6">
    <div class="mt-8 animate animate-fade-in" *ngIf="isLoading | async; else responseTpl">
      <mat-spinner />
    </div>

    <ng-template #responseTpl>
      <div class="text-center animate animate-fade-in">
        <span [inlineSVG]="STRING_CONSTANTS.ICON.SUCCESS_ALERT"></span>
        <p class="my-4">
          <ng-container *ngIf="dataSubject | async as data">
            <ng-container [ngTemplateOutlet]="defaultTpl" [ngTemplateOutletContext]="{data}"></ng-container>
            <!--
              has replacement assignee but not multiple
              role need replacement
              role has optional replacement
              show noteview: only if has multiple replacement assignee
            -->
            <ng-container [ngTemplateOutlet]="multipleAsigneeCheck ? assignedMultipleTpl : assigneeTpl" [ngTemplateOutletContext]="{ role: data.role_string }"></ng-container>
          </ng-container>
        </p>
      </div>
    </ng-template>

    <ng-template #defaultTpl let-data="data">
      <span>{{ data.name }} ({{ data.role_string }}) berhasil di unassign dari </span>
      <ng-container *ngIf="isShowSubAreaList(data.role_enum)" [ngTemplateOutlet]="renderArrayStringTpl" [ngTemplateOutletContext]="{ data: data.sub_area_list }"></ng-container>
      <ng-container *ngIf="isShowWarehouseList(data.role_enum)" [ngTemplateOutlet]="renderArrayStringTpl" [ngTemplateOutletContext]="{ data: data.warehouse_list }"></ng-container>
      <ng-container [ngTemplateOutlet]="areaNameTpl" [ngTemplateOutletContext]="{ role: data.role_enum }"></ng-container>
    </ng-template>

    <ng-template #assigneeTpl>
      <ng-container *ngIf="dataResponse | async as resp">
        <span *ngIf="resp.replacement && !!resp.replacement.length"> {{ resp.replacement[0].user }} ({{ dataSubject.value.role_string }}) ditunjuk sebagai pengganti. </span>
      </ng-container>
    </ng-template>

    <ng-template #assignedMultipleTpl let-role="role">
      <app-note-view [classNoteView]="'my-6'" [color]="'info'" [icon]="STRING_CONSTANTS.ICON.IC_INFORMATION" [extraContent]="true">
        <ul class="ms-n4 text-start" *ngIf="dataResponse | async as data">
          <li *ngFor="let item of data.replacement" class="my-1">{{ item['scope'] }} ditugaskan ke {{ item['user'] }} ({{ role }})</li>
        </ul>
      </app-note-view>
    </ng-template>
  </div>
</app-modal>

<ng-template #renderArrayStringTpl let-data="data">
  <span>{{ renderArrayString(data, 'name') }}</span>
</ng-template>

<ng-template #areaNameTpl let-role="role">
  <span class="ms-1"> {{ isThisRole(EnumRole.REGIONAL_DIRECTOR, role) || !isShowSubAreaList(role) || !isShowWarehouseList(role) ? areaName : '(' + areaName + ')' }}. </span>
</ng-template>
