import { Component } from '@angular/core';
import { STRING_CONSTANTS } from '@config/constants/string.constants';

@Component({
  selector: 'app-noteview-unassign',
  styleUrls: ['./noteview-unassign.component.scss'],
  template: ` <app-note-view [color]="'info'" [icon]="STRING_CONSTANTS.ICON.IC_INFORMATION" [extraContent]="true">
    <p class="mb-2">Jika user di unassign:</p>
    <ul class="ms-n4">
      <li>Status akan menjadi Teregistrasi dan tidak bisa login ke platform Maxxi Agri</li>
      <li>User dapat diaktifkan kembali dengan menambah cakupan</li>
    </ul>
  </app-note-view>`,
})
export class NoteviewUnassignComponent {
  protected readonly STRING_CONSTANTS = STRING_CONSTANTS;
}
