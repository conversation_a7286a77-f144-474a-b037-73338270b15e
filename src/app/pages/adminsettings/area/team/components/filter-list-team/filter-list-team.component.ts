import { AfterViewInit, Component, EventEmitter, HostBinding, Input, OnInit, Output, ViewChild } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { STRING_CONSTANTS } from '@config/constants/string.constants';
import { Form<PERSON>uilder, FormControl, FormGroup } from '@angular/forms';
import { ActivatedRoute, ActivatedRouteSnapshot } from '@angular/router';
import { MatChipListbox, MatChipListboxChange } from '@angular/material/chips';
import { BaseService } from '@services/base-service.service';
import { IRole } from '@models/user.model';
import { API } from '@config/constants/api.constant';
import { FilterService } from '@services/filter.service';
import { EnumRole } from '@models/role.model';

@Component({
  selector: 'app-filter-list-team',
  templateUrl: './filter-list-team.component.html',
  styleUrls: ['./filter-list-team.component.scss'],
})
export class FilterListTeamComponent implements OnInit, AfterViewInit {
  @HostBinding('class') class = 'd-flex w-100 justify-content-between';

  @Input() finishLoadingSubject = new BehaviorSubject(true);
  @Input() searchInputValue: string = '';
  @Input() filterInputActivated = false;
  @Input() showAddTeamCTA = true;

  @Output() addTeamAction = new EventEmitter();

  filterForm: FormGroup;
  filterInputOpened = false;
  filterPills = new BehaviorSubject([] as IRole[]);

  activeRouteSnapshot!: ActivatedRouteSnapshot;
  isLoading = new BehaviorSubject(false);

  @ViewChild('filterPillsBox') filterPillsBox: MatChipListbox;

  constructor(private fb: FormBuilder, private activeRoute: ActivatedRoute, private baseService: BaseService, private filterService: FilterService) {
    this.activeRouteSnapshot = this.activeRoute.snapshot;
  }

  get RoleControl() {
    return <FormControl>this.filterForm.get('role');
  }

  ngOnInit(): void {
    this.initFilterForm();
  }

  initFilterForm() {
    this.filterForm = this.fb.group({
      role: '',
    });
  }

  initFilterPillsBox() {
    this.isLoading.next(true);
    const getRoles$ = this.baseService.getData<IRole[]>(API.USER.LIST_ROLE);
    getRoles$.subscribe((resp) => {
      if (!resp) return;
      const data = resp.data.filter((item) => item.enum_value !== EnumRole.SUPER_ADMIN);
      this.filterPills.next(data);
      this.isLoading.next(false);
    });
  }

  actionResetFilter() {
    this.filterInputOpened = false;
    this.resetFilterForm();
  }

  actionStatusChange(e: MatChipListboxChange) {
    let _selectedKeys = <string[]>[];
    e.value.forEach((item: string) => _selectedKeys.push(item));
    this.RoleControl.patchValue(_selectedKeys.join());
  }

  actionSubmitFilter() {
    if (!this.RoleControl.value) return;

    this.filterInputOpened = false;
    const _extras = {
      queryParams: {
        string_filter: this.searchInputValue,
        role: this.RoleControl.value,
      },
      state: history.state,
    };

    return this.filterService.submitFilter(this.filterForm, _extras);
  }

  actionSearch = (e: string) => this.filterService.onSearch(e);

  toggleOpenFilter = () => {
    this.filterInputOpened = !this.filterInputOpened;
    // fetch data on open filter.
    if (!this.filterPills.value.length && this.filterInputOpened) return this.initFilterPillsBox();
  };

  resetFilterForm = () => this.filterService.resetFilter(this.filterForm, ['role']);

  onAddTeam() {
    this.addTeamAction.emit(true);
  }

  ngAfterViewInit(): void {}

  protected readonly STRING_CONSTANTS = STRING_CONSTANTS;
}
