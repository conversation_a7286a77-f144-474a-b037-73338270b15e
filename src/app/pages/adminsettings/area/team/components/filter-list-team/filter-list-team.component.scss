.filter-pills {

  .chips {
    background-color: var(--kt-white);
    border: 1px solid var(--kt-gray-300);
    border-radius: 38px;
    height: 40px;
    margin: {
      top: 6px;
      bottom: 6px;
    }

    .custom-x-icon {
      opacity: 0;
      visibility: hidden;
      margin: 0;
      padding: 0;

      //&:after {
      //  content: "x";
      //  display: block;
      //  width: 0;
      //  height: 0;
      //  border-radius: 50%;
      //  background: var(--kt-primary);
      //  color: white;
      //  text-align: center;
      //  margin: 0 0 0 8px;
      //  font-size: 12px;
      //  line-height: 16px;
      //  box-shadow: 0 1px 2px rgba(0,0,0, 0.75);
      //}
    }

    &.mat-mdc-chip-selected {
      background: #F0FFCE;
      border-color: #688238;
    }

    &.mat-mdc-chip-selected .custom-x-icon {
      opacity: 1;
      visibility: visible;
      margin: 0 0 0 8px;
      //&:after {
      //  min-width: 16px;
      //  height: 16px;
      //}
    }
  }

  ::ng-deep {
    .chips .mdc-evolution-chip__graphic {
      display: none;
    }

    .chips .mdc-evolution-chip__cell {
      padding: 0 0 0 16px;
    }

    .chips .mat-mdc-chip-action-label {
      color: var(--kt-gray-700);
    }

    .chips .mat-mdc-chip-action::before {
      border: 0;
    }
  }
}
// todo:make global
