import { Routes } from '@angular/router';
import { RoleGuard } from '@guards/role.guard';

const adminSettingsModule = () => import('./adminsettings/admin-settings.module').then((m) => m.AdminSettingsModule);
const productModule = () => import('./products/products.module').then((m) => m.ProductsModule);
const distributorModule = () => import('./distributor/distributor.module').then((m) => m.DistributorModule);
const retailerModule = () => import('./retailer/retailers.module').then((m) => m.RetailersModule);
const purchaseModule = () => import('./purchase-order/purchase-order.module').then((m) => m.PurchaseOrderModule);
const adminCmsModule = () => import('./admin-cms/admin-cms.module').then((m) => m.AdminCmsModule);
const salesOrderModule = () => import('./sales-order/sales-order.module').then((m) => m.SalesOrderModule);
const spmModule = () => import('./spm/spm.module').then((m) => m.SpmModule);
const deliveryOrderModule = () => import('./delivery-order/delivery-order.module').then((m) => m.DeliveryOrderModule);
const salesMarketingModule = () => import('./sales-marketing/sales-marketing.module').then((m) => m.SalesMarketingModule);
const reportModule = () => import('./report/report.module').then((m) => m.ReportModule);
const masterDataModule = () => import('./master-data/master-data.module').then((m) => m.MasterDataModule);
const rewardSettingModule = () => import('./reward-setting/reward-setting.module').then((m) => m.RewardSettingModule);
const Routing: Routes = [
  // DISTRIBUTOR
  {
    path: 'distributor',
    canActivateChild: [RoleGuard],
    loadChildren: distributorModule,
    data: {
      enum: 'DISTRIBUTOR',
      label: 'Distributor',
      icon: 'ic_distributor.svg',
      activeClass: 'active',
      children: [
        {
          enum: 'LIST_TEREGISTRASI',
          label: 'Teregistrasi',
          path: 'distributor/teregistrasi/active',
          children: [],
        },
        {
          enum: 'LIST_MENUNGGU',
          label: 'Sedang Menunggu',
          path: 'distributor/pending/registration',
          children: [],
        },
        {
          enum: 'LIST_DITOLAK',
          label: 'Ditolak',
          path: 'distributor/reject/registration',
          children: [],
        },
      ],
    },
  },

  // RETAILER
  {
    path: 'retailer',
    canActivateChild: [RoleGuard],
    loadChildren: retailerModule,
    data: {
      enum: 'RETAILER',
      label: 'Retailer',
      icon: 'ic_retailer.svg',
      activeClass: 'active',
      children: [
        {
          enum: 'LIST_RETAILER_MENUNGGU',
          label: 'Terverifikasi',
          path: 'retailer/terverifikasi/active',
          children: [],
        },
        {
          enum: 'LIST_RETAILER_MENUNGGU',
          label: 'Sedang Menunggu',
          path: 'retailer/pending/register',
          children: [],
        },
        {
          enum: 'LIST_RETAILER_DITOLAK',
          label: 'Ditolak',
          path: 'retailer/reject',
          children: [],
        },
      ],
    },
  },

  // PURCHASE ORDER
  {
    path: 'purchase-order',
    canActivateChild: [RoleGuard],
    loadChildren: purchaseModule,
    data: {
      enum: 'PURCHASE_ORDER',
      label: 'Purchase Order',
      icon: 'ic_trolley.svg',
      activeClass: 'active',
      children: [
        {
          enum: 'LIST_PO_ALL',
          label: 'List Purchase Order',
          path: 'purchase-order/list',
          children: [],
        },
        {
          enum: 'LIST_PO_ALL',
          label: 'Sedang Menunggu',
          path: 'purchase-order/waiting',
          children: [],
        },
        {
          enum: 'LIST_PO_NEED_FULLFILL',
          label: 'Siap Diproses',
          path: 'purchase-order/needfulfill',
          children: [],
        },
        {
          enum: 'LIST_PO_DIPROSES',
          label: 'Diproses',
          path: 'purchase-order/inprogress',
          children: [],
        },
        {
          enum: 'LIST_PO_HISTORY',
          label: 'Histori',
          path: 'purchase-order/history',
          children: [],
        },
      ],
    },
  },

  // SALES ORDER
  {
    path: 'sales-order',
    canActivateChild: [RoleGuard],
    loadChildren: salesOrderModule,
    data: {
      enum: 'SALES_ORDER',
      label: 'Sales Order',
      icon: 'ic_task_alt.svg',
      path: 'sales-order',
      activeClass: 'active',
      children: [
        {
          enum: 'LIST_SO_ALL',
          label: 'List Sales Order',
          path: 'sales-order/list',
          children: [],
        },
        {
          enum: 'LIST_SO_MENUNGGU',
          label: 'Menunggu Konfirmasi',
          path: 'sales-order/pending/center',
          children: [],
        },
        {
          enum: 'LIST_SO_DIPROSES',
          label: 'Diproses',
          path: 'sales-order/onprogress',
          children: [],
        },
        {
          enum: 'LIST_SO_HISTORY',
          label: 'Histori',
          path: 'sales-order/history',
          children: [],
        },
      ],
    },
  },

  // SPM Order
  {
    path: 'spm',
    canActivateChild: [RoleGuard],
    loadChildren: spmModule,
    data: {
      enum: 'SURAT_PERINTAH_MUAT',
      label: 'Surat Perintah Muat',
      icon: 'ic_spm_alt.svg',
      activeClass: 'active',
      children: [
        {
          enum: 'LIST_SPM_ALL',
          label: 'List SPM',
          path: 'spm/list',
          children: [],
        },
        {
          enum: 'LIST_SPM_BUTUH_DIPROSES',
          label: 'Menunggu Konfirmasi',
          path: 'spm/waiting-confirmation',
          children: [],
        },
        {
          enum: 'LIST_SPM_BUTUH_DIPROSES',
          label: 'Butuh Diproses',
          path: 'spm/created',
          children: [],
        },
        {
          enum: 'LIST_SPM_DIPROSES',
          label: 'Diproses',
          path: 'spm/pending',
          children: [],
        },
        {
          enum: 'LIST_SPM_HISTORY',
          label: 'Histori',
          path: 'spm/history',
          children: [],
        },
      ],
    },
  },

  // DELIVERY ORDER
  {
    path: 'delivery-order',
    canActivateChild: [RoleGuard],
    loadChildren: deliveryOrderModule,
    data: {
      enum: 'DELIVERY_ORDER',
      label: 'Delivery Order',
      icon: 'ic_delivery.svg',
      activeClass: 'active',
      children: [
        {
          enum: 'LIST_DELIVERY_ORDER',
          label: 'List Delivery Order',
          path: 'delivery-order/list',
          children: [],
        },
      ],
    },
  },

  // Products
  {
    path: 'products',
    canActivateChild: [RoleGuard],
    loadChildren: productModule,
    data: {
      enum: 'PRODUCT_CATALOG',
      label: 'Product Catalog',
      icon: 'ic_package.svg',
      activeClass: 'active',
      path: 'products',
      children: [
        {
          enum: 'LIST_PRODUCT_PUBLISHED',
          label: 'List Products',
          path: 'products/list-published',
          children: [],
        },
        {
          enum: 'LIST_PRODUCT_PUBLISH_REQUEST',
          path: 'products/publish-request',
          label: 'Publish Request',
          children: [],
        },
        {
          enum: 'LIST_QR_PRODUCT',
          path: 'products/list-qr-produk',
          label: 'QR Produk',
          children: [],
        },
      ],
    },
  },

  // ADMIN SETTINGS
  {
    path: 'settings',
    canActivateChild: [RoleGuard],
    loadChildren: adminSettingsModule,
    data: {
      enum: 'SETTINGS',
      label: 'Admin Settings',
      icon: 'ic_option.svg',
      activeClass: 'active',
      children: [
        // USERS SETTINGS
        {
          enum: 'USER_MANAGEMENT',
          label: 'User Management',
          path: 'settings/users/list',
          children: [
            {
              enum: 'LIST_USER',
              label: 'User List',
              path: 'settings/users/list',
              children: [],
            },
            {
              enum: 'CREATE_USER',
              label: 'Tambah User',
              path: 'settings/users/create',
              children: [],
            },
          ],
        },

        // ROLE SETTINGS
        {
          enum: 'SETTINGS_ROLE',
          label: 'Role Setting',
          path: 'settings/roles',
          children: [],
        },

        // AREA SETTINGS
        {
          enum: 'AREA_MANAGEMENT',
          label: 'Area Management',
          path: 'settings/area/list',
          children: [
            {
              enum: 'LIST_AREA',
              label: 'Area List',
              path: 'settings/area/list',
              children: [],
            },
            {
              enum: 'CREATE_AREA',
              label: 'Tambah Area',
              path: 'settings/area/create',
              children: [],
            },
          ],
        },
      ],
    },
  },

  // Sales Marketing
  {
    path: 'sales-marketing',
    canActivateChild: [RoleGuard],
    loadChildren: salesMarketingModule,
    data: {
      enum: 'SALES_MARKETING_PROGRAM',
      label: 'Sales & Marketing',
      icon: 'ic_marketing_promo.svg',
      activeClass: 'active',
      children: [
        {
          enum: 'DISCOUNT_SETTING',
          label: 'Discount Setting',
          path: 'sales-marketing/discount-setting/sales',
          children: [
            {
              enum: 'SALES_DISCOUNT',
              label: 'Sales Discount',
              path: 'sales-marketing/discount-setting/sales',
              children: [],
            },
            {
              enum: 'CBD_DISCOUNT',
              label: 'CBD Discount',
              path: 'sales-marketing/discount-setting/cbd',
              children: [],
            },
          ],
        },

        {
          enum: 'PROGRAM_MARKETING_SETTING',
          label: 'Program Marketing Distributor',
          path: 'sales-marketing/program-marketing/list',
          children: [],
        },

        {
          enum: 'PROGRAM_MARKETING_SETTING',
          label: 'Program Marketing Retailer',
          path: 'sales-marketing/program-marketing-retailer/list',
          children: [],
        },
      ],
    },
  },

  // REWARDS
  {
    path: 'reward-setting',
    canActivateChild: [RoleGuard],
    loadChildren: rewardSettingModule,
    data: {
      enum: 'REWARD_SETTING', // REWARD_SETTING
      label: 'Reward Setting',
      icon: 'ic_promo.svg',
      activeClass: 'active',
      children: [
        {
          enum: 'REWARD_PRODUCT',
          label: 'Reward Produk',
          path: 'reward-setting',
          children: [
            {
              enum: 'LIST_REWARD',
              label: 'List Reward',
              path: 'reward-setting/product/list',
              children: [],
            },
            {
              enum: 'CREATE_REWARD',
              label: 'Tambah Reward',
              path: 'reward-setting/product/form',
              children: [],
            },
          ],
        },
        {
          enum: 'REWARD_RETAILER',
          label: 'Hadiah Retailer',
          path: 'reward-setting/retailer-level/list',
          children: [],
        },
      ],
    },
  },

  // Master Data
  {
    path: 'master-data',
    canActivateChild: [RoleGuard],
    loadChildren: masterDataModule,
    data: {
      enum: 'DISCOUNT_SETTING',
      label: 'Master Data',
      icon: 'ic_data_management.svg',
      activeClass: 'active',
      children: [
        {
          enum: 'SALES_DISCOUNT',
          label: 'List Data',
          path: 'master-data/list',
          children: [],
        },
        {
          enum: 'CBD_DISCOUNT',
          label: 'Tambah data',
          path: 'master-data/form',
          children: [],
        },
      ],
    },
  },

  // Report
  {
    path: 'report',
    canActivateChild: [RoleGuard],
    loadChildren: reportModule,
    data: {
      enum: 'REPORT',
      label: 'Report',
      icon: 'ic_task.svg',
      activeClass: 'active',
      children: [
        {
          enum: 'LIST_NEED_FULLFILL',
          label: 'Need Fullfill Item',
          path: 'report/need-fullfill',
          children: [],
        },
      ],
    },
  },

  {
    path: 'admin-cms',
    loadChildren: adminCmsModule,
    data: {
      enum: 'ADMIN_CMS',
      children: [],
    },
  },

  {
    path: '',
    redirectTo: 'distributor/teregistrasi/active',
    pathMatch: 'full',
  },

  {
    path: '**',
    redirectTo: 'error/404',
  },
];

export { Routing };
