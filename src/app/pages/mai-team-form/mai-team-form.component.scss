* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  display: flex;
}

.form-member {
  width: 100%;
  height: min-content;
  background-color: #7b9640;
  display: grid;
  place-items: center;
  padding-inline: 70px;
  padding-block: 30px;
  min-height: 100vh;
}

form {
  background-color: white;
  display: flex;
  flex-direction: column;
  gap: 10px;
  height: max-content;
  min-width: 500px;
  padding: 20px;
}

.team-name {
  width: 100%;
  border: solid 1px darkgray;
  display: flex;
  flex-direction: column;
}

.label-name {
  display: flex;
  flex-direction: row;
  padding: 1rem;
}

.role-area {
  display: flex;
  flex-direction: column;
  width: 100%;
  gap: 10px;
}

button {
  padding: 10px;
  border-radius: 10px;
  background-color: #688238;
  color: white;
  cursor: pointer;
  text-decoration: none;
  border: none;
  justify-content: center;
}

.add-team {
  background-color: white;
  border: solid 2px black;
  color: black;
}

.reset-team {
  background-color: #ff6363;
}

input {
  width: 100%;
  border: solid 1px darkgray;
  border-radius: 5px;
  padding: 5px;
}

label {
  width: 10rem;
}
