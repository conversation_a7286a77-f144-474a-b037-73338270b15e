<div [formGroup]="formGroup"
     style="margin-bottom: 1rem; border: 1px solid #ccc; gap: 10px; display: flex; width: 100%; flex-direction: column; padding: 1rem;">

  <div class="label-name">
    <label>
      Nama <PERSON>a:
    </label>
    <input formControlName="name" />
  </div>

  <div class="label-name">
    <label>
      Role:
    </label>
    <select formControlName="role">
      <option value="" disabled selected>Pilih Role</option>
      <option *ngFor="let role of roles" [value]="role">{{ role }}</option>
    </select>
  </div>

  <button type="button" (click)="remove.emit()">Hapus</button>
</div>
