import { Component, EventEmitter, Input, Output } from '@angular/core';
import { FormGroup, ReactiveFormsModule } from '@angular/forms';
import { Ng<PERSON>orOf, NgIf } from '@angular/common';

@Component({
  selector: 'app-member-form',
  standalone: true,
  imports: [
    ReactiveFormsModule,
    NgIf,
    NgForOf,
  ],
  templateUrl: './member-form.component.html',
  styleUrl: './member-form.component.scss'
})
export class MemberFormComponent {
  @Input() formGroup!: FormGroup;
  @Output() remove = new EventEmitter<void>();

  roles = ['Backend Engineer', 'Frontend Engineer', 'Product Manager', 'UI/UX', 'QA Engineer'];
}
