<app-modal #modal [modalConfig]="modalConfig" [modalOptions]="modalOptions">
  <div class="w-100">
    <div cardBody class="detail-modal-products">
      <p class="h2">{{ detailModal.value.title }}</p>
      <div class="h-100 scroll-content my-40">
        <div *ngFor="let item of detailModal.value.detail" class="row w-100 mb-2">
          <label class="col-lg-4 text-gray-700">{{ item.key }}</label>
          <div class="col-lg-8">
            <span class="text-gray-800">:&nbsp; {{ item.value }}</span>
          </div>
        </div>
      </div>
    </div>

    <div>
      <p class="h4">Dokumen</p>
      <div *ngFor="let item of documentList.value" class="d-flex justify-content-between p-5">
        <div>
          <span [inlineSVG]="ASSETS_ILLUSTRATION.IL_PDF" class="img-fluid"></span>
          <span>&nbsp; {{ item.name }}</span>
        </div>
        <div>
          <span (click)="toDownload(item)" [inlineSVG]="ASSETS_ICON.IC_DOWNLOAD" class="img-fluid cursor-pointer"></span>
        </div>
      </div>
    </div>
  </div>
</app-modal>
