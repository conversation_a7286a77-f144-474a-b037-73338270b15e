import { ChangeDetectorRef, Component, Input, OnInit, ViewChild } from '@angular/core';
import { SwiperComponent } from 'swiper/angular';
import { NgbModalOptions, NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { IDetailDocument, IModalDetailWithDocument, ModalConfig } from '@shared/components/modal/modal.interface';
import { BehaviorSubject } from 'rxjs';
import { STRING_CONSTANTS } from '@config/constants/string.constants';
import { SwiperOptions } from 'swiper';
import { ModalComponent } from '@shared/components/modal/modal.component';
import { PdfService } from '@services/pdf.service';

@Component({
  selector: 'app-modal-detail-with-document',
  templateUrl: './modal-detail-with-document.component.html',
  styleUrls: ['./modal-detail-with-document.component.scss'],
})
export class ModalDetailWithDocumentComponent implements OnInit {
  @ViewChild('swiperVariant', { static: false }) swiperVariant: SwiperComponent;
  @ViewChild('modal') private modalDetail: ModalComponent;

  @Input() public modalConfig: ModalConfig;
  @Input() public modalOptions: NgbModalOptions;
  @Input() public detailModal: BehaviorSubject<IModalDetailWithDocument> = new BehaviorSubject<IModalDetailWithDocument>({} as IModalDetailWithDocument);
  @Input() public isLoading: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(true);
  @Input() callbackDelete?: (value: any) => Promise<void> | void;

  ASSETS_ICON = STRING_CONSTANTS.ICON;
  ASSETS_ILLUSTRATION = STRING_CONSTANTS.ILLUSTRATIONS;
  documentList: BehaviorSubject<IDetailDocument[]> = new BehaviorSubject<IDetailDocument[]>([]);

  defaultOptions: NgbModalOptions = {
    size: 'lg',
  };
  defaultConfig: ModalConfig = {
    showFooter: false,
    showHeader: false,
    onClose: () => {
      // this.urlPreview.next(null);
      return true;
    },
  };
  config: SwiperOptions = {
    slidesPerView: 3,
    spaceBetween: 50,
    navigation: false,
    pagination: { clickable: true },
  };
  private modalRef: NgbModalRef;

  constructor(private ref: ChangeDetectorRef, private pdfService: PdfService) {}

  set setDocumentList(data: IDetailDocument[]) {
    this.documentList.next(data);
  }

  ngOnInit(): void {
    this.modalOptions = typeof this.modalOptions === 'undefined' ? this.defaultOptions : { ...this.modalOptions, ...this.defaultOptions };

    this.modalConfig = {
      ...this.defaultConfig,
      ...this.modalConfig,
    };

    this.setDataModal();
  }

  setDataModal() {
    this.detailModal.subscribe((val) => {
      if (!val) return;
      this.setDocumentList = val.documents;
    });
  }

  openModal = () => {
    return this.modalDetail.open();
  };

  toDownload(item: IDetailDocument) {
    return this.pdfService.downloadPdf(item).subscribe((res) => res);
  }

  async dismiss(): Promise<void> {
    if (this.modalConfig.shouldDismiss === undefined || (await this.modalConfig.shouldDismiss())) {
      const result = this.modalConfig.onDismiss === undefined || (await this.modalConfig.onDismiss());
      this.modalRef.dismiss(result);
    }
  }
}
