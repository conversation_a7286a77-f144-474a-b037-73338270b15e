import { ChangeDetectorRef, Component, Input, OnInit, ViewChild } from '@angular/core';
import { IModalDetailWithImage, ModalConfig } from '@shared/components/modal/modal.interface';
import { NgbModalOptions, NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { BehaviorSubject } from 'rxjs';
import { SwiperComponent } from 'swiper/angular';
import { STRING_CONSTANTS } from '@config/constants/string.constants';
import { SwiperOptions } from 'swiper';
import { ModalComponent } from '@shared/components/modal/modal.component';

@Component({
  selector: 'app-modal-detail-with-image',
  templateUrl: './modal-detail-with-image.component.html',
  styleUrls: ['./modal-detail-with-image.component.scss'],
})
export class ModalDetailWithImageComponent implements OnInit {
  @ViewChild('swiperVariant', { static: false }) swiperVariant: SwiperComponent;
  private modalRef: NgbModalRef;

  @Input() public modalConfig: ModalConfig;
  @Input() public modalOptions: NgbModalOptions;
  @Input() public detailModal: BehaviorSubject<IModalDetailWithImage> = new BehaviorSubject<IModalDetailWithImage>({} as IModalDetailWithImage);
  @Input() public isLoading: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(true);

  @Input() callbackDelete?: (value: any) => Promise<void> | void;

  ASSETS_ICON = STRING_CONSTANTS.ICON;
  urlPreview: BehaviorSubject<string | null> = new BehaviorSubject<string | null>(null);
  imageList: BehaviorSubject<string[]> = new BehaviorSubject<string[]>([]);
  withAction: boolean = false;

  defaultOptions: NgbModalOptions = {
    size: 'lg',
  };
  defaultConfig: ModalConfig = {
    showFooter: false,
    showHeader: false,
    onClose: () => {
      this.urlPreview.next(null);
      return true;
    },
  };
  config: SwiperOptions = {
    slidesPerView: 3,
    spaceBetween: 50,
    navigation: false,
    pagination: { clickable: true },
  };
  @ViewChild('modal') private modalDetail: ModalComponent;

  constructor(private ref: ChangeDetectorRef) {}

  set setUrlPreview(url: string) {
    this.urlPreview.next(url);
  }

  set setImageList(urls: string[]) {
    this.imageList.next(urls);
  }

  ngOnInit(): void {
    this.modalOptions = typeof this.modalOptions === 'undefined' ? this.defaultOptions : { ...this.modalOptions, ...this.defaultOptions };

    this.modalConfig = {
      ...this.defaultConfig,
      ...this.modalConfig,
    };
    this.detailModal.subscribe((value) => {
      if (value) {
        const findAction = value.detail?.find((value) => value.key === 'Action');
        if (findAction) {
          this.withAction = true;
        }
        this.urlPreview.next(value.images?.[0] ?? '');
        this.ref.detectChanges();
      }
    });
  }

  openModal = () => {
    return this.modalDetail.open();
  };

  closeModal = () => {
    return this.modalDetail.close();
  };

  slideNext() {
    this.swiperVariant.swiperRef.slideNext(100, false);
  }

  slidePrev() {
    this.swiperVariant.swiperRef.slidePrev(100, false);
  }

  counter(i: number) {
    return new Array(i);
  }

  onClickImagePreview($event: string) {
    this.urlPreview.next($event);
  }

  async dismiss(): Promise<void> {
    if (this.modalConfig.shouldDismiss === undefined || (await this.modalConfig.shouldDismiss())) {
      const result = this.modalConfig.onDismiss === undefined || (await this.modalConfig.onDismiss());
      this.modalRef.dismiss(result);
    }
  }

  async handleAction(value: string): Promise<void> {
    this.callbackDelete === undefined ? await this.dismiss() : await this.callbackDelete(value);
    return;
  }
}
