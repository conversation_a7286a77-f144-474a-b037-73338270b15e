<app-modal #modal [modalConfig]="modalConfig" [modalOptions]="modalOptions">
  <div class="d-flex w-100 justify-content-between flex-column">
    <div cardBody class="d-flex w-100 justify-content-between detail-modal-products">
      <div>
        <h1>{{ detailModal.value.title }}</h1>
        <div class="mt-4 mb-3 h-100 scroll-content">
          <div *ngFor="let item of detailModal.value.detail" class="row mt-4 w-100">
            <div *ngIf="item.key === 'Action' && detailModal.value.privilege?.delete" class="col-lg-12 text-center mt-5">
              <button (click)="handleAction(item.value)" class="btn btn-link">
                <span class="text-danger fw-bold fs-14"><u>Hapus Produk Eksklusif</u></span>
              </button>
            </div>
            <div *ngIf="item.key === 'Reason'; else defaultView" class="detail-reason mb-4">
              <div class="px-10 py-3">
                <label class="text-gray-400 mb-3">Alasan Perubahan</label>
                <ul>
                  <li *ngFor="let val of item.value">{{ val }}</li>
                </ul>
              </div>
            </div>
            <ng-template #defaultView>
              <label *ngIf="item.key !== 'Action'" class="col-lg-5 text-gray-400">{{ item.key }}</label>
              <div *ngIf="item.key !== 'Action'" class="col-lg-7">
                <span class="text-gray-800">: {{ item.value }}</span>
              </div>
            </ng-template>
          </div>
        </div>
      </div>

      <div class="d-flex align-items-center justify-content-center flex-column left-section">
        <div *ngIf="isLoading | async">
          <div class="w-100">
            <div class="w-100 d-flex align-items-center justify-content-center flex-column">
              <app-skeleton-text [height]="244" [isLoading]="isLoading" [width]="244"></app-skeleton-text>
              <div class="d-flex flex-row">
                <div *ngFor="let item of counter(5)" class="px-2">
                  <app-skeleton-text [height]="56" [isLoading]="isLoading" [width]="56"></app-skeleton-text>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="w-100 d-flex align-items-center flex-column">
          <div class="d-flex justify-content-center align-items-center img-fluid"
               style="min-height: 300px; height: auto">
<!--            <div *ngIf="urlPreview.value">-->
              <app-image-preview
                      [image]="(urlPreview | async) ?? null"
                      [openTextLabel]="false"
                      [titleModal]="'Dokumen Persetujuan'"
              ></app-image-preview>
<!--            </div>-->
          </div>
          <div *ngIf="imageList.value.length > 1" class="w-100 paginator-group">
            <div (click)="slidePrev()" *ngIf="imageList.value.length >= 3" [inlineSVG]="ASSETS_ICON.IC_ANGLE"
                 class="swiper-button"></div>
            <div class="w-100 swiper-body-product-detail">
              <swiper #swiperVariant [config]="config" [slidesPerView]="3" [spaceBetween]="10" class="d-flex w-100">
                <ng-template *ngFor="let item of imageList.value" swiperSlide>
                  <div (click)="onClickImagePreview(item)"
                       class="border-primary image-container cursor-pointer {{ item === urlPreview.value && 'active' }}">
                    <img alt="image" height="56" ngSrc="{{ item }}" style="white-space: nowrap" width="56"/>
                  </div>
                </ng-template>
              </swiper>
            </div>
            <div (click)="slideNext()" *ngIf="imageList.value.length >= 3" [inlineSVG]="ASSETS_ICON.IC_ANGLE"
                 class="swiper-button button-next"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</app-modal>
