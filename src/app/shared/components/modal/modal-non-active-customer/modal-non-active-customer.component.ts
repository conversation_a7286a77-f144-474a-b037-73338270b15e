import { ChangeDetectorRef, Component, Input, OnInit, ViewChild } from '@angular/core';
import { IResponseNonActiveCustomer, ModalConfig, ModalOption } from '@shared/components/modal/modal.interface';
import { ModalFormComponent } from '@shared/components/modal/modal-form/modal-form.component';
import { IGenericLabelValue, IGenericNameUrl } from '@shared/interface/generic';
import { STRING_CONSTANTS } from '@config/constants/string.constants';
import { FormArray, FormBuilder, FormControl, Validators } from '@angular/forms';
import { BehaviorSubject } from 'rxjs';
import { ModalComponent } from '@shared/components/modal/modal.component';
import { ActivatedRoute, Router } from '@angular/router';
import { UtilitiesService } from '@services/utilities.service';
import { BaseService } from '@services/base-service.service';
import { API } from '@config/constants/api.constant';
import { DistributorService } from '../../../../pages/distributor/distributor.service';
import { RetailerService } from '../../../../pages/retailer/retailer.service';

@Component({
  selector: 'app-modal-non-active-customer',
  templateUrl: './modal-non-active-customer.component.html',
  styleUrls: ['./modal-non-active-customer.component.scss'],
})
export class ModalNonActiveCustomerComponent implements OnInit {
  @Input() customerType: string;

  reasonOptions: IGenericLabelValue[] = [];
  isOtherReason: boolean = false;
  isLoadingUpload = false;
  customerId: string = '';
  isLoading: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  responseSubmission: IResponseNonActiveCustomer;

  modalOptions: ModalOption = {
    size: 'lg',
  };

  form = this.fb.group({
    reasons: new FormArray([], [Validators.required]), // Array
    other_reason: new FormControl('', []),
    document: new FormControl('', [Validators.required]),
  });

  @ViewChild('modalNonActive') modalNonActive: ModalFormComponent;
  modalConfig: ModalConfig = {
    modalTitle: 'Non Active Customer',
    showFooter: true,
  };

  @ViewChild('modalNonActiveConfirm') modalNonActiveConfirm: ModalComponent;
  modalConfigConfirm: ModalConfig = {
    modalTitle: 'Non Active Customer',
    showFooter: true,
    closeButtonLabel: 'Batal',
    dismissButtonLabel: 'Lanjutkan',
    onDismiss: () => this.onSubmitConfirmation(),
  };

  @ViewChild('modalResponse') private modalResponse: ModalComponent;
  modalResponseConfig: ModalConfig = {
    closeButtonLabel: 'Oke',
    dismissButtonLabel: 'Oke',
    hideCloseButton: () => true,
    onDismiss: () => this.goToList(),
  };

  get ReasonForm() {
    return this.form.get('reasons') as FormArray;
  }

  get OtherReasonForm() {
    return this.form.get('other_reason') as FormControl;
  }

  get DocumentForm() {
    return <FormControl>this.form.get('document');
  }

  get ImpactedDistributor() {
    return this.distributorService.DistributorImpact;
  }

  get IsDistributor() {
    return this.customerType === 'distributor';
  }

  get CustomerTitle() {
    return this.IsDistributor ? 'Distributor' : 'Retailer';
  }

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    public utilities: UtilitiesService,
    private baseService: BaseService,
    private distributorService: DistributorService,
    private retailerService: RetailerService,
    private ref: ChangeDetectorRef
  ) {
    this.customerId = this.route.snapshot.params['id'];
  }

  ngOnInit() {
    this.isLoading.next(true);
    this.initModalNonActiveCustomer();
    this.initReasonCustomer();
    this.selectedConfirm();
  }

  initModalNonActiveCustomer() {
    if (this.customerType === 'retailer') {
      this.modalConfig.modalTitle = 'NONAKTIFKAN RETAILER';
      this.modalConfigConfirm.modalTitle = 'NONAKTIFKAN RETAILER';
    } else {
      this.modalConfig.modalTitle = 'NONAKTIFKAN DISTRIBUTOR';
      this.modalConfigConfirm.modalTitle = 'NONAKTIFKAN DISTRIBUTOR';
    }
  }

  initReasonCustomer() {
    const _api = this.customerType === 'retailer' ? API.RETAILER.GET_REASON_NON_ACTIVE_RETAILER : API.DISTRIBUTOR.GET_REASON_NON_ACTIVE_DISTRIBUTOR;
    this.baseService.getData<IGenericLabelValue[]>(_api).subscribe((resp) => {
      if (resp && resp.data) {
        this.reasonOptions = resp.data;
        this.reasonOptions.forEach((val) => {
          this.addReason(val);
        });
        if (this.customerType === 'retailer') this.isLoading.next(false);
      }
    });
  }

  selectedConfirm() {
    this.distributorService.distributorImpactSubject.subscribe((value) => {
      if (Object.keys(value).length === 0) return;
      setTimeout(() => {
        this.isLoading.next(false);
      }, 150);
    });
  }

  addReason(val: IGenericLabelValue) {
    const control = this.fb.group({
      value: val.value,
      label: val.label,
      checked: false,
    });
    this.ReasonForm.push(control);
  }

  onReasonChange(option: any) {
    if (option.value === 'OTHER_REASON') {
      this.isOtherReason = !option.checked;
      this.OtherReasonForm.setValue('');
      if (!option.checked) {
        this.OtherReasonForm.setValidators(Validators.required);
      } else {
        this.OtherReasonForm.clearValidators();
      }
      this.OtherReasonForm.updateValueAndValidity();
    }
  }

  handleDocument = (e: IGenericNameUrl) => this.DocumentForm.patchValue(e);

  handleUploadState = (e: any) => (this.isLoadingUpload = e);

  validateForm() {
    const reason = this.ReasonForm.value.filter((val: any) => val.checked).length > 0;
    return !(reason && this.form.valid) || this.isLoadingUpload;
  }

  isNoImpacted() {
    const { total_receivable, have_compensation_program, list_sales_order } = this.ImpactedDistributor;
    return (!total_receivable || total_receivable === 0) && !have_compensation_program && !list_sales_order;
  }

  open() {
    if (this.customerType === 'retailer') {
      this.modalNonActive.open().then();
    } else if (this.IsDistributor && this.isNoImpacted()) {
      this.modalNonActive.open().then();
    } else {
      this.modalNonActiveConfirm.open().then();
    }
  }

  handleCancel() {
    return this.modalNonActive.dismiss();
  }

  onSubmitConfirmation() {
    this.modalNonActive.open().then();
    return true;
  }

  async onSubmitForm() {
    this.modalNonActive.close().then();
    const { reasons, other_reason, document } = this.form.value;
    const payload = {
      disable_enum: reasons?.filter((val: any) => val.checked).map((val: any) => val.value),
      other_reason,
      document,
    };

    this.modalResponse.open().then();
    const _api = this.customerType === 'retailer' ? API.RETAILER.POST_NON_ACTIVE_RETAILER : API.DISTRIBUTOR.POST_NON_ACTIVE_DISTRIBUTOR;
    this.baseService.putData<IResponseNonActiveCustomer>(_api + this.customerId, payload).subscribe((resp) => {
      if (resp && resp.success) {
        this.responseSubmission = resp.data;
        this.ref.detectChanges();
      }
    });
  }

  async goToList() {
    if (this.customerType === 'distributor') {
      return this.router.navigate(['/distributor/teregistrasi/active']);
    } else {
      return this.router.navigate(['/retailer/terverifikasi/active']);
    }
  }

  generateRetailerNonActive(): string[] {
    const baseMessages = [
      'Retailer tidak lagi memiliki akses ke Maxxi Reward',
      'Poin & Level Retailer tidak berubah',
      'Retailer tetap mendapatkan hadiah berdasarkan capaian Program Marketing',
    ];

    const isMac = this.retailerService.RetailerDetail.is_mac;
    return isMac ? baseMessages : [...baseMessages, 'Status MAC dicabut'];
  }

  textConfirm() {
    const value = this.ImpactedDistributor;
    const hasCompensation = value.have_compensation_program;
    const hasReceivable = Number(value.total_receivable) > 0;
    const hasActiveSO = Array.isArray(value.list_sales_order) && value.list_sales_order.length > 0;

    if (hasCompensation && hasReceivable && hasActiveSO) {
      return `total piutang sebesar ${this.utilities.toRupiah(value.total_receivable, false)}, Sales Order Aktif, dan Program Marketing Kompensasi.`;
    }

    if (hasActiveSO) {
      if (hasReceivable) {
        return `Sales Order Aktif dan total piutang sebesar ${this.utilities.toRupiah(value.total_receivable, false)}.`;
      }
      if (hasCompensation) {
        return 'Sales Order Aktif dan Program Marketing Kompensasi.';
      }
      return 'Sales Order Aktif.';
    }

    if (hasReceivable) {
      if (hasCompensation) {
        return `total piutang sebesar ${this.utilities.toRupiah(value.total_receivable, false)} dan Program Marketing Kompensasi.`;
      }
      return `total piutang sebesar ${this.utilities.toRupiah(value.total_receivable, false)}.`;
    }

    if (hasCompensation) {
      return 'Program Marketing Kompensasi.';
    }

    return '';
  }

  protected readonly STRING_CONSTANTS = STRING_CONSTANTS;
}
