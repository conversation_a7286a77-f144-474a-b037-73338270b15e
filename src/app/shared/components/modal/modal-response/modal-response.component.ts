import {Component, Input, OnInit, ViewChild} from '@angular/core';
import {STRING_CONSTANTS} from "@config/constants/string.constants";
import {BehaviorSubject} from "rxjs";
import {ModalComponent} from "@shared/components/modal/modal.component";
import {IModalResponse, ModalConfig} from "@shared/components/modal/modal.interface";
import {Router} from "@angular/router";

@Component({
  selector: 'app-modal-response',
  templateUrl: './modal-response.component.html',
  styleUrls: ['./modal-response.component.scss']
})
export class ModalResponseComponent implements OnInit {
  @Input() data: IModalResponse;
  @Input() isLoading: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  @Input() callbackSubmit: () => Promise<void> | void;

  @ViewChild('modalResponse') modalResponse: ModalComponent;
  modalResponseConfig: ModalConfig = {
    hideCloseButton: () => true,
    dismissButtonLabel: 'Oke',
    disableDismissButton: () => this.isLoading.value,
  };

  ASSETS_ICON = STRING_CONSTANTS.ICON;

  constructor(
    private router: Router
  ) {
  }

  ngOnInit() {
  }

  open() {
    return this.modalResponse.open();
  }

  close() {
    return this.modalResponse.close();
  }

  protected readonly Object = Object;
}
