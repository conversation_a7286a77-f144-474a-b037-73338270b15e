<app-modal #modalResponse [callbackSubmit]="callbackSubmit" [modalConfig]="modalResponseConfig">
  <div class="d-flex flex-column justify-content-center align-items-center mb-n6">
    <ng-container *ngIf="isLoading | async; else responseTpl">
      <div class="mt-8">
        <mat-spinner></mat-spinner>
      </div>
    </ng-container>
    <ng-template #responseTpl>
      <span [inlineSVG]="data.icon"></span>
      <p class="my-4">{{ data.text }}</p>
      <app-note-view
        *ngIf="data.noteView"
        [classNoteView]="'text-left'"
        [color]="'info'"
        [icon]="ASSETS_ICON.IC_INFORMATION"
        [text]="data.noteView && data.noteView.text"
      ></app-note-view>
    </ng-template>
  </div>
</app-modal>
