import { Component, Input, OnInit, ViewChild } from '@angular/core';
import { ModalConfig } from '@shared/components/modal/modal.interface';
import { ModalComponent } from '@shared/components/modal/modal.component';

@Component({
  selector: 'app-modal-static-cancel',
  styleUrls: ['./modal-static-cancel.component.scss'],
  template: `
    <app-modal #modalCancel [modalConfig]="config">
      <div class="text-center" [innerHTML]="text"></div>
    </app-modal>
  `,
})
export class ModalStaticCancelComponent implements OnInit {
  @Input() config!: ModalConfig;
  @Input() text!: string;

  @ViewChild('modalCancel') modalCancel: ModalComponent;
  defaultConfig = <ModalConfig>{
    closeButtonLabel: 'Batal',
    dismissButtonLabel: 'Lanjutkan',
  };

  ngOnInit() {
    this.initConfig();
  }

  initConfig() {
    this.config = {
      ...this.defaultConfig,
      ...this.config,
    };
  }

  openDialog = () => this.modalCancel.open();
}
