<app-modal #modal [modalConfig]="modalConfig" [modalOptions]="modalOptions">
  <app-card *ngIf="isLoading | async; else dataTpl" [cardClasses]="'mb-8 animation animation-fade-in'">
    <ng-container cardBody>
      <app-section-loader />
    </ng-container>
  </app-card>
  <ng-template #dataTpl>
    <div class="d-flex w-100 justify-content-between flex-column" *ngIf="detailMpr | async as data">
      <div cardBody>
        <div>
          <h2 class="fw-bold mb-4">Progress {{ getDetailModal().name }}</h2>
          <ng-container *ngIf="getDetailModal().completion_times">
            <app-note-view [color]="'info'" [icon]="STRING_CONSTANTS.ICON.IC_INFORMATION" [extraContent]="true">
              <ng-container *ngIf="isRetailer; else Monitoring">
                <span>Program Marketing diselesaikan {{ getDetailModal().completion_times }}x.</span>
              </ng-container>
              <ng-template #Monitoring>
                <span>Retailer telah menyelesaikan Program Marketing {{ getDetailModal().completion_times }}x.</span>
              </ng-template>
            </app-note-view>
          </ng-container>
          <div class="scroll-content mt-4 mb-3 h-100">
            <ng-container *ngFor="let item of getDetailModal().progress_scan">
              <div class="col w-100 mt-7">
                <ng-container *ngIf="isMultilevel; else singleString">
                  <span class="fw-bold font-16 me-3">{{ item.string_level }}</span>
                  <span *ngIf="item.is_level_complete" class="svg-icon2 "
                        [inlineSVG]="STRING_CONSTANTS.ICON.IC_BULLET_TICK_GREEN"></span>
                </ng-container>
                <ng-template #singleString>
                  <span class="fw-bold font-16 me-3">Progress Scan</span>
                </ng-template>

                <ng-container>
                  <div class="mt-3">
                    <div class="row w-100 d-flex">
                      <div class="col-5">
                        <span class="text-gray-700">Target Program</span>
                      </div>
                      <div class="col-7">
                        <span>: {{ item.target_program }}</span>
                        <div class="text-gray-700"
                             style="font-style: italic">{{ item.total_scan }}
                        </div>
                      </div>
                    </div>
                  </div>
                </ng-container>
                <ng-container *ngIf="!!item.achievements">
                  <ng-container *ngFor="let scan of item.achievements">
                    <div class="mt-3">
                      <div class="row w-100 d-flex">
                        <div class="col-5">
                          <span class="text-gray-700">{{ scan.variant_name }}</span>
                        </div>
                        <div class="col-7">
                          <span>: {{ scan.value }}/{{ scan.target }}</span>
                          <div class="text-gray-700"
                               style="font-style: italic">{{ scan.qr_quantity }} QR
                          </div>
                        </div>
                      </div>
                    </div>
                  </ng-container>
                </ng-container>

              </div>
            </ng-container>

            <ng-container *ngIf="downloaded | async">
            </ng-container>

            <div *ngIf="getDetailModal().progress_scan?.length"
                 class="cursor-pointer loading-button flex flex-row mt-7">

                <ng-container *ngIf="!downloaded?.value; else loadDownload">
                  <span class="svg-icon2 me-4" [inlineSVG]="STRING_CONSTANTS.ICON.IC_DOWNLOAD_CLOUD"></span>
                  <span (click)="handleDownload()" class="fw-bold" style="color: #688238;">Unduh Progress Scan QR</span>
                </ng-container>

                <ng-template #loadDownload>
                  <mat-spinner class="loading-spinner me-4"></mat-spinner>
                  <span class="fw-bold" style="color: #688238;">Mengunduh...</span>
                </ng-template>
            </div>

            <ng-container *ngIf="!hasReward() && getDetailModal().progress_scan?.length">
              <hr class="my-8 border-gray-300" />
            </ng-container>

            <ng-container *ngFor="let reward of getDetailModal().reward">
              <div class="col w-100 mt-7">
                <div>
                  <span class="fw-bold font-16 me-3">Hadiah {{ reward.string_level }}</span>
                </div>
                <div class="mt-3">
                  <div class="row w-100 d-flex mb-2">
                    <div class="col-5">
                      <span class="text-gray-700">QTY Hadiah</span>
                    </div>
                    <div class="col-7">
                      <span>: {{ reward.qty_hadiah }}</span>
                    </div>
                  </div>
                  <div class="row w-100 d-flex mb-2">
                    <div class="col-5">
                      <span class="text-gray-700">Hadiah</span>
                    </div>
                    <div class="col-7">
                      <span>: {{ reward.reward_type_enum === EnumRewardType.NON_MAI ? (reward.non_mai_product.product_other_reward) : (reward.mai_product.variant_name + ' (' + reward.mai_product.qty + ' ' + reward.mai_product.sale_unit + ') ') }}</span>
                    </div>
                  </div>
                  <div class="row w-100 d-flex mb-2">
                    <div class="col-5">
                      <span class="text-gray-700">Maksimal Budget</span>
                    </div>
                    <div class="col-7">
                      <span>: {{ reward.reward_type_enum === EnumRewardType.MAI ? utils.toRupiah(reward.mai_product.maximum_budget) : utils.toRupiah(reward.non_mai_product.maximum_budget) }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </ng-container>

          </div>
        </div>
      </div>
    </div>
  </ng-template>

</app-modal>
