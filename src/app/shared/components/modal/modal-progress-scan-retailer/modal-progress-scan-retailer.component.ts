import { Component, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { ModalConfig } from '@shared/components/modal/modal.interface';
import { NgbModalOptions, NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { STRING_CONSTANTS } from '@config/constants/string.constants';
import { ModalComponent } from '@shared/components/modal/modal.component';
import {
  IModalProgressScanMpr,
  IProgressScan
} from '../../../../pages/sales-marketing/program-marketing-retailer/program-marketing-retailer.interface';
import { BehaviorSubject } from 'rxjs';
import {
  EnumRewardType
} from '../../../../pages/sales-marketing/program-marketing-retailer/program-marketing-retailer.enum';
import { UtilitiesService } from '@services/utilities.service';

@Component({
  selector: 'app-modal-progress-scan-retailer',
  templateUrl: './modal-progress-scan-retailer.component.html',
  styleUrls: ['./modal-progress-scan-retailer.component.scss']
})
export class ModalProgressScanRetailerComponent implements OnInit{
  private modalRef: NgbModalRef;

  @Input() public modalConfig: ModalConfig;
  @Input() public modalOptions: NgbModalOptions;
  @Input() public detailMpr?: BehaviorSubject<IModalProgressScanMpr> = new BehaviorSubject<IModalProgressScanMpr>({} as IModalProgressScanMpr);
  @Input() public isMultilevel?: boolean;
  @Input() public isRetailer: boolean;
  @Input() public downloaded?: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(true);
  @Output() download = new EventEmitter<void>();

  isLoading: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(true);

  ASSETS_ICON = STRING_CONSTANTS.ICON;
  withAction: boolean = false;

  defaultOptions: NgbModalOptions = {
    size: 'md',
  };
  defaultConfig: ModalConfig = {
    showHeader: false,
    showFooter: false,
    onClose: () => {
      setTimeout(() => {
        this.detailMpr?.next({} as IModalProgressScanMpr);
      }, 1000);
      return true;
    },
  };
  @ViewChild('modal') private modalDetail: ModalComponent;

  constructor(
    public utils : UtilitiesService
  ) {}

  ngOnInit(): void {
    this.modalOptions = typeof this.modalOptions === 'undefined' ? this.defaultOptions : { ...this.modalOptions, ...this.defaultOptions };
    this.modalConfig = {
      ...this.defaultConfig,
      ...this.modalConfig,
    };

    this.detailMpr?.subscribe((data) => {
      const hasDetail = !!data?.detail && Object.keys(data.detail).length > 0;
      this.isLoading.next(!hasDetail);
    });
  }

  hasReward(): boolean {
    const reward = this.detailMpr?.value?.detail?.reward;
    return !(reward && reward.length > 0);
  }

  getDetailModal() {
    if (!this.detailMpr?.value.detail) return {} as IProgressScan;
    return this.detailMpr.value.detail;
  }

  handleDownload() {
    this.download.emit();
  }
  openModal = () => {
    return this.modalDetail.open();
  };

  closeModal = () => {
    return this.modalDetail.close();
  };


  async dismiss(): Promise<void> {
    if (this.modalConfig.shouldDismiss === undefined || (await this.modalConfig.shouldDismiss())) {
      const result = this.modalConfig.onDismiss === undefined || (await this.modalConfig.onDismiss());
      this.modalRef.dismiss(result);
    }
  }


  protected readonly STRING_CONSTANTS = STRING_CONSTANTS;
  protected readonly EnumRewardType = EnumRewardType;
}
