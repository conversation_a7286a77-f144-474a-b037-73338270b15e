//
// To make future updates easier consider overriding the global variables from _variables.bootstrap.scss and _variables.custom.scss for current demo in this file.
// Note that this file is included first and variables defined in _variables.bootstrap.scss and _variables.custom.scss
// are not accessible in this file but you can override any global variable as shown below:
//

// Bootstrap color system
$white: #ffffff !default;
$black: #000000 !default;

// Theme colors
// Primary
$primary: #688238;
$primary-active: lighten(#688238, 5%);
$primary-light: #f8f8f8;
$primary-light-dark: #212e48;
$primary-inverse: $white;

// Success
$success: #50cd89;
$success-active: #47be7d;
$success-light: #e8fff3;
$success-light-dark: #1c3238;
$success-inverse: $white;

// Info
$info: #5186CF;
$info-active: #5014d0;
$info-light: #f8f5ff;
$info-light-dark: #2f264f;
$info-inverse: $white;
$info-text: #5EC8F2;

// Danger
//$danger:       									    #f1416c;
$danger: #CC4435;
$danger-active: #d9214e;
$danger-light: #fff5f8;
$danger-light-dark: #3a2434;
$danger-inverse: $white;

// Warning
$warning: #ffc700;
$warning-active: #f1bc00;
$warning-light: #fff8dd;
$warning-light-dark: #392f28;
$warning-inverse: $white;

// Bootstrap grey colors
$gray-000: #f0f0f0 !default;
$gray-100: #f8f8f8 !default;
$gray-200: #eff2f5 !default;
$gray-300: #d1d1d1 !default;
$gray-400: #B5B5C3 !default;
$gray-500: #A1A5B7 !default;
$gray-600: #7E8299 !default;
$gray-700: #808080 !default;
$gray-800: #3F4254 !default;
$gray-900: #353535 !default;

$gray-100-dark: #1b1b29 !default;
$gray-200-dark: #2B2B40 !default;
$gray-300-dark: #323248 !default;
$gray-400-dark: #474761 !default;
$gray-500-dark: #565674 !default;
$gray-600-dark: #6D6D80 !default;
$gray-700-dark: #92929F !default;
$gray-800-dark: #CDCDDE !default;
$gray-900-dark: #FFFFFF !default;

// Bootstrap muted color
$text-muted: $gray-500 !default;
$text-muted-dark: $gray-500-dark !default;


// Body
//
// Settings for the `<body>` element.
$body-bg: $white !default;
$body-bg-rgb: to-rgb($body-bg) !default;
$body-bg-dark: #1e1e2d !default;
$body-bg-rgb-dark: to-rgb($body-bg-dark) !default;
$body-color: $gray-900 !default;
$body-color-dark: $gray-900-dark !default;
$body-text-align: null !default;

// Components
//
// Define common padding and border radius sizes and more.
$border-width: 1px !default;
$border-color: $gray-200 !default;
$border-color-dark: $gray-200-dark !default;
$border-dashed-color: $gray-300 !default;
$border-dashed-color-dark: $gray-300-dark !default;

$border-widths: (
  0: 0,
  1: 1px,
  2: 2px,
  3: 3px,
  4: 4px,
  5: 5px
) !default;

// Border Radiues
$border-radius: .475rem !default;
$border-radius-sm: .425rem !default;
$border-radius-lg: .625rem !default;
$border-radius-xl: 1rem !default;
$border-radius-2xl: 2rem !default;
$border-radius-pill: 50rem !default;

// Typography
//
// Font, line-height, and color for body text, headings, and more.
// Font family
$font-family-inter: "Inter" !default;
$font-family-campton: 'camptonbook' !default;

$font-size-base: 1rem !default; // Assumes the browser default, typically `13px`
$font-size-lg: $font-size-base * 1.076923076923077 !default; // 14.04px
$font-size-sm: $font-size-base * .925 !default; // 12.025px

$font-weight-lighter: lighter !default;
$font-weight-light: 300 !default;
$font-weight-normal: 400 !default;
$font-weight-semibold: 500 !default;
$font-weight-bold: 600 !default;
$font-weight-bolder: 700 !default;

// Forms
$input-placeholder-color: #d1d1d1 !default;

// Input groups
$input-group-addon-bg: $gray-000 !default;
$input-group-addon-color: $gray-700 !default;

// Solid input style
$input-solid-bg: $gray-100 !default;
$input-solid-bg-dark: $gray-100-dark !default;
$input-solid-bg-focus: darken($gray-100, 2%) !default;

// Pagination
$pagination-item-height: 2.25rem !default;
$pagination-item-bg: $body-bg !default;
$pagination-item-bg-dark: $body-bg-dark !default;
$pagination-item-space: 0.5rem !default;
$pagination-item-space-tablet-and-mobile: 0.25rem !default;
$pagination-font-weight: $font-weight-normal !default;
$pagination-font-size: $font-size-sm !default;
$pagination-icon-font-size: 0.85rem !default;
$pagination-icon-height: $pagination-item-height * 0.35 !default;

// Card
$card-bg: $body-bg !default;
$card-box-shadow: 0px 0px 20px 0px rgba(76, 87, 125, 0.02) !default;
$card-border-color: $border-color !default;
$card-border-style: solid !default;
$card-border-dashed-color: $border-dashed-color !default;
$card-color: null !default;
$card-cap-bg: transparent !default;
$card-py: 2rem !default;
$card-px: 1.75rem !default;
$card-border-radius: $border-radius-lg !default;
$card-header-py: 0.5rem !default;
$card-header-height: 70px !default;
$card-border-enabled: false !default;


// height and width sizes
$custom-sizes: (
  unset: unset,
  20: 20%,
  25: 25%,
  33: calc(100%/3),
  50: 50%,
  60: 60%,
  75: 75%,
  80: 80%,
  100: 100%,
  auto: auto,
  1px: 1px,
  2px: 2px,
  3px: 3px,
  4px: 4px,
  5px: 5px,
  6px: 6px,
  7px: 7px,
  8px: 8px,
  9px: 9px,
  10px: 10px,
  15px: 15px,
  20px: 20px,
  25px: 25px,
  30px: 30px,
  35px: 35px,
  40px: 40px,
  45px: 45px,
  50px: 50px,
  55px: 55px,
  60px: 60px,
  65px: 65px,
  70px: 70px,
  75px: 75px,
  80px: 80px,
  85px: 85px,
  90px: 90px,
  95px: 95px,
  100px: 100px,
  125px: 125px,
  150px: 150px,
  175px: 175px,
  200px: 200px,
  225px: 225px,
  250px: 250px,
  275px: 275px,
  300px: 300px,
  325px: 325px,
  350px: 350px,
  375px: 375px,
  400px: 400px,
  425px: 425px,
  450px: 450px,
  475px: 475px,
  500px: 500px,
  550px: 550px,
  600px: 600px,
  650px: 650px,
  700px: 700px,
  750px: 750px,
  800px: 800px,
  850px: 850px,
  900px: 900px,
  950px: 950px,
  1000px: 1000px
) !default;
