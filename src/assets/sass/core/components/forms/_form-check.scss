//
// Check/radio
//

.form-check-input {
	background-color: var(--kt-form-check-input-bg);
	border: var(--kt-form-check-input-border);

	&:active {
		filter: var(--kt-form-check-input-active-filter);
	}

	&:focus {
		border-color: var(--kt-form-check-input-focus-border);
		box-shadow: var(--kt-form-check-input-focus-box-shadow);
	}

	&:checked {
		background-color: var(--kt-form-check-input-checked-bg-color);
		border-color: var(--kt-form-check-input-checked-border-color);

		&[type="checkbox"] {
			@if $enable-gradients {
				background-image: var(--kt-form-check-input-checked-bg-image), var(--#{$prefix}gradient);
			} @else {
				background-image: var(--kt-form-check-input-checked-bg-image);
			}
		}

		&[type="radio"] {
			@if $enable-gradients {
				background-image: var(--kt-form-check-radio-checked-bg-image), var(--#{$prefix}gradient);
			} @else {
				background-image: var(--kt-form-check-radio-checked-bg-image);
			}
		}
	}

	&[type="checkbox"]:indeterminate {
		background-color: var(--kt-form-check-input-indeterminate-bg-color);
		border-color: var(--kt-form-check-input-indeterminate-border-color);

		@if $enable-gradients {
			background-image: var(--kt-form-check-input-indeterminate-bg-image), var(--#{$prefix}gradient);
		} @else {
			background-image:var(--kt-form-check-input-indeterminate-bg-image);
		}
	}

	&:disabled {
		opacity: var(--kt-form-check-input-disabled-opacity);
	}

	// Use disabled attribute in addition of :disabled pseudo-class
	// See: https://github.com/twbs/bootstrap/issues/28247
	&[disabled],
	&:disabled {
		~ .form-check-label {
			opacity: var(--kt-form-check-label-disabled-opacity);
		}
	}
}

.form-check-label {
	color: var(--kt-form-check-label-color);
}

.form-check-input {
	&:checked {
		& + span,
		& + label {
			color: var(--kt-form-check-label-color-checked);
		}
	}
}

.form-check {
	.mat-mdc-radio-button .mdc-form-field label {
		cursor: pointer;
	}

	.mat-mdc-radio-button .mdc-radio {
		&__outer-circle { border-width: 1px!important; }

		&:hover .mdc-radio__native-control:enabled:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle,
		&__native-control:enabled:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle {
			border-color: var(--kt-gray-700)!important;
		}

		&:hover .mdc-radio__native-control:enabled+.mdc-radio__background .mdc-radio__inner-circle,
		&__native-control:enabled+.mdc-radio__background .mdc-radio__inner-circle {
			border-color: #A5CE46!important;
		}

		&:hover .mdc-radio__native-control:enabled:checked+.mdc-radio__background .mdc-radio__outer-circle,
		&__native-control:enabled:checked+.mdc-radio__background .mdc-radio__outer-circle {
			border-color: #A5CE46!important;
		}
	}
}


//
// Switch
//

.form-switch {
	.form-check-input {
		background-image: var(--kt-form-switch-bg-image);

		&:focus {
			background-image: var(--kt-form-switch-focus-bg-image);
		}

		&:checked {
			@if $enable-gradients {
				background-image: var(--kt-form-switch-checked-bg-image), var(--#{$prefix}gradient);
			} @else {
				background-image: var(--kt-form-switch-checked-bg-image);
			}
		}
	}
}

.btn-check {
	&[disabled],
	&:disabled {
		+ .btn {
			opacity: var(--kt-form-check-btn-check-disabled-opacity);
		}
	}
}

.mat-radio-button{
  transition: .3s;
  .mat-radio-outer-circle{
    border-color: #808080;
    border-width: 1px;
  }
  &.mat-radio-checked{
    .mat-radio-outer-circle{
      border-width: 1px;
      border-color: #A5CE46;
    }
    .mat-radio-inner-circle{
      background-color: #A5CE46;
    }
  }
}
