//
// Form Control
//


// Form control
.form-control {
	// Dropdown shown state
	.dropdown.show > & {
		color: var(--kt-input-focus-color);
		background-color: var(--kt-input-focus-bg);
  	    border-color: var(--kt-input-focus-border-color);
	}

	// Readonly state
	&[readonly] {
		background-color: var(--kt-input-readonly-bg);
	}

	// Solid style
	&.form-control-solid {
		background-color: var(--kt-input-solid-bg);
		border-color: var(--kt-input-solid-bg);
		color: var(--kt-input-solid-color);
		@include placeholder(var(--kt-input-solid-placeholder-color));
		transition: $transition-input;

		.dropdown.show > &,
		&:active,
		&.active,
		&:focus,
		&.focus {
			background-color: var(--kt-input-solid-bg-focus);
			border-color: var(--kt-input-solid-bg-focus);
			color: var(--kt-input-solid-color);
			transition: $transition-input;
		}

      &:disabled,
      &[readonly] {
        background-color: #ebebeb;
      }

      &.is-invalid {
        border-color: var(--bs-danger)
      }
	}

	// Transparent style
	&.form-control-transparent {
		background-color: transparent;
		border-color: transparent;

		.dropdown.show > &,
		&:active,
		&.active,
		&:focus,
		&.focus {
			background-color: transparent;
			border-color: transparent;
		}
	}

	// Flush
	&.form-control-flush {
		@include input-reset();
	}
}

// Placeholder colors
.placeholder-gray-500 {
	@include placeholder(var(--kt-gray-500));
}

.placeholder-white {
	@include placeholder($white);
}

// Textarea reset resize
.resize-none {
	resize: none;
}

// Form control solid bg
.form-control-solid-bg {
	background-color: var(--kt-input-solid-bg);
}

// Mat form field in form-control
.form-control.form-control-datepicker {

  &.disabled {
    background-color: #ebebeb;
    pointer-events: none;
  }

  .mat-form-field {
    padding: 0.2rem 0;

    &-wrapper,
    &-infix,
    &-label-wrapper {
      padding: 0;
    }

    &-wrapper {
      width: 100%;
    }

    &-infix {
      display: flex;
      align-items: center;
      border: 0;
    }

    &-underline {
      display: none;
    }

    &-label-wrapper {
      top: 0;
      bottom: 0;
      margin: auto;
    }

    &-label-wrapper .mat-form-field-label {
      font-size: 12px;
      color: #d1d1d1;
      top: 0;
      bottom: 0;
    }

    .mat-datepicker-toggle {
      position: absolute;
      top: 0;
      bottom: 0;
      right: 0;
      display: flex;
      align-items: center;
    }

    .mat-datepicker-toggle .mat-icon {
      height: auto;

      .svg-icon {
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
}
