.mat-mdc-tab-group {

  > * {
    font-family: 'Inter', sans-serif;
  }

  .mat-mdc-tab-header {
    border-bottom: 1px solid rgba(0, 0, 0, 0.12);
  }

  .mat-mdc-tab[role="tab"] {
    height: 56px;
    color: #353535;
    opacity: 1;
  }

  &.square,
  &.line {
    .mat-mdc-tab-header {
      margin-bottom: 25px;
    }
  }

  &.square .mat-mdc-tab-header {
    border-bottom: 0;
    border-bottom-left-radius: 4px;
  }

  &.square .mat-mdc-tab-header .mat-mdc-tab-labels {
    background-color: white;
    border: 1px solid #D1D1D1;
    border-radius: 4px;
    overflow: hidden;

    .mdc-tab {
      line-height: 18px;
      letter-spacing: 0.01em;
    }

    .mdc-tab--active {
      background: #EEF5DF;
    }
  }

  &.line .mat-mdc-tab-header .mat-mdc-tab-labels {
    .mdc-tab {
      line-height: 24px;
      letter-spacing: 0.01em;
    }
  }


  &.line .mdc-tab-indicator {
    &__content--underline {
      max-width: calc(100% - 48px);
    }
  }

  .mat-mdc-tab-body-wrapper {
    min-height: 75vh;
  }
}
