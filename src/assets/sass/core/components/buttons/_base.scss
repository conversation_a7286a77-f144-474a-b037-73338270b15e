//
// Buttons Base
//

// Button
.btn {

  --#{$prefix}btn-color: var(--kt-body-color);
  --#{$prefix}btn-bg: transparent;
  --#{$prefix}btn-border-color: transparent;

  font-size: $font-size-base;
  letter-spacing: 0.015em;

  &.mdc-button { border-radius: 8px; }
  &.mdc-button:not(.mat-mdc-icon-button) {
    height: auto;
    min-height: 40px;
  }

  // Reset outline
  outline: none !important;

  // Reset focus shadow
  &:not(.btn-shadow):not(.shadow):not(.shadow-sm):not(.shadow-lg):not(.shadow-xs) {
    box-shadow: none !important;
  }

  &:not(.btn-outline):disabled {
    background-color: var(--kt-secondary);
  }

  &.btn-outline-disabled:disabled {
    filter: grayscale(1);
  }

  // Remove border
  &:not(.btn-outline):not(.btn-dashed):not(.border-hover):not(.border-active):not(.btn-flush):not(.btn-icon) {
    border: 0;
    //padding: calc(#{$btn-padding-y} + #{$btn-border-width}) calc(#{$btn-padding-x} + #{$btn-border-width});

    &.btn-lg {
      padding: calc(#{$btn-padding-y-lg} + #{$btn-border-width}) calc(#{$btn-padding-x-lg} + #{$btn-border-width});
    }

    &.btn-sm {
      padding: calc(#{$btn-padding-y-sm} + #{$btn-border-width}) calc(#{$btn-padding-x-sm} + #{$btn-border-width});
    }
  }

  // Link
  &.btn-link {
    border: 0;
    border-radius: 0;
    padding-left: 0 !important;
    padding-right: 0 !important;
    text-decoration: none;
    font-weight: $btn-font-weight;
  }

  // Outline
  &.btn-outline:not(.btn-outline-dashed) {
    border: 1px solid var(--kt-input-border-color);
  }

  // Outline dashed
  &.btn-outline-dashed {
    border: 1px dashed var(--kt-input-border-color);
  }

  // Flush
  &.btn-flush {
    @include button-reset();
  }

  // Flex
  &.btn-flex {
    display: inline-flex;
    align-items: center;
  }

  // Align start
  &.btn-trim-start {
    justify-content: flex-start !important;
    padding-left: 0 !important;
  }

  // Align start
  &.btn-trim-end {
    justify-content: flex-end !important;
    padding-right: 0 !important;
  }
}

// Icons
.btn {
  // Font icon
  i {
    display: inline-flex;
    font-size: $font-size-base;
    padding-right: 0.35rem;
    vertical-align: middle;
    line-height: 0;
  }

  // Svg icon
  .svg-icon {
    flex-shrink: 0;
    line-height: 0;
    margin-right: 0.5rem;
  }

  // Icon only button
  &.btn-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    height: $input-height;
    width: $input-height;

    // Remove border
    &:not(.btn-outline):not(.btn-dashed):not(.border-hover):not(.border-active):not(.btn-flush) {
      border: 0;
    }

    // Sizes
    &.btn-sm {
      height: $input-height-sm;
      width: $input-height-sm;
    }

    &.btn-lg {
      height: $input-height-lg;
      width: $input-height-lg;
    }

    &.btn-circle {
      border-radius: 50%;
    }

    i,
    .svg-icon {
      padding: 0;
      margin: 0;
      line-height: 1;
    }
  }
}

.btn {
  &.btn-action-detail {
    position: absolute;
    top: 25px;
    right: 25px;
    display: flex;
    align-items: center;
    border: 1px solid #d1d1d1 !important;
    border-radius: 8px;
    padding: 8px 24px;

    font-weight: 700;
    font-size: 14px;
    line-height: 24px;
    letter-spacing: 0.015em;
    color: #688238;
    svg {
      margin-left: 16px;
    }
  }
}

.btn:disabled {

  svg path {
    fill: #fff;
  }

  .btn-plus-minus {
    svg {
      path {
        fill: #d1d1d1;
      }
    }
  }
}

.btn-plus-minus {
  width: 34px;
  height: 34px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f7f7f7;
}

.btn-bg-none:disabled,
.btn-bg-none[disabled] {
  background-color: transparent !important;
  color: grey !important;
}

.button-group--scanner .btn-icon {
  width: 32px;
  height: 32px;
  margin-right: 8px;

  &:last-child {
    margin-right: 0;
  }

  .svg-icon {
    color: var(--kt-primary);
  }

  &.btn-scan-rollback .svg-icon {
    color: var(--kt-danger);
  }

  &.btn-scan-reset .svg-icon {
    color: var(--kt-primary);
  }

  &.disabled {
    pointer-events: none;
    background: var(--kt-gray-100);
  }

  &.disabled .svg-icon {
    color: var(--kt-gray-300);
  }
}

.btn-uploader .svg-icon svg {
  fill: #1f93c0;
}
