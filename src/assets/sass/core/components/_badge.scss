//
// Badge
//

.badge {
    --#{$prefix}badge-color: var(--kt-badge-color);

    display: inline-flex;
    align-items: center;

    // Fixed size
    &.badge-circle,
    &.badge-square {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        height: $badge-size;
        min-width: $badge-size;
        padding: 0 0.1rem;
        line-height: 0;
    }

    // Circle
    &.badge-circle {
        border-radius: 50%;
        padding: 0;
        min-width: unset;
        width: $badge-size;
    }

    // Sizes
    &.badge-sm {
        min-width: $badge-size-sm;
        font-size: $badge-font-size-sm;

        &.badge-square {
            height: $badge-size-sm;
        }

        &.badge-circle {
            width: $badge-size-sm;
            height: $badge-size-sm;
        }
    }

    &.badge-lg {
        min-width: $badge-size-lg;
        font-size: $badge-font-size-lg;

        &.badge-square {
            height: $badge-size-lg;
        }

        &.badge-circle {
            width: $badge-size-lg;
            height: $badge-size-lg;
        }
    }
}

@each $name, $value in $theme-colors {
    .badge-#{$name} {
        color: var(--kt-#{$name}-inverse);
        background-color: var(--kt-#{$name});

        &.badge-outline {
            border: 1px solid var(--kt-#{$name});
            color: var(--kt-#{$name});
            background-color: transparent;
        }
    }

    .badge-light-#{$name} {
        color: var(--kt-#{$name});
        background-color: var(--kt-#{$name}-light);
    }
}

.badge__status {
  padding: 0;
  font: {
    weight: 700;
    size: 12px;
  }

  &--active, &--REGISTERED, &--completed, &--FINISH,
  &--ACTIVE {
    color: #269C25;
  }

  &--inactive,
  &--EXPIRED,
  &--IN_COMPLETE,
  &--NONACTIVATED,
  &--FINISHED{
    color: #808080;
  }

  &--CREATED, &--DIPROSES, &--REQUEST, &--CANCELLATION_REQUEST,
  &--SUBMITTED, &--EXTEND_PERIOD {
    color: #F29F05;
  }

  &--reject, &--DITOLAK, &--REJECTED {
    color: #CC4435;
  }

  &--blue,
  &--AKTIF, &--ondelivery, &--ON_DELIVERY, &--CONFIRMED,
  &--PUBLISHED, &--EXTENDS {
    color: #1F93C0;
  }

  &--purple, &--RESERVED, &--WAITING_ERP,
  &--SCHEDULED {
    color: #6751CF;
  }

  &--onprogress {
    color: #85774F;
  }

  &[class*='--COMPLETED'],
  &[class*='--PARTIAL']{
    color: #269C25;
  }

  &[class*='--IN_PROGRESS'],
  &[class*='--READY_TO_DELIVERY'],
  &[class*='--FINISH_EDIT_DATA'],
  &[class*='--ON_PROGRESS']{
    color: #1F93C0;
  }
  &[class*='--READY_TO_PROGRESS'],
  &[class*='--PENDING_NONACTIVATED'],
  &[class*='--NEED_PROGRESS']{
    color: #6751CF;
  }

  &[class*='--CANCEL'],
  &[class*='--REGISTRATION_REJECT'],
  &[class*='--REJECT_EDIT_VERIFICATION'],
  &[class*='_REJECTED'] {
    color: #CC4435;
  }

  &[class*='--CANCELED']{
    color: #808080;
  }

  &[class*='--NEED_APPROVE'],
  &[class*='--CANCELLATION_REQUEST'],
  &[class*='--WAITING'],
  &[class*='--OUTSTANDING'],
  &[class*='--PENDING'],
  &[class*='--pending'],
  &--NEED_CHANGE_DISCOUNT,
  &--NEED_CHANGE_ORDER_BY_DISTRIBUTOR {
    color: #F29F05;
  }

  &[class*='--ACTIVE'],
  &[class*='--REGISTERED'],
  &[class*='--PENDING'],
  &[class*='_REJECTED']{
    background: transparent;
  }

  &--NEED_CHANGED {
    color: #CB4335;
  }
}

.badge-bg {
  font-weight: normal;
  padding: 4px 16px;

  &.badge__status {

    &--PENDING {
      background: #FFF8DD;
    }

    &--PUBLISHED {
      background: #E7F6FC;
    }

    &--IN_COMPLETE {
      background: #F8F8F8;
    }
  }
}
