//
// SVG Icon
//

.svg-icon {
    line-height: 1;
    color: var(--kt-text-muted);
    @include svg-icon-size(get($font-sizes, 5));

    // Theme colors
    @each $name, $color in $theme-text-colors {
        &.svg-icon-#{$name} {
            color: var(--kt-text-#{$name});
        }
    }

    // Responsive icon sizes
    @each $breakpoint in map-keys($grid-breakpoints) {
        @include media-breakpoint-up($breakpoint) {
            $infix: breakpoint-infix($breakpoint, $grid-breakpoints);

            // Sizes
            @each $name, $value in $font-sizes {
                &.svg-icon#{$infix}-#{$name} {
                    @include svg-icon-size($value, true);
                }
            }
        }
    }
  &.svg-icon-empty{
    svg{
      width: 180px;
      height: 180px;
    }
  }

  &.icon-note-view{
    svg{
      width: 24px !important;
      height: 24px !important;
    }
  }

  &.svg-icon-desc{
    svg{
      width: 50px;
      height: 50px;
    }
  }

  &.svg-icon-24 svg {
    width: 24px;
    height: 24px;
  }

  &.svg-icon-32 svg {
    width: 32px;
    height: 32px;
  }

  &.svg-icon-48 svg {
    width: 48px;
    height: 48px;
  }

  &.svg-icon-64 svg { width: 64px; height: 64px; }
}
