//
//  Tooltip
//


// Base
.tooltip {
    --#{$prefix}tooltip-color: var(--kt-tooltip-color);
    --#{$prefix}tooltip-bg: var(--kt-tooltip-bg);
    --#{$prefix}tooltip-opacity: var(--kt-tooltip-opacity);

    .tooltip-inner {
        box-shadow: var(--kt-tooltip-box-shadow);
    }

    &.tooltop-auto-width {
       	.tooltip-inner {
       		white-space: nowrap;
  			max-width: none;
        }
    }

    // Inverse
    &.tooltip-inverse {
        @include tooltip-theme(
            $bg-color: var(--kt-dark),
            $color: var(--kt-dark-inverse),
            $arrow-color: var(--kt-dark),
        );
    }
}

.mat-tooltip {
  background: #353535;
  border-radius: 8px !important;
  padding: 8px 24px !important;
  color: white;
  overflow: visible !important;
  text-align: center;
  font-weight: 400;
  font-size: 12px;
  line-height: 18px;
  &::after{
    content: ' ';
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
    border-bottom: 10px solid #353535;
  }
}
