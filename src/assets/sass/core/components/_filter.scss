.filter-body{
  position: absolute !important;
  top: 50px !important;
  right: 0 !important;
}

.btn-filter{
  display: flex;
  align-items: center;
  background-color: #F8F8F8;
  border-radius: 8px;
  padding: 12px 16px;
  color: #808080;
  &:hover{
    background-color: #F1FCD6;
    color: #688238;
  }
  &.active{
    background-color: #F1FCD6;
    color: #688238;
    border: 1px solid #688238 !important;
  }
  .filter-is-active{
    display: inherit;
    margin-left: 16px;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: #CCC118;
  }
}

.btn-reset{
  color: #688238;
  font-weight: 700;
  font-size: 12px;
  line-height: 18px;
  background: none;
  border: none;
  text-decoration: underline;
  letter-spacing: 0.01em;
}
