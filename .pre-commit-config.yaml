repos:
  # YAML validation
  - repo: https://github.com/adrienverge/yamllint.git
    rev: v1.32.0
    hooks:
      - id: yamllint
        args: [-c=.yamllint.yml]
        files: \.(yaml|yml)$

  # General file checks
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
        args: [--allow-multiple-documents]
      - id: check-added-large-files
      - id: check-merge-conflict

  # GitLab CI specific validation
  - repo: local
    hooks:
      - id: gitlab-ci-lint
        name: GitLab CI Lint
        entry: bash -c 'if command -v glab &> /dev/null; then glab ci lint; else echo "glab not installed, skipping GitLab CI validation"; fi'
        language: system
        files: \.gitlab-ci\.yml$
        pass_filenames: false
