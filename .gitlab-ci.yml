# GitLab CI/CD Main Pipeline
# Project: Atom Backoffice Data
#
# This is the main CI/CD orchestration file that includes modular pipeline configurations
# for better maintainability and team collaboration.

stages:
  - sync-dev-branches
  - sync-to-staging
  - sync-to-production

variables:
  # Global variables for all pipelines
  NODE_VERSION: "20"
  GIT_STRATEGY: clone
  GIT_DEPTH: 0  # Full history for proper branch operations

# Include modular pipeline configurations
include:
  # Branch synchronization pipeline
  - local: '.gitlab/ci/branch-sync.yml'
  # GitLab Releases pipeline
  - local: '.gitlab/ci/releases.yml'

# Future pipeline includes (uncomment when needed):
# - local: '.gitlab/ci/testing.yml'        # Unit tests, E2E tests
# - local: '.gitlab/ci/linting.yml'        # Code quality, TypeScript linting
# - local: '.gitlab/ci/security.yml'       # Security scanning, dependency audit
# - local: '.gitlab/ci/build.yml'          # Build optimization, artifacts
# - local: '.gitlab/ci/deployment.yml'     # Advanced deployment strategies

# Quick lint job (can be moved to .gitlab/ci/linting.yml later)
lint-commitlint:
  stage: sync-dev-branches
  image: node:${NODE_VERSION}
  before_script:
    - npm install
  script:
    - npx commitlint --from HEAD~1 --to HEAD --verbose
  cache:
    key: ${CI_COMMIT_REF_SLUG}
    paths:
      - .npm/
  only:
    - merge_requests
  allow_failure: true

# Project info job
project-info:
  stage: sync-dev-branches
  image: alpine:latest
  script:
    - echo "Atom Backoffice Data CI/CD Pipeline"
    - echo "Branch ${CI_COMMIT_REF_NAME}"
    - echo "Commit ${CI_COMMIT_SHORT_SHA}"
    - echo "Author ${CI_COMMIT_AUTHOR}"
    - echo "Pipeline ${CI_PIPELINE_ID}"
  only:
    - development
    - staging
    - main

# Backup job (enabled while testing include issue)
# sync-to-dev-second:
#   stage: sync-dev-branches
#   image: alpine:latest
#   before_script:
#     - apk add --no-cache git
#     - git config --global user.name "GitLab CI"
#     - git config --global user.email "<EMAIL>"
#     - git remote set-url origin https://oauth2:${CI_PUSH_TOKEN}@${CI_SERVER_HOST}/${CI_PROJECT_PATH}.git
#   script:
#     - echo "Starting sync process"
#     - git fetch origin
#     - git checkout development-second
#     - 'COMMIT_MSG="CHORE: auto-sync development into development-second [skip ci]"'
#     - git merge origin/development --no-ff -m "$COMMIT_MSG"
#     - git push origin development-second
#     - echo "Sync completed successfully"
#   only:
#     - development
#   when: on_success


